version: '3.8'

services:
  bot-engine:
    build:
      context: .
      dockerfile: apps/bot-engine/Dockerfile
      target: dependencies
    container_name: whatsite-bot-engine-dev
    ports:
      - "3000:3000"
    volumes:
      # Source code for hot reload
      - ./apps/bot-engine/src:/app/apps/bot-engine/src
      - ./packages:/app/packages
      # Persistent workspace data
      - bot-workspaces:/app/workspaces
      - bot-sessions:/app/sessions
    environment:
      - NODE_ENV=development
      - PORT=3000
      - WORKSPACE_BASE_DIR=/app/workspaces
      - SESSION_STORAGE_DIR=/app/sessions
    env_file:
      - .env
    command: pnpm --filter bot-engine dev
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    networks:
      - bot-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: whatsite-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  postgres:
    image: postgres:15-alpine
    container_name: whatsite-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=whatsite_dev
      - POSTGRES_USER=whatsite
      - POSTGRES_PASSWORD=dev_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - bot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U whatsite -d whatsite_dev"]
      interval: 10s
      timeout: 5s
      retries: 3

networks:
  bot-network:
    driver: bridge

volumes:
  bot-workspaces:
  bot-sessions:
  redis-data:
  postgres-data: