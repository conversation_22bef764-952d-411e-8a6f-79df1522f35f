# Production Fly.io configuration for whatsite-bot-engine
# Optimized for production scaling, monitoring, and performance

app = "whatsite-bot-engine"
primary_region = "sjc"
kill_signal = "SIGINT"
kill_timeout = "10s"

[build]
  dockerfile = "Dockerfile"
  ignorefile = "../../.dockerignore"

[env]
  NODE_ENV = "production"
  PORT = "3000"
  WORKSPACE_BASE_DIR = "/app/workspaces"
  SESSION_STORAGE_DIR = "/app/sessions"
  LOG_LEVEL = "info"
  METRICS_ENABLED = "true"
  HEALTH_CHECK_TIMEOUT = "5s"
  GRACEFUL_SHUTDOWN_TIMEOUT = "30s"
  MAX_CONCURRENT_SESSIONS = "10"

[experimental]
  auto_rollback = true
  cmd = []
  entrypoint = []
  exec = []
  image = ""

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 5
  processes = ["app"]
  
  # Connection pooling and performance
  [http_service.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800

  # Health checks for zero-downtime deployments
  [[http_service.checks]]
    interval = "10s"
    timeout = "5s"
    grace_period = "10s"
    method = "GET"
    path = "/health"
    protocol = "http"
    restart_limit = 3
    
    [http_service.checks.headers]
      X-Forwarded-Proto = "https"
      User-Agent = "Fly Health Check"

  # Readiness check
  [[http_service.checks]]
    interval = "30s"
    timeout = "10s"
    grace_period = "15s"
    method = "GET"
    path = "/ready"
    protocol = "http"
    restart_limit = 2
    
    [http_service.checks.headers]
      X-Forwarded-Proto = "https"

  # Deep health check
  [[http_service.checks]]
    interval = "60s"
    timeout = "15s"
    grace_period = "20s"
    method = "GET"
    path = "/metrics"
    protocol = "http"
    restart_limit = 1

# Machine configuration optimized for production
[machine]
  cpu_kind = "shared"
  cpus = 2
  memory_mb = 2048
  
  # Auto-scaling based on metrics
  [machine.auto_scaling]
    min_instances = 1
    max_instances = 5
    
    # CPU-based scaling
    [machine.auto_scaling.metrics.cpu]
      target = 70.0
      scale_up_threshold = 80.0
      scale_down_threshold = 40.0
      
    # Memory-based scaling
    [machine.auto_scaling.metrics.memory]
      target = 75.0
      scale_up_threshold = 85.0
      scale_down_threshold = 50.0
      
    # Request-based scaling
    [machine.auto_scaling.metrics.requests]
      target = 100
      scale_up_threshold = 150
      scale_down_threshold = 50

# Persistent volumes for production data
[[mounts]]
  source = "whatsite_workspaces"
  destination = "/app/workspaces"
  size_gb = 50
  
[[mounts]]
  source = "whatsite_sessions"
  destination = "/app/sessions"
  size_gb = 20

# Production deployment settings
[deploy]
  release_command = "echo 'Starting production deployment...'"
  strategy = "rolling"
  
  # Blue-green deployment configuration
  [deploy.blue_green]
    enabled = true
    
# Logging configuration for production
[logging]
  level = "info"
  format = "json"
  
  # Structured logging
  [logging.structured]
    enabled = true
    
  # Log aggregation
  [logging.destinations]
    stdout = true
    stderr = true

# Monitoring and metrics
[metrics]
  enabled = true
  
  # Prometheus metrics
  [metrics.prometheus]
    enabled = true
    path = "/metrics"
    port = 3000
    
  # Custom metrics
  [metrics.custom]
    sessions_active = "gauge"
    sessions_completed = "counter"
    sessions_failed = "counter"
    webhooks_received = "counter"
    webhooks_processed = "counter"
    response_time = "histogram"

# Security configuration
[security]
  # Rate limiting
  [security.rate_limit]
    requests_per_minute = 100
    burst_size = 50
    
  # Headers
  [security.headers]
    x_frame_options = "DENY"
    x_content_type_options = "nosniff"
    x_xss_protection = "1; mode=block"
    strict_transport_security = "max-age=31536000; includeSubDomains"
    content_security_policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# Performance optimization
[performance]
  # Connection settings
  [performance.connections]
    keep_alive_timeout = "60s"
    max_connections = 1000
    
  # Resource limits
  [performance.resources]
    max_memory_mb = 2048
    max_cpu_percent = 90
    
  # Caching
  [performance.cache]
    enabled = true
    ttl = "5m"

# Backup and recovery
[backup]
  enabled = true
  schedule = "0 2 * * *"  # Daily at 2 AM
  retention_days = 7
  
  # Backup destinations
  [backup.destinations]
    volumes = ["whatsite_workspaces", "whatsite_sessions"]