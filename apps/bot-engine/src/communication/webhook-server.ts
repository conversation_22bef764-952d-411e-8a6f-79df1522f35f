/**
 * HTTP server for webhook endpoints
 * Handles incoming webhook requests from Vercel and provides endpoints for communication
 */

import { createServer, Server } from 'http';
import { randomUUID } from 'crypto';

import express, { Request, Response, Router, NextFunction, json, urlencoded } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import type { WebhookEvent, WebhookResponse } from '@whatsite-bot/core';

import {
  apiKeyAuth,
  signatureAuth,
  validateWebhookRequest,
  securityHeaders,
  requestLogger,
  AuthConfig,
} from '../middleware/auth.js';
import { BotEngine } from '../core/bot-engine.js';

import { WebhookHandler } from './webhook-handler.js';
import { StatusCommunicator } from './status-communicator.js';

/**
 * Webhook server configuration
 */
export interface WebhookServerConfig {
  /** Server port */
  port: number;
  /** Server host */
  host: string;
  /** Base path for webhook endpoints */
  basePath: string;
  /** Enable CORS */
  enableCors: boolean;
  /** Enable request logging */
  enableLogging: boolean;
  /** Enable rate limiting */
  enableRateLimit: boolean;
  /** Rate limit configuration */
  rateLimit: {
    windowMs: number;
    max: number;
    message: string;
  };
  /** Authentication configuration */
  auth: AuthConfig;
  /** Enable health check endpoints */
  enableHealthCheck: boolean;
  /** Enable metrics endpoints */
  enableMetrics: boolean;
  /** Request timeout in milliseconds */
  requestTimeout: number;
  /** Enable request body size limit */
  bodySizeLimit: string;
}

/**
 * Webhook server events
 */
export interface WebhookServerEvents {
  'server:started': (port: number) => void;
  'server:stopped': () => void;
  'server:error': (error: Error) => void;
  'request:received': (req: Request, endpoint: string) => void;
  'request:completed': (req: Request, res: Response, duration: number) => void;
  'webhook:received': (event: WebhookEvent) => void;
  'webhook:processed': (event: WebhookEvent, response: WebhookResponse) => void;
}

/**
 * Default webhook server configuration
 */
const DEFAULT_WEBHOOK_SERVER_CONFIG: WebhookServerConfig = {
  port: parseInt(process.env.PORT || '3000'),
  host: process.env.HOST || '0.0.0.0',
  basePath: process.env.WEBHOOK_BASE_PATH || '/webhook',
  enableCors: true,
  enableLogging: true,
  enableRateLimit: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
  },
  auth: {
    apiKey: process.env.FLY_IO_API_KEY || process.env.WEBHOOK_API_KEY,
    secretKey: process.env.WEBHOOK_SECRET_KEY,
    requireSignature: false,
    enableRateLimit: true,
  },
  enableHealthCheck: true,
  enableMetrics: true,
  requestTimeout: 30000, // 30 seconds
  bodySizeLimit: '10mb',
};

/**
 * Webhook server class
 * Provides HTTP endpoints for webhook communication
 */
export class WebhookServer {
  private config: WebhookServerConfig;
  private app: express.Application;
  private server: Server;
  private botEngine: BotEngine;
  private webhookHandler: WebhookHandler;
  private statusCommunicator: StatusCommunicator;
  private isRunning: boolean = false;
  private requestCount: number = 0;
  private errorCount: number = 0;
  private startTime: Date;

  constructor(
    botEngine: BotEngine,
    webhookHandler: WebhookHandler,
    statusCommunicator: StatusCommunicator,
    config: WebhookServerConfig = DEFAULT_WEBHOOK_SERVER_CONFIG
  ) {
    this.config = { ...DEFAULT_WEBHOOK_SERVER_CONFIG, ...config };
    this.botEngine = botEngine;
    this.webhookHandler = webhookHandler;
    this.statusCommunicator = statusCommunicator;
    this.startTime = new Date();

    this.app = express();
    this.server = createServer(this.app);

    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();

    console.log('[WebhookServer] Initialized with configuration:', {
      port: this.config.port,
      host: this.config.host,
      basePath: this.config.basePath,
      enableCors: this.config.enableCors,
      enableRateLimit: this.config.enableRateLimit,
    });
  }

  /**
   * Setup middleware
   */
  private setupMiddleware(): void {
    // Security headers
    this.app.use(helmet());
    this.app.use(securityHeaders());

    // CORS
    if (this.config.enableCors) {
      this.app.use(
        cors({
          origin: '*',
          methods: ['GET', 'POST', 'OPTIONS'],
          allowedHeaders: [
            'Content-Type',
            'Authorization',
            'X-API-Key',
            'X-Webhook-Signature',
            'X-Webhook-Timestamp',
          ],
          credentials: false,
        })
      );
    }

    // Request logging
    if (this.config.enableLogging) {
      this.app.use(morgan('combined'));
      this.app.use(requestLogger());
    }

    // Rate limiting
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.max,
        message: {
          error: 'Rate limit exceeded',
          message: this.config.rateLimit.message,
        },
        standardHeaders: true,
        legacyHeaders: false,
      });

      this.app.use(limiter);
    }

    // Body parsing
    this.app.use(json({ limit: this.config.bodySizeLimit }));
    this.app.use(urlencoded({ extended: true, limit: this.config.bodySizeLimit }));

    // Request timeout
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.setTimeout(this.config.requestTimeout, () => {
        res.status(408).json({
          error: 'Request timeout',
          message: 'Request took too long to process',
        });
      });
      next();
    });

    // Request tracking
    this.app.use((_req: Request, res: Response, next: NextFunction) => {
      this.requestCount++;

      res.on('finish', () => {
        if (res.statusCode >= 400) {
          this.errorCount++;
        }
      });

      next();
    });
  }

  /**
   * Setup routes
   */
  private setupRoutes(): void {
    const router = Router();

    // Health check endpoint
    if (this.config.enableHealthCheck) {
      router.get('/health', (req, res, next) => {
        try {
          this.handleHealthCheck(req, res);
        } catch (error) {
          next(error);
        }
      });
      router.get('/ready', (req, res, next) => {
        try {
          this.handleReadinessCheck(req, res);
        } catch (error) {
          next(error);
        }
      });
    }

    // Metrics endpoint
    if (this.config.enableMetrics) {
      router.get('/metrics', (req, res, next) => {
        this.handleMetrics(req, res).catch(next);
      });
    }

    // Main webhook endpoint for receiving events from Vercel
    router.post(
      '/whatsapp',
      apiKeyAuth(this.config.auth),
      signatureAuth(this.config.auth),
      validateWebhookRequest(),
      (req, res, next) => {
        this.handleWebhookEvent(req, res).catch(next);
      }
    );

    // New endpoint for receiving forwarded webhooks from webhook-handler
    router.post('/webhook', apiKeyAuth(this.config.auth), (req, res, next) => {
      this.handleForwardedWebhook(req, res).catch(next);
    });

    // Status update endpoint for sending updates back to Vercel
    router.post('/status', apiKeyAuth(this.config.auth), (req, res, next) => {
      this.handleStatusUpdate(req, res).catch(next);
    });

    // Test endpoint for webhook validation
    router.post('/test', apiKeyAuth(this.config.auth), (req, res, next) => {
      try {
        this.handleTestWebhook(req, res);
      } catch (error) {
        next(error);
      }
    });

    // Generic webhook endpoint
    router.post(
      '/',
      apiKeyAuth(this.config.auth),
      signatureAuth(this.config.auth),
      validateWebhookRequest(),
      (req, res, next) => {
        this.handleWebhookEvent(req, res).catch(next);
      }
    );

    // Mount router
    this.app.use(this.config.basePath, router);

    // Root health check
    this.app.get('/', (_req: Request, res: Response) => {
      res.json({
        service: 'WhatsApp Bot Engine',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
      });
    });
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    // 404 handler
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'Not found',
        message: `Endpoint ${req.method} ${req.path} not found`,
        availableEndpoints: [
          `${this.config.basePath}/whatsapp`,
          `${this.config.basePath}/status`,
          `${this.config.basePath}/test`,
          `${this.config.basePath}/health`,
          `${this.config.basePath}/metrics`,
        ],
      });
    });

    // Error handler
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    this.app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
      console.error('[WebhookServer] Error:', err);

      // Don't expose internal errors in production
      const isDevelopment = process.env.NODE_ENV === 'development';

      res.status(500).json({
        error: 'Internal server error',
        message: isDevelopment ? err.message : 'Something went wrong',
        timestamp: new Date().toISOString(),
        requestId: randomUUID(),
      });
    });
  }

  /**
   * Handle webhook events
   */
  private async handleWebhookEvent(req: Request, res: Response): Promise<void> {
    try {
      const body = req.body as Record<string, unknown>;
      const event = body.event as Record<string, unknown>;

      const eventType = typeof event.type === 'string' ? event.type : 'unknown';
      console.log(`[WebhookServer] Received webhook event: ${eventType}`);

      // Process webhook event
      const response = await this.webhookHandler.handle(event as unknown as WebhookEvent);

      // Send response
      res.status(response.status === 'success' ? 200 : 500).json(response);

      console.log(`[WebhookServer] Webhook processed: ${eventType} - ${response.status}`);
    } catch (error) {
      console.error('[WebhookServer] Webhook processing error:', error);

      res.status(500).json({
        status: 'error',
        message: 'Failed to process webhook event',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle forwarded webhook from webhook-handler
   */
  private async handleForwardedWebhook(req: Request, res: Response): Promise<void> {
    try {
      const body = req.body as Record<string, unknown>;
      const originalWebhook = body.originalWebhook;
      const message = body.message as Record<string, unknown>;
      const classifiedIntent = body.classifiedIntent as Record<string, unknown>;
      const timestamp = body.timestamp;

      if (!message || !classifiedIntent) {
        res.status(400).json({
          error: 'Missing required fields',
          message: 'message and classifiedIntent are required',
        });
        return;
      }

      const intentType =
        typeof classifiedIntent.type === 'string' ? classifiedIntent.type : 'unknown';
      const messageFrom = typeof message.from === 'string' ? message.from : 'unknown';
      console.log(`[WebhookServer] Received forwarded webhook: ${intentType} - ${messageFrom}`);

      // Convert to our internal event format
      const messageId = typeof message.id === 'string' ? message.id : 'unknown';
      const timestampStr = typeof timestamp === 'string' ? timestamp : new Date().toISOString();
      const event: WebhookEvent = {
        id: `msg_${messageId}`,
        type: 'message.received',
        timestamp: new Date(timestampStr),
        source: 'whatsapp',
        version: '1.0',
        data: {
          messageId,
          from: {
            phoneNumber: messageFrom,
            name: 'Unknown', // TODO: Extract from message.contact safely
            metadata: {
              intent: classifiedIntent,
              originalWebhook,
            },
          },
          message: {
            id: messageId,
            type: 'text' as const, // TODO: Extract from message.type safely
            content: {
              type: 'text',
              text: '', // TODO: Extract from message.text safely
            },
            timestamp: new Date(), // TODO: Extract from message.timestamp safely
            metadata: {
              forwarded: true,
              forwardedAt: new Date().toISOString(),
              source: 'webhook-handler',
              intent: classifiedIntent,
              originalWebhook,
            },
          },
          context: {
            sessionId: `session_${messageFrom}`,
            intent: intentType,
            data: {
              classifiedIntent,
              originalWebhook,
            },
          },
        },
      };

      // Process the event
      const response = await this.webhookHandler.handle(event);

      // Send response
      res.status(response.status === 'success' ? 200 : 500).json(response);

      console.log(
        `[WebhookServer] Forwarded webhook processed: ${intentType} - ${response.status}`
      );
    } catch (error) {
      console.error('[WebhookServer] Forwarded webhook processing error:', error);

      res.status(500).json({
        status: 'error',
        message: 'Failed to process forwarded webhook',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(req: Request, res: Response): Promise<void> {
    try {
      const body = req.body as Record<string, unknown>;
      const sessionId = body.sessionId;
      const phoneNumber = body.phoneNumber;
      const message = body.message;
      const type = body.type || 'info';
      const data = body.data;

      if (!sessionId || !phoneNumber || !message) {
        res.status(400).json({
          error: 'Missing required fields',
          message: 'sessionId, phoneNumber, and message are required',
        });
        return;
      }

      // Send status update with proper type checking
      const sessionIdStr = typeof sessionId === 'string' ? sessionId : '';
      const phoneNumberStr = typeof phoneNumber === 'string' ? phoneNumber : '';
      const messageStr = typeof message === 'string' ? message : '';
      const typeStr =
        typeof type === 'string' && ['info', 'success', 'warning', 'error'].includes(type)
          ? (type as 'info' | 'success' | 'warning' | 'error')
          : 'info';
      const dataObj =
        typeof data === 'object' && data !== null ? (data as Record<string, unknown>) : undefined;

      console.log(`[WebhookServer] Received status update: ${messageStr}`);

      await this.statusCommunicator.sendStatusUpdate(
        sessionIdStr,
        phoneNumberStr,
        messageStr,
        typeStr,
        dataObj
      );

      res.json({
        status: 'success',
        message: 'Status update sent',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('[WebhookServer] Status update error:', error);

      res.status(500).json({
        status: 'error',
        message: 'Failed to send status update',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle test webhook
   */
  private handleTestWebhook(req: Request, res: Response): void {
    try {
      const body = req.body as Record<string, unknown>;
      const test_type = typeof body.test_type === 'string' ? body.test_type : 'ping';

      console.log(`[WebhookServer] Test webhook received: ${test_type}`);

      // Basic test response
      res.json({
        status: 'success',
        message: 'Test webhook received successfully',
        test_type,
        timestamp: new Date().toISOString(),
        server_info: {
          uptime: Date.now() - this.startTime.getTime(),
          requests_processed: this.requestCount,
          errors: this.errorCount,
        },
      });
    } catch (error) {
      console.error('[WebhookServer] Test webhook error:', error);

      res.status(500).json({
        status: 'error',
        message: 'Test webhook failed',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Handle health check
   */
  private handleHealthCheck(_req: Request, res: Response): void {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime.getTime(),
        memory: process.memoryUsage(),
        version: '1.0.0',
      };

      res.json(health);
    } catch (error) {
      console.error('[WebhookServer] Health check error:', error);

      res.status(500).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Handle readiness check
   */
  private handleReadinessCheck(_req: Request, res: Response): void {
    try {
      const readiness = {
        status: 'ready',
        timestamp: new Date().toISOString(),
        services: {
          webhook_handler: !!this.webhookHandler,
          status_communicator: !!this.statusCommunicator,
          bot_engine: !!this.botEngine,
        },
      };

      res.json(readiness);
    } catch (error) {
      console.error('[WebhookServer] Readiness check error:', error);

      res.status(500).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Handle metrics
   */
  private async handleMetrics(_req: Request, res: Response): Promise<void> {
    try {
      const metrics = {
        requests_total: this.requestCount,
        errors_total: this.errorCount,
        uptime_seconds: Math.floor((Date.now() - this.startTime.getTime()) / 1000),
        memory_usage: process.memoryUsage(),
        webhook_handler_stats: this.webhookHandler.getStatistics(),
        status_communicator_stats: this.statusCommunicator.getStatistics(),
        bot_engine_status: await this.botEngine.getStatus(),
        timestamp: new Date().toISOString(),
      };

      res.json(metrics);
    } catch (error) {
      console.error('[WebhookServer] Metrics error:', error);

      res.status(500).json({
        error: 'Failed to get metrics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Start webhook server
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(this.config.port, this.config.host, () => {
          this.isRunning = true;
          console.log(`[WebhookServer] Started on ${this.config.host}:${this.config.port}`);
          console.log(`[WebhookServer] Webhook endpoints available at: ${this.config.basePath}`);
          resolve();
        });

        this.server.on('error', (error: Error) => {
          console.error('[WebhookServer] Server error:', error);
          reject(error);
        });
      } catch (error) {
        console.error('[WebhookServer] Failed to start server:', error);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }

  /**
   * Stop webhook server
   */
  async stop(): Promise<void> {
    return new Promise(resolve => {
      if (!this.isRunning) {
        resolve();
        return;
      }

      this.server.close(() => {
        this.isRunning = false;
        console.log('[WebhookServer] Stopped');
        resolve();
      });
    });
  }

  /**
   * Get server status
   */
  getStatus(): {
    isRunning: boolean;
    port: number;
    host: string;
    uptime: number;
    requestCount: number;
    errorCount: number;
  } {
    return {
      isRunning: this.isRunning,
      port: this.config.port,
      host: this.config.host,
      uptime: Date.now() - this.startTime.getTime(),
      requestCount: this.requestCount,
      errorCount: this.errorCount,
    };
  }
}

/**
 * Create webhook server instance
 */
export function createWebhookServer(
  botEngine: BotEngine,
  webhookHandler: WebhookHandler,
  statusCommunicator: StatusCommunicator,
  config?: WebhookServerConfig
): WebhookServer {
  return new WebhookServer(botEngine, webhookHandler, statusCommunicator, config);
}
