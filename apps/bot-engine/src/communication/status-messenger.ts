/**
 * Status Messenger - WhatsApp Status Messaging Service
 * Provides real-time status updates via WhatsApp during bot operations
 */

import { EventEmitter } from 'events';

import twilio from 'twilio';
import type { User } from '@whatsite-bot/core';

import { MessageQueue } from '../messaging/message-queue.js';
import { MessageTemplates } from '../messaging/message-templates.js';
import { ProgressTracker } from '../messaging/progress-tracker.js';

export interface StatusMessageOptions {
  /** User to send message to */
  user: User;
  /** Message type */
  type: StatusMessageType;
  /** Message context data */
  data?: Record<string, unknown>;
  /** Priority level */
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  /** Whether to queue message or send immediately */
  queue?: boolean;
  /** Retry configuration */
  retry?: {
    attempts: number;
    delay: number;
  };
}

export type StatusMessageType =
  | 'operation_start'
  | 'ai_generation_start'
  | 'ai_generation_progress'
  | 'ai_generation_complete'
  | 'project_setup_start'
  | 'project_setup_complete'
  | 'github_creation_start'
  | 'github_creation_complete'
  | 'deployment_start'
  | 'deployment_progress'
  | 'deployment_complete'
  | 'operation_success'
  | 'operation_error'
  | 'operation_timeout'
  | 'progress_update'
  | 'warning'
  | 'info'
  | 'website_creation_start'
  | 'website_editing_start'
  | 'repository_import_start'
  | 'enhanced_editing_start'
  | 'enhanced_changes_generated'
  | 'enhanced_changes_applied'
  | 'scaffold_generation_start'
  | 'scaffold_files_generated'
  | 'long_operation_notice'
  | 'operation_retry'
  | 'rate_limit_warning'
  | 'operation_complete_with_stats';

export interface StatusMessageResult {
  success: boolean;
  messageId?: string;
  error?: string;
  retryCount?: number;
}

export interface StatusMessengerConfig {
  twilio: {
    client: twilio.Twilio;
    phoneNumber: string;
  };
  messageQueue: {
    maxConcurrent: number;
    retryAttempts: number;
    retryDelay: number;
  };
  rateLimit: {
    maxMessagesPerMinute: number;
    maxMessagesPerHour: number;
  };
  features: {
    enableProgressTracking: boolean;
    enableErrorNotifications: boolean;
    enableWarningNotifications: boolean;
    enableInfoNotifications: boolean;
  };
}

interface MessageTask {
  id: string;
  user: User;
  type: StatusMessageType;
  content: string;
}

export interface StatusMessengerEvents {
  'message:sent': (user: User, type: StatusMessageType, messageId: string) => void;
  'message:failed': (user: User, type: StatusMessageType, error: Error) => void;
  'message:queued': (user: User, type: StatusMessageType) => void;
  'message:retry': (user: User, type: StatusMessageType, attempt: number) => void;
  'rate_limit:exceeded': (user: User) => void;
  error: (error: Error, context?: Record<string, unknown>) => void;
}

/**
 * StatusMessenger - WhatsApp Status Messaging Service
 */
export class StatusMessenger extends EventEmitter {
  private config: StatusMessengerConfig;
  private messageQueue: MessageQueue;
  private messageTemplates: MessageTemplates;
  private progressTracker: ProgressTracker;
  private rateLimiter: Map<string, { count: number; lastReset: number }> = new Map();
  private isInitialized: boolean = false;

  constructor(config: StatusMessengerConfig) {
    super();
    this.config = config;

    // Initialize components
    this.messageQueue = new MessageQueue({
      maxConcurrent: config.messageQueue.maxConcurrent,
      retryAttempts: config.messageQueue.retryAttempts,
      retryDelay: config.messageQueue.retryDelay,
    });

    this.messageTemplates = new MessageTemplates();
    this.progressTracker = new ProgressTracker();

    // Set up event listeners
    this.setupEventListeners();

    console.log('[StatusMessenger] Initialized with configuration:', {
      maxConcurrent: config.messageQueue.maxConcurrent,
      retryAttempts: config.messageQueue.retryAttempts,
      features: config.features,
    });
  }

  /**
   * Initialize the status messenger
   */
  initialize(): void {
    try {
      this.messageQueue.initialize();
      this.isInitialized = true;

      console.log('[StatusMessenger] Initialized successfully');
    } catch (error) {
      console.error('[StatusMessenger] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Send status message
   */
  async sendStatusMessage(options: StatusMessageOptions): Promise<StatusMessageResult> {
    if (!this.isInitialized) {
      throw new Error('StatusMessenger not initialized');
    }

    try {
      const { user, type, data = {}, priority = 'medium', queue = true, retry } = options;

      console.log(`[StatusMessenger] Sending status message to ${user.phoneNumber}: ${type}`);

      // Check rate limits
      if (!this.checkRateLimit(user.phoneNumber)) {
        console.warn(`[StatusMessenger] Rate limit exceeded for ${user.phoneNumber}`);
        this.emit('rate_limit:exceeded', user);
        return { success: false, error: 'Rate limit exceeded' };
      }

      // Check if message type is enabled
      if (!this.isMessageTypeEnabled(type)) {
        console.log(`[StatusMessenger] Message type ${type} is disabled, skipping`);
        return { success: true, messageId: 'skipped' };
      }

      // Generate message content
      const messageContent = this.messageTemplates.getTemplate(type, data);

      // Create message task
      const messageTask = {
        id: this.generateMessageId(),
        user,
        type,
        content: messageContent,
        priority,
        status: 'pending' as const,
        attempts: 0,
        maxAttempts: retry?.attempts || this.config.messageQueue.retryAttempts,
        delay: retry?.delay || this.config.messageQueue.retryDelay,
        createdAt: new Date(),
        data,
      };

      if (queue) {
        // Queue message for processing
        this.messageQueue.addMessage(messageTask);
        this.emit('message:queued', user, type);

        return { success: true, messageId: messageTask.id };
      } else {
        // Send immediately
        const result = await this.sendMessage(messageTask);
        return result;
      }
    } catch (error) {
      console.error('[StatusMessenger] Failed to send status message:', error);
      this.emit('error', error as Error, {
        user: options.user,
        type: options.type,
      });
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Send operation start message
   */
  async sendOperationStart(
    user: User,
    operationType: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'operation_start',
      data: { operationType, ...data },
      priority: 'high',
    });
  }

  /**
   * Send operation success message
   */
  async sendOperationSuccess(
    user: User,
    operationType: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'operation_success',
      data: { operationType, ...data },
      priority: 'high',
    });
  }

  /**
   * Send operation error message
   */
  async sendOperationError(
    user: User,
    operationType: string,
    error: Error,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'operation_error',
      data: {
        operationType,
        error: error.message,
        userMessage: this.getUserFriendlyError(error),
        ...data,
      },
      priority: 'urgent',
    });
  }

  /**
   * Send progress update message
   */
  async sendProgressUpdate(
    user: User,
    operationType: string,
    progress: number,
    message?: string
  ): Promise<StatusMessageResult> {
    // Update progress tracker
    this.progressTracker.updateProgress(user.phoneNumber, operationType, progress, message);

    return this.sendStatusMessage({
      user,
      type: 'progress_update',
      data: { operationType, progress, message },
      priority: 'medium',
    });
  }

  /**
   * Send AI generation progress
   */
  async sendAIGenerationProgress(
    user: User,
    stage: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'ai_generation_progress',
      data: { stage, ...data },
      priority: 'medium',
    });
  }

  /**
   * Send deployment progress
   */
  async sendDeploymentProgress(
    user: User,
    stage: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'deployment_progress',
      data: { stage, ...data },
      priority: 'medium',
    });
  }

  /**
   * Send warning message
   */
  async sendWarning(
    user: User,
    message: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'warning',
      data: { message, ...data },
      priority: 'medium',
    });
  }

  /**
   * Send info message
   */
  async sendInfo(
    user: User,
    message: string,
    data?: Record<string, unknown>
  ): Promise<StatusMessageResult> {
    return this.sendStatusMessage({
      user,
      type: 'info',
      data: { message, ...data },
      priority: 'low',
    });
  }

  /**
   * Get progress for user operation
   */
  getProgress(userPhone: string, operationType: string): number {
    return this.progressTracker.getProgress(userPhone, operationType);
  }

  /**
   * Get message queue status
   */
  getQueueStatus(): Record<string, unknown> {
    return this.messageQueue.getStatus();
  }

  /**
   * Shutdown the status messenger
   */
  async shutdown(): Promise<void> {
    try {
      await this.messageQueue.shutdown();
      this.isInitialized = false;

      console.log('[StatusMessenger] Shutdown complete');
    } catch (error) {
      console.error('[StatusMessenger] Shutdown error:', error);
    }
  }

  // Private methods

  /**
   * Set up event listeners
   */
  private setupEventListeners(): void {
    // Message queue events
    this.messageQueue.on('message:processing', (task: MessageTask) => {
      console.log(`[StatusMessenger] Processing message ${task.id} for ${task.user.phoneNumber}`);
    });

    this.messageQueue.on('message:completed', (task: MessageTask) => {
      console.log(`[StatusMessenger] Message ${task.id} completed successfully`);
      this.emit('message:sent', task.user, task.type, task.id);
    });

    this.messageQueue.on('message:failed', (task: MessageTask, error: Error) => {
      console.error(`[StatusMessenger] Message ${task.id} failed:`, error);
      this.emit('message:failed', task.user, task.type, error);
    });

    this.messageQueue.on('message:retry', (task: MessageTask, attempt: number) => {
      console.log(`[StatusMessenger] Retrying message ${task.id} (attempt ${attempt})`);
      this.emit('message:retry', task.user, task.type, attempt);
    });

    this.messageQueue.on('error', (error: Error) => {
      console.error('[StatusMessenger] Message queue error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Send message via Twilio
   */
  private async sendMessage(task: MessageTask): Promise<StatusMessageResult> {
    try {
      const result = await this.config.twilio.client.messages.create({
        from: `whatsapp:${this.config.twilio.phoneNumber}`,
        to: `whatsapp:${task.user.phoneNumber}`,
        body: task.content,
      });

      // Update rate limiter
      this.updateRateLimit(task.user.phoneNumber);

      return {
        success: true,
        messageId: result.sid,
      };
    } catch (error) {
      console.error(`[StatusMessenger] Failed to send message via Twilio:`, error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Check rate limits
   */
  private checkRateLimit(phoneNumber: string): boolean {
    const now = Date.now();
    const userLimit = this.rateLimiter.get(phoneNumber);

    if (!userLimit) {
      return true;
    }

    // Reset counter every minute
    if (now - userLimit.lastReset > 60000) {
      userLimit.count = 0;
      userLimit.lastReset = now;
    }

    return userLimit.count < this.config.rateLimit.maxMessagesPerMinute;
  }

  /**
   * Update rate limiter
   */
  private updateRateLimit(phoneNumber: string): void {
    const now = Date.now();
    const userLimit = this.rateLimiter.get(phoneNumber);

    if (!userLimit) {
      this.rateLimiter.set(phoneNumber, { count: 1, lastReset: now });
    } else {
      userLimit.count++;
    }
  }

  /**
   * Check if message type is enabled
   */
  private isMessageTypeEnabled(type: StatusMessageType): boolean {
    const features = this.config.features;

    switch (type) {
      case 'operation_error':
        return features.enableErrorNotifications;
      case 'warning':
        return features.enableWarningNotifications;
      case 'info':
        return features.enableInfoNotifications;
      case 'progress_update':
      case 'ai_generation_progress':
      case 'deployment_progress':
        return features.enableProgressTracking;
      default:
        return true;
    }
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyError(error: Error): string {
    const message = error.message.toLowerCase();

    if (message.includes('timeout')) {
      return 'The operation took too long. Please try again with a simpler request.';
    }

    if (message.includes('rate limit')) {
      return 'Too many requests. Please wait a moment and try again.';
    }

    if (message.includes('permission') || message.includes('unauthorized')) {
      return 'Permission denied. Please check your repository access settings.';
    }

    if (message.includes('network') || message.includes('connection')) {
      return 'Network error. Please check your connection and try again.';
    }

    return 'An unexpected error occurred. Please try again or contact support.';
  }
}

/**
 * Create StatusMessenger instance
 */
export function createStatusMessenger(config: StatusMessengerConfig): StatusMessenger {
  return new StatusMessenger(config);
}

/**
 * Default StatusMessenger configuration
 */
export const DEFAULT_STATUS_MESSENGER_CONFIG: Partial<StatusMessengerConfig> = {
  messageQueue: {
    maxConcurrent: 5,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  rateLimit: {
    maxMessagesPerMinute: 10,
    maxMessagesPerHour: 100,
  },
  features: {
    enableProgressTracking: true,
    enableErrorNotifications: true,
    enableWarningNotifications: true,
    enableInfoNotifications: true,
  },
};
