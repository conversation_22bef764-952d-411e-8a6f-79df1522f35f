/**
 * Performance Metrics - Real-time performance metrics collection and monitoring
 * Provides detailed performance insights and analytics
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { cpuUsage, memoryUsage } from 'process';

/**
 * Metric types
 */
export type MetricType = 'counter' | 'gauge' | 'histogram' | 'timer' | 'rate';

/**
 * Metric value
 */
export interface MetricValue {
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
}

/**
 * Histogram bucket
 */
export interface HistogramBucket {
  upperBound: number;
  count: number;
}

/**
 * Histogram data
 */
export interface HistogramData {
  buckets: HistogramBucket[];
  count: number;
  sum: number;
  min: number;
  max: number;
  mean: number;
  stdDev: number;
  percentiles: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
  };
}

/**
 * Timer data
 */
export interface TimerData {
  count: number;
  sum: number;
  min: number;
  max: number;
  mean: number;
  rate: number;
  histogram: HistogramData;
}

/**
 * Rate data
 */
export interface RateData {
  rate: number;
  count: number;
  lastUpdate: number;
  window: number;
}

/**
 * Metric configuration
 */
export interface MetricConfig {
  type: MetricType;
  description: string;
  unit?: string;
  buckets?: number[]; // For histograms
  windowSize?: number; // For rates
  maxAge?: number; // Maximum age for values
  maxSamples?: number; // Maximum number of samples
}

/**
 * Performance metrics configuration
 */
export interface PerformanceMetricsConfig {
  // Collection settings
  collection: {
    enabled: boolean;
    interval: number; // milliseconds
    maxSamples: number;
    maxAge: number; // milliseconds
  };

  // Export settings
  export: {
    enabled: boolean;
    interval: number; // milliseconds
    format: 'json' | 'prometheus' | 'csv';
    destination: 'console' | 'file' | 'http';
    endpoint?: string;
    filePath?: string;
  };

  // Aggregation settings
  aggregation: {
    enabled: boolean;
    interval: number; // milliseconds
    retention: number; // milliseconds
  };

  // Alert settings
  alerts: {
    enabled: boolean;
    thresholds: Record<string, number>;
    cooldown: number; // milliseconds
  };
}

/**
 * Performance metrics events
 */
export interface PerformanceMetricsEvents {
  'metric:recorded': (name: string, value: MetricValue) => void;
  'metric:exported': (format: string, data: Record<string, unknown>) => void;
  'alert:triggered': (metric: string, value: number, threshold: number) => void;
  'collection:error': (error: Error) => void;
}

export interface MetricData {
  type: MetricType;
  description: string;
  value: unknown;
}

/**
 * Base metric class
 */
abstract class BaseMetric {
  protected values: MetricValue[] = [];
  protected config: MetricConfig;
  protected lastCleanup: number = Date.now();

  constructor(
    public readonly name: string,
    config: MetricConfig
  ) {
    this.config = config;
  }

  /**
   * Record a value
   */
  abstract record(value: number, labels?: Record<string, string>): void;

  /**
   * Get current value
   */
  abstract getValue(): unknown;

  /**
   * Get metric type
   */
  getType(): MetricType {
    return this.config.type;
  }

  /**
   * Get metric description
   */
  getDescription(): string {
    return this.config.description;
  }

  /**
   * Clean up old values
   */
  protected cleanup(): void {
    const now = Date.now();

    if (now - this.lastCleanup < 60000) {
      // Cleanup every minute
      return;
    }

    const maxAge = this.config.maxAge || 3600000; // 1 hour default
    const maxSamples = this.config.maxSamples || 10000;

    // Remove old values
    this.values = this.values.filter(v => now - v.timestamp < maxAge);

    // Limit number of samples
    if (this.values.length > maxSamples) {
      this.values = this.values.slice(-maxSamples);
    }

    this.lastCleanup = now;
  }
}

/**
 * Counter metric
 */
class CounterMetric extends BaseMetric {
  private count: number = 0;

  record(value: number, labels?: Record<string, string>): void {
    this.count += Math.max(0, value); // Counters only increase

    const metricValue: MetricValue = {
      value: this.count,
      timestamp: Date.now(),
      labels,
    };

    this.values.push(metricValue);
    this.cleanup();
  }

  getValue(): number {
    return this.count;
  }

  reset(): void {
    this.count = 0;
    this.values = [];
  }
}

/**
 * Gauge metric
 */
class GaugeMetric extends BaseMetric {
  private value: number = 0;

  record(value: number, labels?: Record<string, string>): void {
    this.value = value;

    const metricValue: MetricValue = {
      value: this.value,
      timestamp: Date.now(),
      labels,
    };

    this.values.push(metricValue);
    this.cleanup();
  }

  getValue(): number {
    return this.value;
  }

  increase(delta: number = 1): void {
    this.record(this.value + delta);
  }

  decrease(delta: number = 1): void {
    this.record(this.value - delta);
  }
}

/**
 * Histogram metric
 */
class HistogramMetric extends BaseMetric {
  private buckets: Map<number, number> = new Map();
  private count: number = 0;
  private sum: number = 0;
  private samples: number[] = [];

  constructor(name: string, config: MetricConfig) {
    super(name, config);

    // Initialize buckets
    const buckets = config.buckets || [0.1, 0.5, 1, 2.5, 5, 10, 25, 50, 100, 250, 500, 1000];
    for (const bucket of buckets) {
      this.buckets.set(bucket, 0);
    }
  }

  record(value: number, labels?: Record<string, string>): void {
    this.count++;
    this.sum += value;
    this.samples.push(value);

    // Update buckets
    for (const [upperBound, count] of this.buckets) {
      if (value <= upperBound) {
        this.buckets.set(upperBound, count + 1);
      }
    }

    const metricValue: MetricValue = {
      value,
      timestamp: Date.now(),
      labels,
    };

    this.values.push(metricValue);
    this.cleanup();
  }

  getValue(): HistogramData {
    const sortedSamples = [...this.samples].sort((a, b) => a - b);

    const bucketData: HistogramBucket[] = Array.from(this.buckets.entries()).map(
      ([upperBound, count]) => ({ upperBound, count })
    );

    const min = sortedSamples[0] || 0;
    const max = sortedSamples[sortedSamples.length - 1] || 0;
    const mean = this.count > 0 ? this.sum / this.count : 0;

    // Calculate standard deviation
    const variance =
      this.samples.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / this.count;
    const stdDev = Math.sqrt(variance);

    // Calculate percentiles
    const p50 = this.getPercentile(sortedSamples, 50);
    const p90 = this.getPercentile(sortedSamples, 90);
    const p95 = this.getPercentile(sortedSamples, 95);
    const p99 = this.getPercentile(sortedSamples, 99);

    return {
      buckets: bucketData,
      count: this.count,
      sum: this.sum,
      min,
      max,
      mean,
      stdDev,
      percentiles: { p50, p90, p95, p99 },
    };
  }

  private getPercentile(sortedSamples: number[], percentile: number): number {
    if (sortedSamples.length === 0) return 0;

    const index = Math.ceil((percentile / 100) * sortedSamples.length) - 1;
    const clampedIndex = Math.max(0, Math.min(index, sortedSamples.length - 1));
    return sortedSamples[clampedIndex] || 0;
  }
}

/**
 * Timer metric
 */
class TimerMetric extends BaseMetric {
  private histogram: HistogramMetric;
  private activeTimers: Map<string, number> = new Map();

  constructor(name: string, config: MetricConfig) {
    super(name, config);
    this.histogram = new HistogramMetric(name, config);
  }

  record(value: number, labels?: Record<string, string>): void {
    this.histogram.record(value, labels);

    const metricValue: MetricValue = {
      value,
      timestamp: Date.now(),
      labels,
    };

    this.values.push(metricValue);
    this.cleanup();
  }

  start(id: string): void {
    this.activeTimers.set(id, performance.now());
  }

  end(id: string, labels?: Record<string, string>): number {
    const startTime = this.activeTimers.get(id);
    if (!startTime) {
      return 0;
    }

    const duration = performance.now() - startTime;
    this.activeTimers.delete(id);
    this.record(duration, labels);

    return duration;
  }

  getValue(): TimerData {
    const histogramData = this.histogram.getValue();
    const now = Date.now();
    const recentValues = this.values.filter(v => now - v.timestamp < 60000); // Last minute

    return {
      count: histogramData.count,
      sum: histogramData.sum,
      min: histogramData.min,
      max: histogramData.max,
      mean: histogramData.mean,
      rate: recentValues.length, // Events per minute
      histogram: histogramData,
    };
  }
}

/**
 * Rate metric
 */
class RateMetric extends BaseMetric {
  private events: number[] = [];
  private windowSize: number;

  constructor(name: string, config: MetricConfig) {
    super(name, config);
    this.windowSize = config.windowSize || 60000; // 1 minute default
  }

  record(value: number = 1, labels?: Record<string, string>): void {
    const now = Date.now();

    // Add events
    for (let i = 0; i < value; i++) {
      this.events.push(now);
    }

    // Clean old events
    this.events = this.events.filter(timestamp => now - timestamp < this.windowSize);

    const metricValue: MetricValue = {
      value: this.getRate(),
      timestamp: now,
      labels,
    };

    this.values.push(metricValue);
    this.cleanup();
  }

  private getRate(): number {
    return (this.events.length / this.windowSize) * 1000; // Events per second
  }

  getValue(): RateData {
    return {
      rate: this.getRate(),
      count: this.events.length,
      lastUpdate: this.events[this.events.length - 1] || 0,
      window: this.windowSize,
    };
  }
}

/**
 * Performance Metrics implementation
 */
export class PerformanceMetrics extends EventEmitter {
  private config: PerformanceMetricsConfig;
  private metrics: Map<string, BaseMetric> = new Map();
  private collectionInterval?: NodeJS.Timeout;
  private exportInterval?: NodeJS.Timeout;
  private aggregationInterval?: NodeJS.Timeout;
  private isRunning: boolean = false;
  private lastAlerts: Map<string, number> = new Map();

  constructor(config: PerformanceMetricsConfig) {
    super();
    this.config = config;
    this.initializeBuiltInMetrics();
  }

  /**
   * Initialize built-in system metrics
   */
  private initializeBuiltInMetrics(): void {
    // System metrics
    this.createGauge('system.memory.heap_used', 'Heap memory used', 'bytes');
    this.createGauge('system.memory.heap_total', 'Total heap memory', 'bytes');
    this.createGauge('system.memory.rss', 'Resident set size', 'bytes');
    this.createGauge('system.cpu.user', 'User CPU time', 'microseconds');
    this.createGauge('system.cpu.system', 'System CPU time', 'microseconds');

    // HTTP metrics
    this.createCounter('http.requests.total', 'Total HTTP requests');
    this.createCounter('http.requests.errors', 'HTTP request errors');
    this.createHistogram('http.request.duration', 'HTTP request duration', 'milliseconds');

    // WhatsApp metrics
    this.createCounter('whatsapp.messages.received', 'WhatsApp messages received');
    this.createCounter('whatsapp.messages.sent', 'WhatsApp messages sent');
    this.createCounter('whatsapp.messages.failed', 'WhatsApp message failures');
    this.createTimer(
      'whatsapp.message.processing_time',
      'WhatsApp message processing time',
      'milliseconds'
    );

    // Session metrics
    this.createCounter('sessions.created', 'Sessions created');
    this.createCounter('sessions.completed', 'Sessions completed');
    this.createCounter('sessions.failed', 'Sessions failed');
    this.createGauge('sessions.active', 'Active sessions');
    this.createTimer('sessions.duration', 'Session duration', 'milliseconds');

    // Cache metrics
    this.createCounter('cache.hits', 'Cache hits');
    this.createCounter('cache.misses', 'Cache misses');
    this.createGauge('cache.size', 'Cache size');
    this.createGauge('cache.memory_usage', 'Cache memory usage', 'bytes');

    // Connection pool metrics
    this.createGauge('connection_pool.active', 'Active connections');
    this.createGauge('connection_pool.idle', 'Idle connections');
    this.createGauge('connection_pool.queue_size', 'Connection queue size');
    this.createCounter('connection_pool.created', 'Connections created');
    this.createCounter('connection_pool.destroyed', 'Connections destroyed');
  }

  /**
   * Start metrics collection
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Start collection
    if (this.config.collection.enabled) {
      this.collectionInterval = setInterval(() => {
        this.collectSystemMetrics();
      }, this.config.collection.interval);
    }

    // Start export
    if (this.config.export.enabled) {
      this.exportInterval = setInterval(() => {
        this.exportMetrics();
      }, this.config.export.interval);
    }

    // Start aggregation
    if (this.config.aggregation.enabled) {
      this.aggregationInterval = setInterval(() => {
        this.aggregateMetrics();
      }, this.config.aggregation.interval);
    }

    console.log('[PerformanceMetrics] Started metrics collection');
  }

  /**
   * Stop metrics collection
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
    }

    if (this.exportInterval) {
      clearInterval(this.exportInterval);
    }

    if (this.aggregationInterval) {
      clearInterval(this.aggregationInterval);
    }

    console.log('[PerformanceMetrics] Stopped metrics collection');
  }

  /**
   * Create a counter metric
   */
  createCounter(name: string, description: string, unit?: string): CounterMetric {
    const config: MetricConfig = {
      type: 'counter',
      description,
      unit,
      maxAge: this.config.collection.maxAge,
      maxSamples: this.config.collection.maxSamples,
    };

    const metric = new CounterMetric(name, config);
    this.metrics.set(name, metric);
    return metric;
  }

  /**
   * Create a gauge metric
   */
  createGauge(name: string, description: string, unit?: string): GaugeMetric {
    const config: MetricConfig = {
      type: 'gauge',
      description,
      unit,
      maxAge: this.config.collection.maxAge,
      maxSamples: this.config.collection.maxSamples,
    };

    const metric = new GaugeMetric(name, config);
    this.metrics.set(name, metric);
    return metric;
  }

  /**
   * Create a histogram metric
   */
  createHistogram(
    name: string,
    description: string,
    unit?: string,
    buckets?: number[]
  ): HistogramMetric {
    const config: MetricConfig = {
      type: 'histogram',
      description,
      unit,
      buckets,
      maxAge: this.config.collection.maxAge,
      maxSamples: this.config.collection.maxSamples,
    };

    const metric = new HistogramMetric(name, config);
    this.metrics.set(name, metric);
    return metric;
  }

  /**
   * Create a timer metric
   */
  createTimer(name: string, description: string, unit?: string, buckets?: number[]): TimerMetric {
    const config: MetricConfig = {
      type: 'timer',
      description,
      unit,
      buckets,
      maxAge: this.config.collection.maxAge,
      maxSamples: this.config.collection.maxSamples,
    };

    const metric = new TimerMetric(name, config);
    this.metrics.set(name, metric);
    return metric;
  }

  /**
   * Create a rate metric
   */
  createRate(name: string, description: string, windowSize?: number): RateMetric {
    const config: MetricConfig = {
      type: 'rate',
      description,
      windowSize,
      maxAge: this.config.collection.maxAge,
      maxSamples: this.config.collection.maxSamples,
    };

    const metric = new RateMetric(name, config);
    this.metrics.set(name, metric);
    return metric;
  }

  /**
   * Get metric by name
   */
  getMetric(name: string): BaseMetric | undefined {
    return this.metrics.get(name);
  }

  /**
   * Record metric value
   */
  recordMetric(name: string, value: number, labels?: Record<string, string>): void {
    const metric = this.metrics.get(name);
    if (metric) {
      metric.record(value, labels);
      this.emit('metric:recorded', name, {
        value,
        timestamp: Date.now(),
        labels,
      });

      // Check alerts
      this.checkAlerts(name, value);
    }
  }

  /**
   * Increment counter
   */
  incrementCounter(name: string, value: number = 1, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels);
  }

  /**
   * Set gauge value
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels);
  }

  /**
   * Record histogram value
   */
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels);
  }

  /**
   * Start timer
   */
  startTimer(name: string, id: string): void {
    const metric = this.metrics.get(name) as TimerMetric;
    if (metric && metric.getType() === 'timer') {
      metric.start(id);
    }
  }

  /**
   * End timer
   */
  endTimer(name: string, id: string, labels?: Record<string, string>): number {
    const metric = this.metrics.get(name) as TimerMetric;
    if (metric && metric.getType() === 'timer') {
      return metric.end(id, labels);
    }
    return 0;
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): Record<string, MetricData> {
    const result: Record<string, MetricData> = {};

    for (const [name, metric] of this.metrics) {
      result[name] = {
        type: metric.getType(),
        description: metric.getDescription(),
        value: metric.getValue(),
      };
    }

    return result;
  }

  /**
   * Get metrics summary
   */
  getMetricsSummary(): Record<string, unknown> {
    const metrics = this.getAllMetrics();

    return {
      system: {
        memory: {
          heapUsed: metrics['system.memory.heap_used']?.value || 0,
          heapTotal: metrics['system.memory.heap_total']?.value || 0,
          rss: metrics['system.memory.rss']?.value || 0,
        },
        cpu: {
          user: metrics['system.cpu.user']?.value || 0,
          system: metrics['system.cpu.system']?.value || 0,
        },
      },
      http: {
        requests: metrics['http.requests.total']?.value || 0,
        errors: metrics['http.requests.errors']?.value || 0,
        duration: metrics['http.request.duration']?.value || {},
      },
      whatsapp: {
        messagesReceived: metrics['whatsapp.messages.received']?.value || 0,
        messagesSent: metrics['whatsapp.messages.sent']?.value || 0,
        messagesFailed: metrics['whatsapp.messages.failed']?.value || 0,
        processingTime: metrics['whatsapp.message.processing_time']?.value || {},
      },
      sessions: {
        created: metrics['sessions.created']?.value || 0,
        completed: metrics['sessions.completed']?.value || 0,
        failed: metrics['sessions.failed']?.value || 0,
        active: metrics['sessions.active']?.value || 0,
        duration: metrics['sessions.duration']?.value || {},
      },
      cache: {
        hits: metrics['cache.hits']?.value || 0,
        misses: metrics['cache.misses']?.value || 0,
        size: metrics['cache.size']?.value || 0,
        memoryUsage: metrics['cache.memory_usage']?.value || 0,
      },
      connections: {
        active: metrics['connection_pool.active']?.value || 0,
        idle: metrics['connection_pool.idle']?.value || 0,
        queueSize: metrics['connection_pool.queue_size']?.value || 0,
        created: metrics['connection_pool.created']?.value || 0,
        destroyed: metrics['connection_pool.destroyed']?.value || 0,
      },
    };
  }

  /**
   * Collect system metrics
   */
  private collectSystemMetrics(): void {
    try {
      // Memory metrics
      const memStats = memoryUsage();
      this.setGauge('system.memory.heap_used', memStats.heapUsed);
      this.setGauge('system.memory.heap_total', memStats.heapTotal);
      this.setGauge('system.memory.rss', memStats.rss);

      // CPU metrics
      const cpuStats = cpuUsage();
      this.setGauge('system.cpu.user', cpuStats.user);
      this.setGauge('system.cpu.system', cpuStats.system);
    } catch (error) {
      this.emit('collection:error', error as Error);
    }
  }

  /**
   * Export metrics
   */
  private exportMetrics(): void {
    try {
      const metrics = this.getAllMetrics();

      switch (this.config.export.format) {
        case 'json':
          this.exportJSON(metrics);
          break;
        case 'prometheus':
          this.exportPrometheus(metrics);
          break;
        case 'csv':
          this.exportCSV(metrics);
          break;
      }

      this.emit('metric:exported', this.config.export.format, metrics);
    } catch (error) {
      this.emit('collection:error', error as Error);
    }
  }

  /**
   * Export metrics as JSON
   */
  private exportJSON(metrics: Record<string, unknown>): void {
    const output = JSON.stringify(metrics, null, 2);

    switch (this.config.export.destination) {
      case 'console':
        console.log('[PerformanceMetrics] JSON Export:', output);
        break;
      case 'file':
        if (this.config.export.filePath) {
          // Would write to file in production
          console.log('[PerformanceMetrics] JSON Export saved to:', this.config.export.filePath);
        }
        break;
      case 'http':
        if (this.config.export.endpoint) {
          // Would send HTTP request in production
          console.log('[PerformanceMetrics] JSON Export sent to:', this.config.export.endpoint);
        }
        break;
    }
  }

  /**
   * Export metrics in Prometheus format
   */
  private exportPrometheus(metrics: Record<string, unknown>): void {
    const lines: string[] = [];

    for (const [name, metric] of Object.entries(metrics)) {
      const metricName = name.replace(/\./g, '_');
      const metricObj = metric as Record<string, unknown>;

      const description = typeof metricObj.description === 'string' ? metricObj.description : '';
      const type = typeof metricObj.type === 'string' ? metricObj.type : 'gauge';

      lines.push(`# HELP ${metricName} ${description}`);
      lines.push(`# TYPE ${metricName} ${type}`);

      if (type === 'histogram') {
        const histData = metricObj.value as HistogramData;
        for (const bucket of histData.buckets) {
          lines.push(`${metricName}_bucket{le="${bucket.upperBound}"} ${bucket.count}`);
        }
        lines.push(`${metricName}_sum ${histData.sum}`);
        lines.push(`${metricName}_count ${histData.count}`);
      } else {
        const value =
          typeof metricObj.value === 'object' && metricObj.value !== null
            ? (metricObj.value as Record<string, unknown>).count ||
              (metricObj.value as Record<string, unknown>).rate ||
              0
            : metricObj.value;
        lines.push(`${metricName} ${String(value)}`);
      }
    }

    const output = lines.join('\n');
    console.log('[PerformanceMetrics] Prometheus Export:', output);
  }

  /**
   * Export metrics as CSV
   */
  private exportCSV(metrics: Record<string, unknown>): void {
    const lines: string[] = ['timestamp,metric,value,type'];
    const timestamp = new Date().toISOString();

    for (const [name, metric] of Object.entries(metrics)) {
      const metricObj = metric as Record<string, unknown>;
      const value =
        typeof metricObj.value === 'object' ? JSON.stringify(metricObj.value) : metricObj.value;
      const type = typeof metricObj.type === 'string' ? metricObj.type : 'gauge';
      lines.push(
        `${timestamp},${name},${typeof value === 'string' || typeof value === 'number' ? value : JSON.stringify(value)},${type}`
      );
    }

    const output = lines.join('\n');
    console.log('[PerformanceMetrics] CSV Export:', output);
  }

  /**
   * Aggregate metrics
   */
  private aggregateMetrics(): void {
    // This would typically aggregate metrics over time periods
    // For now, we'll just clean up old data
    // Cleanup is handled automatically when metrics are updated
    // No manual cleanup needed as each metric cleans itself up
  }

  /**
   * Check alerts
   */
  private checkAlerts(metricName: string, value: number): void {
    if (!this.config.alerts.enabled) {
      return;
    }

    const threshold = this.config.alerts.thresholds[metricName];
    if (threshold && value > threshold) {
      const lastAlert = this.lastAlerts.get(metricName) || 0;
      const now = Date.now();

      if (now - lastAlert > this.config.alerts.cooldown) {
        this.emit('alert:triggered', metricName, value, threshold);
        this.lastAlerts.set(metricName, now);
      }
    }
  }
}

/**
 * Create performance metrics instance
 */
export function createPerformanceMetrics(config: PerformanceMetricsConfig): PerformanceMetrics {
  return new PerformanceMetrics(config);
}

/**
 * Default performance metrics configuration
 */
export const DEFAULT_PERFORMANCE_METRICS_CONFIG: PerformanceMetricsConfig = {
  collection: {
    enabled: true,
    interval: 30000, // 30 seconds
    maxSamples: 1000,
    maxAge: 3600000, // 1 hour
  },
  export: {
    enabled: true,
    interval: 300000, // 5 minutes
    format: 'json',
    destination: 'console',
  },
  aggregation: {
    enabled: true,
    interval: 300000, // 5 minutes
    retention: 86400000, // 24 hours
  },
  alerts: {
    enabled: true,
    thresholds: {
      'system.memory.heap_used': 512 * 1024 * 1024, // 512MB
      'http.request.duration': 2000, // 2 seconds
      'sessions.active': 50, // 50 active sessions
    },
    cooldown: 300000, // 5 minutes
  },
};
