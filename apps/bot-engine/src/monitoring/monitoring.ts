/**
 * Production Monitoring Service for Fly.io Deployment
 * Provides comprehensive metrics collection, alerting, and performance monitoring
 */

import { EventEmitter } from "events";

import { BotEngine } from "../core/bot-engine.js";
import { SessionManager } from "../session/session-manager.js";
import { WebsiteManager } from "../website/website-manager.js";

export interface MetricPoint {
  name: string;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
  type: "counter" | "gauge" | "histogram" | "summary";
}

export interface AlertCondition {
  metric: string;
  operator: "gt" | "lt" | "eq" | "ne";
  threshold: number;
  duration: number; // seconds
  severity: "low" | "medium" | "high" | "critical";
}

export interface Alert {
  name: string;
  condition: AlertCondition;
  value: number;
  timestamp: string;
  severity: "low" | "medium" | "high" | "critical";
}

export interface MonitoringConfig {
  enabled: boolean;
  metricsPort: number;
  collectInterval: number;
  retentionPeriod: number;
  enablePrometheus: boolean;
  enableCustomMetrics: boolean;
  enableAlerts: boolean;
  alertWebhook?: string;
  logLevel: "debug" | "info" | "warn" | "error";
}

export interface ApplicationMetrics {
  // Request metrics
  http_requests_total: number;
  http_requests_duration_seconds: number[];
  http_requests_in_flight: number;
  http_response_size_bytes: number[];

  // Session metrics
  sessions_active: number;
  sessions_created_total: number;
  sessions_completed_total: number;
  sessions_failed_total: number;
  sessions_duration_seconds: number[];

  // Webhook metrics
  webhooks_received_total: number;
  webhooks_processed_total: number;
  webhooks_failed_total: number;
  webhooks_processing_duration_seconds: number[];

  // Website creation metrics
  websites_created_total: number;
  websites_deployed_total: number;
  websites_failed_total: number;
  website_creation_duration_seconds: number[];

  // System metrics
  process_cpu_seconds_total: number;
  process_memory_bytes: number;
  process_uptime_seconds: number;
  nodejs_heap_size_bytes: number;
  nodejs_heap_used_bytes: number;

  // Error metrics
  errors_total: number;
  error_rate: number;

  // Custom business metrics
  twilio_messages_sent_total: number;
  github_api_calls_total: number;
  vercel_deployments_total: number;
  ai_requests_total: number;
}

export class MonitoringService extends EventEmitter {
  private config: MonitoringConfig;
  private botEngine: BotEngine;
  private sessionManager: SessionManager;
  private metrics: Map<string, MetricPoint[]> = new Map();
  private alerts: Map<string, AlertCondition> = new Map();
  private intervals: NodeJS.Timeout[] = [];
  private isRunning = false;
  private startTime = Date.now();

  constructor(
    config: MonitoringConfig,
    botEngine: BotEngine,
    sessionManager: SessionManager,
    _websiteManager: WebsiteManager
  ) {
    super();
    this.config = config;
    this.botEngine = botEngine;
    this.sessionManager = sessionManager;

    this.setupDefaultAlerts();
    this.setupEventListeners();
  }

  /**
   * Start monitoring service
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    console.log("[Monitoring] Starting monitoring service...");

    this.isRunning = true;

    // Start metrics collection
    if (this.config.enabled) {
      this.startMetricsCollection();
    }

    // Start cleanup task
    this.startCleanupTask();

    console.log(`[Monitoring] Service started on port ${this.config.metricsPort}`);
  }

  /**
   * Stop monitoring service
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    console.log("[Monitoring] Stopping monitoring service...");

    this.isRunning = false;

    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];

    console.log("[Monitoring] Service stopped");
  }

  /**
   * Record a metric point
   */
  recordMetric(
    name: string,
    value: number,
    labels?: Record<string, string>,
    type: MetricPoint["type"] = "gauge"
  ): void {
    if (!this.config.enabled) {
      return;
    }

    const metric: MetricPoint = {
      name,
      value,
      timestamp: Date.now(),
      labels,
      type,
    };

    const existing = this.metrics.get(name) || [];
    existing.push(metric);

    // Keep only recent metrics
    const cutoff = Date.now() - this.config.retentionPeriod * 1000;
    const filtered = existing.filter(m => m.timestamp > cutoff);

    this.metrics.set(name, filtered);

    // Check alerts
    this.checkAlerts(name, value);

    // Emit metric event
    this.emit("metric", metric);
  }

  /**
   * Increment a counter metric
   */
  incrementCounter(name: string, value = 1, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels, "counter");
  }

  /**
   * Set a gauge metric
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels, "gauge");
  }

  /**
   * Record a histogram metric
   */
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric(name, value, labels, "histogram");
  }

  /**
   * Record request duration
   */
  recordRequestDuration(duration: number, method: string, status: number, path: string): void {
    this.recordHistogram("http_requests_duration_seconds", duration, {
      method,
      status: status.toString(),
      path: this.sanitizePath(path),
    });

    this.incrementCounter("http_requests_total", 1, {
      method,
      status: status.toString(),
      path: this.sanitizePath(path),
    });
  }

  /**
   * Record session metrics
   */
  recordSessionMetrics(action: "created" | "completed" | "failed", duration?: number): void {
    switch (action) {
      case "created":
        this.incrementCounter("sessions_created_total");
        break;
      case "completed":
        this.incrementCounter("sessions_completed_total");
        if (duration) {
          this.recordHistogram("sessions_duration_seconds", duration);
        }
        break;
      case "failed":
        this.incrementCounter("sessions_failed_total");
        break;
    }
  }

  /**
   * Record webhook metrics
   */
  recordWebhookMetrics(action: "received" | "processed" | "failed", duration?: number): void {
    switch (action) {
      case "received":
        this.incrementCounter("webhooks_received_total");
        break;
      case "processed":
        this.incrementCounter("webhooks_processed_total");
        if (duration) {
          this.recordHistogram("webhooks_processing_duration_seconds", duration);
        }
        break;
      case "failed":
        this.incrementCounter("webhooks_failed_total");
        break;
    }
  }

  /**
   * Record website creation metrics
   */
  recordWebsiteMetrics(action: "created" | "deployed" | "failed", duration?: number): void {
    switch (action) {
      case "created":
        this.incrementCounter("websites_created_total");
        if (duration) {
          this.recordHistogram("website_creation_duration_seconds", duration);
        }
        break;
      case "deployed":
        this.incrementCounter("websites_deployed_total");
        break;
      case "failed":
        this.incrementCounter("websites_failed_total");
        break;
    }
  }

  /**
   * Record error metrics
   */
  recordError(error: Error, context?: Record<string, unknown>): void {
    this.incrementCounter("errors_total", 1, {
      error_type: error.name,
      error_message: error.message.substring(0, 100),
    });

    this.emit("error", error, context);
  }

  /**
   * Get current metrics
   */
  getMetrics(): ApplicationMetrics {
    const now = Date.now();

    return {
      // Request metrics
      http_requests_total: this.getCounterValue("http_requests_total"),
      http_requests_duration_seconds: this.getHistogramValues("http_requests_duration_seconds"),
      http_requests_in_flight: this.getGaugeValue("http_requests_in_flight"),
      http_response_size_bytes: this.getHistogramValues("http_response_size_bytes"),

      // Session metrics
      sessions_active: this.getGaugeValue("sessions_active"),
      sessions_created_total: this.getCounterValue("sessions_created_total"),
      sessions_completed_total: this.getCounterValue("sessions_completed_total"),
      sessions_failed_total: this.getCounterValue("sessions_failed_total"),
      sessions_duration_seconds: this.getHistogramValues("sessions_duration_seconds"),

      // Webhook metrics
      webhooks_received_total: this.getCounterValue("webhooks_received_total"),
      webhooks_processed_total: this.getCounterValue("webhooks_processed_total"),
      webhooks_failed_total: this.getCounterValue("webhooks_failed_total"),
      webhooks_processing_duration_seconds: this.getHistogramValues(
        "webhooks_processing_duration_seconds"
      ),

      // Website creation metrics
      websites_created_total: this.getCounterValue("websites_created_total"),
      websites_deployed_total: this.getCounterValue("websites_deployed_total"),
      websites_failed_total: this.getCounterValue("websites_failed_total"),
      website_creation_duration_seconds: this.getHistogramValues(
        "website_creation_duration_seconds"
      ),

      // System metrics
      process_cpu_seconds_total: this.getCpuUsage(),
      process_memory_bytes: this.getMemoryUsage(),
      process_uptime_seconds: (now - this.startTime) / 1000,
      nodejs_heap_size_bytes: this.getHeapSize(),
      nodejs_heap_used_bytes: this.getHeapUsed(),

      // Error metrics
      errors_total: this.getCounterValue("errors_total"),
      error_rate: this.getErrorRate(),

      // Custom business metrics
      twilio_messages_sent_total: this.getCounterValue("twilio_messages_sent_total"),
      github_api_calls_total: this.getCounterValue("github_api_calls_total"),
      vercel_deployments_total: this.getCounterValue("vercel_deployments_total"),
      ai_requests_total: this.getCounterValue("ai_requests_total"),
    };
  }

  /**
   * Get Prometheus-formatted metrics
   */
  getPrometheusMetrics(): string {
    const metrics = this.getMetrics();
    const lines: string[] = [];

    // Add help and type information
    lines.push("# HELP http_requests_total Total number of HTTP requests");
    lines.push("# TYPE http_requests_total counter");
    lines.push(`http_requests_total ${metrics.http_requests_total}`);

    lines.push("# HELP sessions_active Number of active sessions");
    lines.push("# TYPE sessions_active gauge");
    lines.push(`sessions_active ${metrics.sessions_active}`);

    lines.push("# HELP sessions_created_total Total number of sessions created");
    lines.push("# TYPE sessions_created_total counter");
    lines.push(`sessions_created_total ${metrics.sessions_created_total}`);

    lines.push("# HELP process_memory_bytes Process memory usage in bytes");
    lines.push("# TYPE process_memory_bytes gauge");
    lines.push(`process_memory_bytes ${metrics.process_memory_bytes}`);

    lines.push("# HELP process_uptime_seconds Process uptime in seconds");
    lines.push("# TYPE process_uptime_seconds gauge");
    lines.push(`process_uptime_seconds ${metrics.process_uptime_seconds}`);

    lines.push("# HELP errors_total Total number of errors");
    lines.push("# TYPE errors_total counter");
    lines.push(`errors_total ${metrics.errors_total}`);

    return lines.join("\n");
  }

  /**
   * Add alert condition
   */
  addAlert(name: string, condition: AlertCondition): void {
    this.alerts.set(name, condition);
  }

  /**
   * Remove alert condition
   */
  removeAlert(name: string): void {
    this.alerts.delete(name);
  }

  // Private methods

  private setupDefaultAlerts(): void {
    if (!this.config.enableAlerts) {
      return;
    }

    // High error rate alert
    this.addAlert("high_error_rate", {
      metric: "error_rate",
      operator: "gt",
      threshold: 0.05, // 5% error rate
      duration: 300, // 5 minutes
      severity: "high",
    });

    // High memory usage alert
    this.addAlert("high_memory_usage", {
      metric: "process_memory_bytes",
      operator: "gt",
      threshold: 1.5 * 1024 * 1024 * 1024, // 1.5GB
      duration: 300,
      severity: "medium",
    });

    // Session failure rate alert
    this.addAlert("high_session_failure_rate", {
      metric: "sessions_failed_total",
      operator: "gt",
      threshold: 10, // 10 failed sessions
      duration: 600, // 10 minutes
      severity: "high",
    });

    // Webhook processing delays
    this.addAlert("slow_webhook_processing", {
      metric: "webhooks_processing_duration_seconds",
      operator: "gt",
      threshold: 30, // 30 seconds
      duration: 180, // 3 minutes
      severity: "medium",
    });
  }

  private setupEventListeners(): void {
    // Listen to bot engine events
    this.botEngine.on("session:started", () => {
      this.recordSessionMetrics("created");
    });

    this.botEngine.on("session:completed", () => {
      this.recordSessionMetrics("completed");
    });

    this.botEngine.on("session:failed", () => {
      this.recordSessionMetrics("failed");
    });

    this.botEngine.on("error", error => {
      this.recordError(error);
    });

    // Listen to webhook events
    this.botEngine.on("webhook:received", () => {
      this.recordWebhookMetrics("received");
    });
  }

  private startMetricsCollection(): void {
    const interval = setInterval(() => {
      this.collectSystemMetrics();
      this.collectApplicationMetrics();
    }, this.config.collectInterval);

    this.intervals.push(interval);
  }

  private collectSystemMetrics(): void {
    try {
      // Memory metrics
      const memoryUsage = process.memoryUsage();
      this.setGauge("process_memory_bytes", memoryUsage.rss);
      this.setGauge("nodejs_heap_size_bytes", memoryUsage.heapTotal);
      this.setGauge("nodejs_heap_used_bytes", memoryUsage.heapUsed);

      // CPU metrics
      const cpuUsage = process.cpuUsage();
      this.setGauge("process_cpu_seconds_total", (cpuUsage.user + cpuUsage.system) / 1000000);

      // Uptime
      this.setGauge("process_uptime_seconds", process.uptime());
    } catch (error) {
      console.error("[Monitoring] Error collecting system metrics:", error);
    }
  }

  private async collectApplicationMetrics(): Promise<void> {
    try {
      // Session metrics
      const sessionStats = await this.sessionManager.getSessionStats();
      this.setGauge("sessions_active", sessionStats.active);

      // Bot engine status
      const botStatus = await this.botEngine.getStatus();
      this.setGauge("bot_engine_running", botStatus.isRunning ? 1 : 0);
    } catch (error) {
      console.error("[Monitoring] Error collecting application metrics:", error);
    }
  }

  private startCleanupTask(): void {
    const interval = setInterval(() => {
      this.cleanupOldMetrics();
    }, 60000); // Clean up every minute

    this.intervals.push(interval);
  }

  private cleanupOldMetrics(): void {
    const cutoff = Date.now() - this.config.retentionPeriod * 1000;

    for (const [name, metrics] of this.metrics) {
      const filtered = metrics.filter(m => m.timestamp > cutoff);
      if (filtered.length !== metrics.length) {
        this.metrics.set(name, filtered);
      }
    }
  }

  private checkAlerts(metricName: string, value: number): void {
    if (!this.config.enableAlerts) {
      return;
    }

    for (const [alertName, condition] of this.alerts) {
      if (condition.metric !== metricName) {
        continue;
      }

      const shouldAlert = this.evaluateCondition(condition, value);
      if (shouldAlert) {
        this.triggerAlert(alertName, condition, value);
      }
    }
  }

  private evaluateCondition(condition: AlertCondition, value: number): boolean {
    switch (condition.operator) {
      case "gt":
        return value > condition.threshold;
      case "lt":
        return value < condition.threshold;
      case "eq":
        return value === condition.threshold;
      case "ne":
        return value !== condition.threshold;
      default:
        return false;
    }
  }

  private triggerAlert(alertName: string, condition: AlertCondition, value: number): void {
    const alert = {
      name: alertName,
      condition,
      value,
      timestamp: new Date().toISOString(),
      severity: condition.severity,
    };

    console.warn(`[Monitoring] Alert triggered: ${alertName}`, alert);

    this.emit("alert", alert);

    // Send webhook if configured
    if (this.config.alertWebhook) {
      this.sendAlertWebhook(alert).catch(error => {
        console.error("[Monitoring] Failed to send alert webhook:", error);
      });
    }
  }

  private async sendAlertWebhook(alert: Alert): Promise<void> {
    try {
      if (!this.config.alertWebhook) {
        return;
      }

      const response = await fetch(this.config.alertWebhook, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(alert),
      });

      if (!response.ok) {
        console.error(`[Monitoring] Failed to send alert webhook: ${response.status}`);
      }
    } catch (error) {
      console.error("[Monitoring] Error sending alert webhook:", error);
    }
  }

  private getCounterValue(name: string): number {
    const metrics = this.metrics.get(name) || [];
    return metrics.reduce((sum, m) => sum + m.value, 0);
  }

  private getGaugeValue(name: string): number {
    const metrics = this.metrics.get(name) || [];
    const lastMetric = metrics[metrics.length - 1];
    return metrics.length > 0 && lastMetric ? lastMetric.value : 0;
  }

  private getHistogramValues(name: string): number[] {
    const metrics = this.metrics.get(name) || [];
    return metrics.map(m => m.value);
  }

  private getCpuUsage(): number {
    const cpuUsage = process.cpuUsage();
    return (cpuUsage.user + cpuUsage.system) / 1000000;
  }

  private getMemoryUsage(): number {
    return process.memoryUsage().rss;
  }

  private getHeapSize(): number {
    return process.memoryUsage().heapTotal;
  }

  private getHeapUsed(): number {
    return process.memoryUsage().heapUsed;
  }

  private getErrorRate(): number {
    const totalRequests = this.getCounterValue("http_requests_total");
    const totalErrors = this.getCounterValue("errors_total");
    return totalRequests > 0 ? totalErrors / totalRequests : 0;
  }

  private sanitizePath(path: string): string {
    // Remove IDs and sensitive data from paths
    return path
      .replace(/\/[0-9a-f-]{36}/g, "/:id")
      .replace(/\/\d+/g, "/:id")
      .replace(/\/[^\/]+@[^\/]+/g, "/:email");
  }
}

export function createMonitoringService(
  config: MonitoringConfig,
  botEngine: BotEngine,
  sessionManager: SessionManager,
  websiteManager: WebsiteManager
): MonitoringService {
  return new MonitoringService(config, botEngine, sessionManager, websiteManager);
}

export const DEFAULT_MONITORING_CONFIG: MonitoringConfig = {
  enabled: true,
  metricsPort: 9090,
  collectInterval: 15000, // 15 seconds
  retentionPeriod: 3600, // 1 hour
  enablePrometheus: true,
  enableCustomMetrics: true,
  enableAlerts: true,
  logLevel: "info",
};
