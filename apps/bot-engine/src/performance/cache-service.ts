/**
 * Cache Service - Provides caching layer for frequently accessed data
 * Implements in-memory caching with <PERSON>TL, LRU eviction, and cache warming
 */

import { EventEmitter } from "events";

/**
 * Cache configuration
 */
export interface CacheConfig {
  // Memory settings
  maxSize: number; // Maximum number of items
  maxMemoryMB: number; // Maximum memory usage in MB

  // TTL settings
  defaultTTL: number; // Default TTL in milliseconds
  maxTTL: number; // Maximum TTL in milliseconds

  // Eviction settings
  evictionPolicy: "lru" | "lfu" | "fifo";
  evictionBatchSize: number;

  // Performance settings
  compressionEnabled: boolean;
  serializationEnabled: boolean;

  // Cache warming settings
  warmupEnabled: boolean;
  warmupKeys: string[];

  // Cleanup settings
  cleanupInterval: number;
  expiredCleanupBatch: number;
}

/**
 * Cache entry
 */
interface CacheEntry<T = unknown> {
  key: string;
  value: T;
  size: number;
  createdAt: number;
  lastAccessed: number;
  accessCount: number;
  ttl: number;
  expiresAt: number;
  compressed?: boolean;
  serialized?: boolean;
}

/**
 * Cache statistics
 */
export interface CacheStats {
  // Hit/Miss stats
  hits: number;
  misses: number;
  hitRate: number;
  missRate: number;

  // Size stats
  size: number;
  maxSize: number;
  memoryUsage: number;
  maxMemoryMB: number;

  // Performance stats
  averageGetTime: number;
  averageSetTime: number;

  // Eviction stats
  evictions: number;
  evictionsByPolicy: number;
  evictionsByTTL: number;
  evictionsByMemory: number;

  // Access patterns
  mostAccessed: Array<{ key: string; count: number }>;
  recentlyAccessed: Array<{ key: string; time: number }>;
}

/**
 * Cache events
 */
export interface CacheEvents {
  "cache:hit": (key: string, value: unknown) => void;
  "cache:miss": (key: string) => void;
  "cache:set": (key: string, size: number) => void;
  "cache:evicted": (key: string, reason: string) => void;
  "cache:expired": (key: string) => void;
  "cache:cleared": () => void;
  "cache:warmed": (keys: string[]) => void;
  "memory:warning": (usage: number, limit: number) => void;
  "memory:critical": (usage: number, limit: number) => void;
}

/**
 * Cache namespace for organizing cache keys
 */
export class CacheNamespace {
  constructor(
    private cache: CacheService,
    private namespace: string
  ) {}

  private namespacedKey(key: string): string {
    return `${this.namespace}:${key}`;
  }

  async get<T>(key: string): Promise<T | null> {
    return this.cache.get<T>(this.namespacedKey(key));
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    return this.cache.set(this.namespacedKey(key), value, ttl);
  }

  async has(key: string): Promise<boolean> {
    return this.cache.has(this.namespacedKey(key));
  }

  async delete(key: string): Promise<boolean> {
    return this.cache.delete(this.namespacedKey(key));
  }

  async clear(): Promise<void> {
    return this.cache.clearNamespace(this.namespace);
  }

  async mget<T>(keys: string[]): Promise<Array<T | null>> {
    const namespacedKeys = keys.map(key => this.namespacedKey(key));
    return this.cache.mget<T>(namespacedKeys);
  }

  async mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    const namespacedEntries = entries.map(entry => ({
      ...entry,
      key: this.namespacedKey(entry.key),
    }));
    return this.cache.mset(namespacedEntries);
  }
}

/**
 * Cache Service implementation
 */
export class CacheService extends EventEmitter {
  private config: CacheConfig;
  private cache: Map<string, CacheEntry> = new Map();
  private accessOrder: string[] = []; // For LRU
  private cleanupInterval?: NodeJS.Timeout;
  private stats: CacheStats;

  constructor(config: CacheConfig) {
    super();
    this.config = config;
    this.stats = this.initializeStats();
    this.startCleanupInterval();

    if (config.warmupEnabled) {
      this.warmupCache();
    }
  }

  /**
   * Initialize cache statistics
   */
  private initializeStats(): CacheStats {
    return {
      hits: 0,
      misses: 0,
      hitRate: 0,
      missRate: 0,
      size: 0,
      maxSize: this.config.maxSize,
      memoryUsage: 0,
      maxMemoryMB: this.config.maxMemoryMB,
      averageGetTime: 0,
      averageSetTime: 0,
      evictions: 0,
      evictionsByPolicy: 0,
      evictionsByTTL: 0,
      evictionsByMemory: 0,
      mostAccessed: [],
      recentlyAccessed: [],
    };
  }

  /**
   * Get value from cache
   */
  get<T>(key: string): T | null {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.stats.misses++;
        this.emit("cache:miss", key);
        return null;
      }

      // Check if expired
      if (Date.now() > entry.expiresAt) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
        this.stats.misses++;
        this.emit("cache:expired", key);
        return null;
      }

      // Update access statistics
      entry.lastAccessed = Date.now();
      entry.accessCount++;
      this.updateAccessOrder(key);

      this.stats.hits++;
      this.emit("cache:hit", key, entry.value);

      return this.deserializeValue<T>(entry);
    } finally {
      const duration = performance.now() - startTime;
      this.updateAverageGetTime(duration);
      this.updateHitRate();
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const startTime = performance.now();

    try {
      const effectiveTTL = Math.min(ttl || this.config.defaultTTL, this.config.maxTTL);
      const serializedValue = this.serializeValue(value);
      const compressedValue = this.compressValue(serializedValue);
      const size = this.calculateSize(compressedValue);

      // Check memory limits
      if (this.getMemoryUsage() + size > this.config.maxMemoryMB * 1024 * 1024) {
        this.evictByMemory(size);
      }

      // Check size limits
      if (this.cache.size >= this.config.maxSize) {
        this.evictByPolicy();
      }

      const entry: CacheEntry<T> = {
        key,
        value: compressedValue,
        size,
        createdAt: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        ttl: effectiveTTL,
        expiresAt: Date.now() + effectiveTTL,
        compressed: this.config.compressionEnabled,
        serialized: this.config.serializationEnabled,
      };

      // Remove existing entry if it exists
      if (this.cache.has(key)) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
      }

      this.cache.set(key, entry);
      this.accessOrder.push(key);

      this.stats.size = this.cache.size;
      this.stats.memoryUsage = this.getMemoryUsage();

      this.emit("cache:set", key, size);
    } finally {
      const duration = performance.now() - startTime;
      this.updateAverageSetTime(duration);
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);

    if (!entry) {
      return false;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      return false;
    }

    return true;
  }

  /**
   * Delete key from cache
   */
  delete(key: string): boolean {
    const existed = this.cache.delete(key);

    if (existed) {
      this.removeFromAccessOrder(key);
      this.stats.size = this.cache.size;
      this.stats.memoryUsage = this.getMemoryUsage();
    }

    return existed;
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.stats.size = 0;
    this.stats.memoryUsage = 0;
    this.emit("cache:cleared");
  }

  /**
   * Clear namespace
   */
  clearNamespace(namespace: string): void {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (key.startsWith(`${namespace}:`)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.delete(key);
    }
  }

  /**
   * Get multiple values
   */
  async mget<T>(keys: string[]): Promise<Array<T | null>> {
    const results: Array<T | null> = [];

    for (const key of keys) {
      const value = await this.get<T>(key);
      results.push(value);
    }

    return results;
  }

  /**
   * Set multiple values
   */
  async mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl);
    }
  }

  /**
   * Create namespace
   */
  namespace(name: string): CacheNamespace {
    return new CacheNamespace(this, name);
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateAccessPatterns();
    return { ...this.stats };
  }

  /**
   * Get cache health
   */
  getHealth(): Record<string, unknown> {
    const stats = this.getStats();
    const memoryUsagePercent = (stats.memoryUsage / (stats.maxMemoryMB * 1024 * 1024)) * 100;
    const sizeUsagePercent = (stats.size / stats.maxSize) * 100;

    return {
      healthy: memoryUsagePercent < 90 && sizeUsagePercent < 90,
      memoryUsage: memoryUsagePercent,
      sizeUsage: sizeUsagePercent,
      hitRate: stats.hitRate,
      status:
        memoryUsagePercent > 95 || sizeUsagePercent > 95
          ? "critical"
          : memoryUsagePercent > 80 || sizeUsagePercent > 80
            ? "warning"
            : "healthy",
    };
  }

  /**
   * Warm up cache with predefined keys
   */
  private warmupCache(): void {
    const warmedKeys: string[] = [];

    for (const key of this.config.warmupKeys) {
      // This would typically load from persistent storage or external service
      // For now, we'll just mark these keys as warmed
      warmedKeys.push(key);
    }

    if (warmedKeys.length > 0) {
      this.emit("cache:warmed", warmedKeys);
    }
  }

  /**
   * Evict entries by memory pressure
   */
  private evictByMemory(requiredSize: number): void {
    const maxMemory = this.config.maxMemoryMB * 1024 * 1024;
    const targetMemory = maxMemory - requiredSize;

    let evicted = 0;

    // Evict expired entries first
    this.evictExpired();

    // Evict by policy if still over limit
    while (this.getMemoryUsage() > targetMemory && this.cache.size > 0) {
      const evictedKey = this.evictByPolicy();
      if (evictedKey) {
        evicted++;
        this.stats.evictionsByMemory++;
      } else {
        break;
      }
    }

    if (this.getMemoryUsage() > maxMemory * 0.9) {
      this.emit("memory:warning", this.getMemoryUsage(), maxMemory);
    }

    if (this.getMemoryUsage() > maxMemory * 0.95) {
      this.emit("memory:critical", this.getMemoryUsage(), maxMemory);
    }
  }

  /**
   * Evict entries by policy
   */
  private evictByPolicy(): string | null {
    switch (this.config.evictionPolicy) {
      case "lru":
        return this.evictLRU();
      case "lfu":
        return this.evictLFU();
      case "fifo":
        return this.evictFIFO();
      default:
        return this.evictLRU();
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): string | null {
    if (this.accessOrder.length === 0) {
      return null;
    }

    const keyToEvict = this.accessOrder.shift();
    if (!keyToEvict) {
      return null;
    }

    this.cache.delete(keyToEvict);

    this.stats.evictions++;
    this.stats.evictionsByPolicy++;
    this.emit("cache:evicted", keyToEvict, "lru");

    return keyToEvict;
  }

  /**
   * Evict least frequently used entry
   */
  private evictLFU(): string | null {
    let leastUsedKey: string | null = null;
    let leastUsedCount = Number.MAX_SAFE_INTEGER;

    for (const [key, entry] of this.cache) {
      if (entry.accessCount < leastUsedCount) {
        leastUsedCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.removeFromAccessOrder(leastUsedKey);

      this.stats.evictions++;
      this.stats.evictionsByPolicy++;
      this.emit("cache:evicted", leastUsedKey, "lfu");
    }

    return leastUsedKey;
  }

  /**
   * Evict first in, first out
   */
  private evictFIFO(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Number.MAX_SAFE_INTEGER;

    for (const [key, entry] of this.cache) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.removeFromAccessOrder(oldestKey);

      this.stats.evictions++;
      this.stats.evictionsByPolicy++;
      this.emit("cache:evicted", oldestKey, "fifo");
    }

    return oldestKey;
  }

  /**
   * Evict expired entries
   */
  private evictExpired(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.stats.evictionsByTTL++;
      this.emit("cache:expired", key);
    }
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.evictExpired();
    }, this.config.cleanupInterval);
  }

  /**
   * Update access order for LRU
   */
  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  /**
   * Remove from access order
   */
  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * Calculate memory usage
   */
  private getMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Calculate size of value
   */
  private calculateSize(value: unknown): number {
    if (typeof value === "string") {
      return Buffer.byteLength(value, "utf8");
    } else if (Buffer.isBuffer(value)) {
      return value.length;
    } else {
      return Buffer.byteLength(JSON.stringify(value), "utf8");
    }
  }

  /**
   * Serialize value
   */
  private serializeValue<T>(value: T): T {
    if (!this.config.serializationEnabled) {
      return value;
    }

    if (typeof value === "object" && value !== null) {
      return JSON.stringify(value) as T;
    }

    return value;
  }

  /**
   * Deserialize value
   */
  private deserializeValue<T>(entry: CacheEntry): T {
    let value: unknown = entry.value;

    if (entry.compressed) {
      value = this.decompressValue(value as string);
    }

    if (entry.serialized) {
      try {
        value = JSON.parse(typeof value === "string" ? value : String(value));
      } catch (error) {
        console.warn("[CacheService] Failed to deserialize value:", error);
      }
    }

    return value as T;
  }

  /**
   * Compress value
   */
  private compressValue<T>(value: T): T {
    if (!this.config.compressionEnabled) {
      return value;
    }

    // Simple compression placeholder - would use zlib in production
    return value;
  }

  /**
   * Decompress value
   */
  private decompressValue<T>(value: T): T {
    if (!this.config.compressionEnabled) {
      return value;
    }

    // Simple decompression placeholder - would use zlib in production
    return value;
  }

  /**
   * Update average get time
   */
  private updateAverageGetTime(duration: number): void {
    const totalOperations = this.stats.hits + this.stats.misses;
    this.stats.averageGetTime =
      (this.stats.averageGetTime * (totalOperations - 1) + duration) / totalOperations;
  }

  /**
   * Update average set time
   */
  private updateAverageSetTime(duration: number): void {
    // This is a simplified calculation
    this.stats.averageSetTime = (this.stats.averageSetTime + duration) / 2;
  }

  /**
   * Update hit rate
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    if (total > 0) {
      this.stats.hitRate = (this.stats.hits / total) * 100;
      this.stats.missRate = (this.stats.misses / total) * 100;
    }
  }

  /**
   * Update access patterns
   */
  private updateAccessPatterns(): void {
    const entries = Array.from(this.cache.values());

    // Most accessed
    this.stats.mostAccessed = entries
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 10)
      .map(entry => ({ key: entry.key, count: entry.accessCount }));

    // Recently accessed
    this.stats.recentlyAccessed = entries
      .sort((a, b) => b.lastAccessed - a.lastAccessed)
      .slice(0, 10)
      .map(entry => ({ key: entry.key, time: entry.lastAccessed }));
  }

  /**
   * Destroy cache service
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.clear();
    console.log("[CacheService] Destroyed cache service");
  }
}

/**
 * Create cache service instance
 */
export function createCacheService(config: CacheConfig): CacheService {
  return new CacheService(config);
}

/**
 * Default cache configuration
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxSize: 10000,
  maxMemoryMB: 256,
  defaultTTL: 3600000, // 1 hour
  maxTTL: 86400000, // 24 hours
  evictionPolicy: "lru",
  evictionBatchSize: 100,
  compressionEnabled: false,
  serializationEnabled: true,
  warmupEnabled: false,
  warmupKeys: [],
  cleanupInterval: 300000, // 5 minutes
  expiredCleanupBatch: 100,
};
