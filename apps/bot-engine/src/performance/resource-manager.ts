/**
 * Resource Manager - Manages resource allocation, cleanup, and optimization
 * Handles memory, file handles, connections, and other system resources
 */

import { EventEmitter } from "events";
import { promises as fs } from "fs";
import { join } from "path";
import { memoryUsage } from "process";

/**
 * Interface for resources that can be destroyed
 */
interface DestroyableResource {
  destroy(): void;
}

/**
 * Type guard to check if a resource is destroyable
 */
function isDestroyableResource(resource: unknown): resource is DestroyableResource {
  return (
    typeof resource === "object" &&
    resource !== null &&
    "destroy" in resource &&
    typeof (resource as DestroyableResource).destroy === "function"
  );
}

/**
 * Resource types
 */
export type ResourceType = "memory" | "file" | "connection" | "process" | "timer" | "stream";

/**
 * Resource configuration
 */
export interface ResourceConfig {
  // Memory management
  memory: {
    maxHeapSize: number; // MB
    gcThreshold: number; // MB
    gcInterval: number; // milliseconds
    leakDetectionEnabled: boolean;
    leakThreshold: number; // MB growth per minute
  };

  // File handle management
  files: {
    maxOpenFiles: number;
    cleanupInterval: number; // milliseconds
    tempFileTimeout: number; // milliseconds
    maxTempFiles: number;
  };

  // Connection management
  connections: {
    maxConnections: number;
    idleTimeout: number; // milliseconds
    cleanupInterval: number; // milliseconds
  };

  // Process management
  processes: {
    maxProcesses: number;
    processTimeout: number; // milliseconds
    cleanupInterval: number; // milliseconds
  };

  // Timer management
  timers: {
    maxTimers: number;
    cleanupInterval: number; // milliseconds
  };

  // Stream management
  streams: {
    maxStreams: number;
    idleTimeout: number; // milliseconds
    cleanupInterval: number; // milliseconds
  };

  // General settings
  monitoring: {
    enabled: boolean;
    interval: number; // milliseconds
    alertThresholds: {
      memory: number; // percentage
      files: number; // percentage
      connections: number; // percentage
    };
  };
}

/**
 * Resource entry for tracking
 */
interface ResourceEntry<T = unknown> {
  id: string;
  type: ResourceType;
  resource: T;
  createdAt: number;
  lastUsed: number;
  metadata: Record<string, unknown>;
  cleanup?: () => Promise<void>;
}

/**
 * Resource statistics
 */
export interface ResourceStats {
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    usage: number; // percentage
  };

  files: {
    open: number;
    max: number;
    temp: number;
    usage: number; // percentage
  };

  connections: {
    active: number;
    idle: number;
    max: number;
    usage: number; // percentage
  };

  processes: {
    active: number;
    max: number;
    usage: number; // percentage
  };

  timers: {
    active: number;
    max: number;
    usage: number; // percentage
  };

  streams: {
    active: number;
    max: number;
    usage: number; // percentage
  };

  total: {
    resources: number;
    cleanup: {
      automatic: number;
      manual: number;
      failed: number;
    };
  };
}

/**
 * Resource events
 */
export interface ResourceEvents {
  "resource:allocated": (type: ResourceType, id: string) => void;
  "resource:released": (type: ResourceType, id: string) => void;
  "resource:leaked": (type: ResourceType, id: string, age: number) => void;
  "resource:cleanup": (type: ResourceType, count: number) => void;
  "threshold:exceeded": (type: ResourceType, usage: number, threshold: number) => void;
  "memory:pressure": (stats: ResourceStats["memory"]) => void;
  "gc:triggered": (before: number, after: number) => void;
}

/**
 * Resource pool for managing specific resource types
 */
class ResourcePool<T = unknown> {
  private resources: Map<string, ResourceEntry> = new Map();
  private maxSize: number;
  private timeout: number;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(
    public readonly type: ResourceType,
    maxSize: number,
    timeout: number,
    cleanupIntervalMs: number
  ) {
    this.maxSize = maxSize;
    this.timeout = timeout;

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      void this.cleanup().catch(error => {
        console.error(`[ResourcePool] Cleanup error for ${this.type}:`, error);
      });
    }, cleanupIntervalMs);
  }

  /**
   * Allocate a resource
   */
  allocate(
    id: string,
    resource: T,
    metadata: Record<string, unknown> = {},
    cleanup?: () => Promise<void>
  ): void {
    if (this.resources.size >= this.maxSize) {
      throw new Error(`Resource pool for ${this.type} is full (${this.maxSize})`);
    }

    const entry: ResourceEntry = {
      id,
      type: this.type,
      resource,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      metadata,
      cleanup,
    };

    this.resources.set(id, entry);
  }

  /**
   * Get a resource
   */
  get(id: string): T | null {
    const entry = this.resources.get(id);
    if (!entry) {
      return null;
    }

    entry.lastUsed = Date.now();
    return entry.resource as T;
  }

  /**
   * Release a resource
   */
  async release(id: string): Promise<boolean> {
    const entry = this.resources.get(id);
    if (!entry) {
      return false;
    }

    // Run cleanup if provided
    if (entry.cleanup) {
      try {
        await entry.cleanup();
      } catch (error) {
        console.error(`[ResourcePool] Cleanup failed for ${this.type}:${id}:`, error);
      }
    }

    this.resources.delete(id);
    return true;
  }

  /**
   * Cleanup expired resources
   */
  async cleanup(): Promise<number> {
    const now = Date.now();
    const expiredIds: string[] = [];

    for (const [id, entry] of this.resources) {
      if (now - entry.lastUsed > this.timeout) {
        expiredIds.push(id);
      }
    }

    let cleanedCount = 0;
    for (const id of expiredIds) {
      const released = await this.release(id);
      if (released) {
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * Get pool statistics
   */
  getStats(): { active: number; max: number; usage: number } {
    return {
      active: this.resources.size,
      max: this.maxSize,
      usage: (this.resources.size / this.maxSize) * 100,
    };
  }

  /**
   * Get all resource entries
   */
  getEntries(): ResourceEntry[] {
    return Array.from(this.resources.values());
  }

  /**
   * Clear all resources
   */
  async clear(): Promise<void> {
    const entries = Array.from(this.resources.values());

    for (const entry of entries) {
      await this.release(entry.id);
    }
  }

  /**
   * Destroy the pool
   */
  async destroy(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    await this.clear();
  }
}

/**
 * Resource Manager implementation
 */
export class ResourceManager extends EventEmitter {
  private config: ResourceConfig;
  private pools: Map<ResourceType, ResourcePool> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private gcInterval?: NodeJS.Timeout;
  private isRunning: boolean = false;
  private stats: ResourceStats;
  private lastMemoryUsage: number = 0;
  private memoryGrowthHistory: number[] = [];

  constructor(config: ResourceConfig) {
    super();
    this.config = config;
    this.stats = this.initializeStats();
    this.initializePools();
  }

  /**
   * Initialize resource statistics
   */
  private initializeStats(): ResourceStats {
    return {
      memory: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        rss: 0,
        usage: 0,
      },
      files: {
        open: 0,
        max: this.config.files.maxOpenFiles,
        temp: 0,
        usage: 0,
      },
      connections: {
        active: 0,
        idle: 0,
        max: this.config.connections.maxConnections,
        usage: 0,
      },
      processes: {
        active: 0,
        max: this.config.processes.maxProcesses,
        usage: 0,
      },
      timers: {
        active: 0,
        max: this.config.timers.maxTimers,
        usage: 0,
      },
      streams: {
        active: 0,
        max: this.config.streams.maxStreams,
        usage: 0,
      },
      total: {
        resources: 0,
        cleanup: {
          automatic: 0,
          manual: 0,
          failed: 0,
        },
      },
    };
  }

  /**
   * Initialize resource pools
   */
  private initializePools(): void {
    // Memory pool (for tracking memory allocations)
    this.pools.set("memory", new ResourcePool("memory", 1000, 0, 60000));

    // File pool
    this.pools.set(
      "file",
      new ResourcePool(
        "file",
        this.config.files.maxOpenFiles,
        this.config.files.tempFileTimeout,
        this.config.files.cleanupInterval
      )
    );

    // Connection pool
    this.pools.set(
      "connection",
      new ResourcePool(
        "connection",
        this.config.connections.maxConnections,
        this.config.connections.idleTimeout,
        this.config.connections.cleanupInterval
      )
    );

    // Process pool
    this.pools.set(
      "process",
      new ResourcePool(
        "process",
        this.config.processes.maxProcesses,
        this.config.processes.processTimeout,
        this.config.processes.cleanupInterval
      )
    );

    // Timer pool
    this.pools.set(
      "timer",
      new ResourcePool("timer", this.config.timers.maxTimers, 0, this.config.timers.cleanupInterval)
    );

    // Stream pool
    this.pools.set(
      "stream",
      new ResourcePool(
        "stream",
        this.config.streams.maxStreams,
        this.config.streams.idleTimeout,
        this.config.streams.cleanupInterval
      )
    );
  }

  /**
   * Start resource monitoring
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Start monitoring
    if (this.config.monitoring.enabled) {
      this.monitoringInterval = setInterval(() => {
        this.updateStats();
        this.checkThresholds();
        this.detectMemoryLeaks();
      }, this.config.monitoring.interval);
    }

    // Start garbage collection monitoring
    if (this.config.memory.gcInterval > 0) {
      this.gcInterval = setInterval(() => {
        this.checkGarbageCollection();
      }, this.config.memory.gcInterval);
    }

    console.log("[ResourceManager] Started resource monitoring");
  }

  /**
   * Stop resource monitoring
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    if (this.gcInterval) {
      clearInterval(this.gcInterval);
    }

    // Cleanup all pools
    for (const pool of this.pools.values()) {
      await pool.destroy();
    }

    console.log("[ResourceManager] Stopped resource monitoring");
  }

  /**
   * Allocate a resource
   */
  allocate<T>(
    type: ResourceType,
    id: string,
    resource: T,
    metadata: Record<string, unknown> = {}
  ): void {
    const pool = this.pools.get(type);
    if (!pool) {
      throw new Error(`No pool found for resource type: ${type}`);
    }

    // Add automatic cleanup for certain resource types
    let cleanup: (() => Promise<void>) | undefined;

    if (type === "file" && typeof resource === "string") {
      cleanup = async () => {
        try {
          await fs.unlink(resource);
        } catch {
          // File might not exist, ignore
        }
      };
    } else if (type === "stream" && isDestroyableResource(resource)) {
      cleanup = () => {
        try {
          resource.destroy();
        } catch (error) {
          console.error("[ResourceManager] Stream cleanup error:", error);
        }
        return Promise.resolve();
      };
    }

    pool.allocate(id, resource, metadata, cleanup);
    this.emit("resource:allocated", type, id);
  }

  /**
   * Get a resource
   */
  get<T>(type: ResourceType, id: string): T | null {
    const pool = this.pools.get(type);
    if (!pool) {
      return null;
    }

    return pool.get(id) as T | null;
  }

  /**
   * Release a resource
   */
  async release(type: ResourceType, id: string): Promise<boolean> {
    const pool = this.pools.get(type);
    if (!pool) {
      return false;
    }

    const released = await pool.release(id);
    if (released) {
      this.emit("resource:released", type, id);
      this.stats.total.cleanup.manual++;
    }

    return released;
  }

  /**
   * Create a temporary file resource
   */
  async createTempFile(id: string, content: string, extension: string = ".tmp"): Promise<string> {
    const tempPath = join("/tmp", `${id}${extension}`);
    await fs.writeFile(tempPath, content);

    this.allocate("file", id, tempPath, {
      type: "temporary",
      size: Buffer.byteLength(content),
      extension,
    });

    return tempPath;
  }

  /**
   * Track a timer resource
   */
  trackTimer(id: string, timer: NodeJS.Timeout): void {
    const cleanup = async () => {
      return Promise.resolve(clearTimeout(timer));
    };

    this.pools.get("timer")?.allocate(id, timer, { type: "timeout" }, cleanup);
    this.emit("resource:allocated", "timer", id);
  }

  /**
   * Track an interval resource
   */
  trackInterval(id: string, interval: NodeJS.Timeout): void {
    const cleanup = async () => {
      return Promise.resolve(clearInterval(interval));
    };

    this.pools.get("timer")?.allocate(id, interval, { type: "interval" }, cleanup);
    this.emit("resource:allocated", "timer", id);
  }

  /**
   * Get current resource statistics
   */
  getStats(): ResourceStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get resource health
   */
  getHealth(): Record<string, unknown> {
    const stats = this.getStats();

    return {
      healthy: stats.memory.usage < 85 && stats.files.usage < 85 && stats.connections.usage < 85,
      memory: {
        usage: stats.memory.usage,
        status:
          stats.memory.usage > 90 ? "critical" : stats.memory.usage > 80 ? "warning" : "healthy",
      },
      files: {
        usage: stats.files.usage,
        status:
          stats.files.usage > 90 ? "critical" : stats.files.usage > 80 ? "warning" : "healthy",
      },
      connections: {
        usage: stats.connections.usage,
        status:
          stats.connections.usage > 90
            ? "critical"
            : stats.connections.usage > 80
              ? "warning"
              : "healthy",
      },
    };
  }

  /**
   * Force garbage collection
   */
  forceGarbageCollection(): void {
    if (global.gc) {
      const before = process.memoryUsage().heapUsed;
      global.gc();
      const after = process.memoryUsage().heapUsed;

      this.emit("gc:triggered", before, after);
      console.log(`[ResourceManager] GC: ${Math.round((before - after) / 1024 / 1024)}MB freed`);
    }
  }

  /**
   * Update resource statistics
   */
  private updateStats(): void {
    const memStats = memoryUsage();

    // Update memory stats
    this.stats.memory = {
      heapUsed: memStats.heapUsed,
      heapTotal: memStats.heapTotal,
      external: memStats.external,
      rss: memStats.rss,
      usage: (memStats.heapUsed / memStats.heapTotal) * 100,
    };

    // Update pool stats
    for (const [type, pool] of this.pools) {
      const poolStats = pool.getStats();

      switch (type) {
        case "file":
          this.stats.files.open = poolStats.active;
          this.stats.files.usage = poolStats.usage;
          break;
        case "connection":
          this.stats.connections.active = poolStats.active;
          this.stats.connections.usage = poolStats.usage;
          break;
        case "process":
          this.stats.processes.active = poolStats.active;
          this.stats.processes.usage = poolStats.usage;
          break;
        case "timer":
          this.stats.timers.active = poolStats.active;
          this.stats.timers.usage = poolStats.usage;
          break;
        case "stream":
          this.stats.streams.active = poolStats.active;
          this.stats.streams.usage = poolStats.usage;
          break;
      }
    }

    // Update total resources
    this.stats.total.resources = Array.from(this.pools.values()).reduce(
      (sum, pool) => sum + pool.getStats().active,
      0
    );
  }

  /**
   * Check resource thresholds
   */
  private checkThresholds(): void {
    const thresholds = this.config.monitoring.alertThresholds;

    if (this.stats.memory.usage > thresholds.memory) {
      this.emit("threshold:exceeded", "memory", this.stats.memory.usage, thresholds.memory);
    }

    if (this.stats.files.usage > thresholds.files) {
      this.emit("threshold:exceeded", "file", this.stats.files.usage, thresholds.files);
    }

    if (this.stats.connections.usage > thresholds.connections) {
      this.emit(
        "threshold:exceeded",
        "connection",
        this.stats.connections.usage,
        thresholds.connections
      );
    }

    // Check for memory pressure
    if (this.stats.memory.usage > 85) {
      this.emit("memory:pressure", this.stats.memory);
    }
  }

  /**
   * Detect memory leaks
   */
  private detectMemoryLeaks(): void {
    if (!this.config.memory.leakDetectionEnabled) {
      return;
    }

    const currentMemory = this.stats.memory.heapUsed;
    const growth = currentMemory - this.lastMemoryUsage;

    this.memoryGrowthHistory.push(growth);

    // Keep only last 10 measurements
    if (this.memoryGrowthHistory.length > 10) {
      this.memoryGrowthHistory.shift();
    }

    // Check for consistent growth
    if (this.memoryGrowthHistory.length >= 5) {
      const averageGrowth =
        this.memoryGrowthHistory.reduce((sum, g) => sum + g, 0) / this.memoryGrowthHistory.length;
      const growthPerMinute = (averageGrowth / this.config.monitoring.interval) * 60000;

      if (growthPerMinute > this.config.memory.leakThreshold * 1024 * 1024) {
        console.warn(
          `[ResourceManager] Potential memory leak detected: ${Math.round(growthPerMinute / 1024 / 1024)}MB/min growth`
        );
      }
    }

    this.lastMemoryUsage = currentMemory;
  }

  /**
   * Check if garbage collection is needed
   */
  private checkGarbageCollection(): void {
    const heapUsed = this.stats.memory.heapUsed;
    const thresholdBytes = this.config.memory.gcThreshold * 1024 * 1024;

    if (heapUsed > thresholdBytes) {
      this.forceGarbageCollection();
    }
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    let totalCleaned = 0;

    for (const [type, pool] of this.pools) {
      const cleaned = await pool.cleanup();
      totalCleaned += cleaned;

      if (cleaned > 0) {
        this.emit("resource:cleanup", type, cleaned);
        this.stats.total.cleanup.automatic += cleaned;
      }
    }

    console.log(`[ResourceManager] Cleaned up ${totalCleaned} resources`);
  }
}

/**
 * Create resource manager instance
 */
export function createResourceManager(config: ResourceConfig): ResourceManager {
  return new ResourceManager(config);
}

/**
 * Default resource manager configuration
 */
export const DEFAULT_RESOURCE_CONFIG: ResourceConfig = {
  memory: {
    maxHeapSize: 1024, // 1GB
    gcThreshold: 512, // 512MB
    gcInterval: 60000, // 1 minute
    leakDetectionEnabled: true,
    leakThreshold: 10, // 10MB/min
  },
  files: {
    maxOpenFiles: 1000,
    cleanupInterval: 300000, // 5 minutes
    tempFileTimeout: 3600000, // 1 hour
    maxTempFiles: 100,
  },
  connections: {
    maxConnections: 100,
    idleTimeout: 300000, // 5 minutes
    cleanupInterval: 60000, // 1 minute
  },
  processes: {
    maxProcesses: 10,
    processTimeout: 1800000, // 30 minutes
    cleanupInterval: 300000, // 5 minutes
  },
  timers: {
    maxTimers: 1000,
    cleanupInterval: 60000, // 1 minute
  },
  streams: {
    maxStreams: 100,
    idleTimeout: 300000, // 5 minutes
    cleanupInterval: 60000, // 1 minute
  },
  monitoring: {
    enabled: true,
    interval: 30000, // 30 seconds
    alertThresholds: {
      memory: 85, // 85%
      files: 80, // 80%
      connections: 80, // 80%
    },
  },
};
