/**
 * Message Templates - Templates for different status messages
 * Provides consistent, user-friendly message formatting
 */

import type { StatusMessageType } from "../communication/status-messenger.js";

export interface MessageTemplate {
  template: string;
  emoji?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  variables?: string[];
  maxLength?: number;
}

export interface MessageTemplateData {
  [key: string]: unknown;
  operationType?: string;
  projectName?: string;
  url?: string;
  error?: string;
  userMessage?: string;
  progress?: number;
  message?: string;
  stage?: string;
  repoName?: string;
  repoUrl?: string;
  changeCount?: number;
  projectType?: string;
  timeElapsed?: number;
  estimatedTime?: number;
}

/**
 * MessageTemplates class for managing status message templates
 */
export class MessageTemplates {
  private templates: Map<StatusMessageType, MessageTemplate>;

  constructor() {
    this.templates = new Map();
    this.initializeTemplates();
  }

  /**
   * Get formatted message for a specific type
   */
  getTemplate(type: StatusMessageType, data: MessageTemplateData = {}): string {
    const template = this.templates.get(type);

    if (!template) {
      console.warn(`[MessageTemplates] Template not found for type: ${type}`);
      return this.getDefaultTemplate(type, data);
    }

    try {
      const message = this.formatTemplate(template.template, data);
      const emoji = template.emoji ? `${template.emoji} ` : "";
      const fullMessage = `${emoji}${message}`;

      // Ensure message doesn't exceed maximum length
      const maxLength = template.maxLength || 1600; // Twilio WhatsApp limit

      return fullMessage.length > maxLength
        ? fullMessage.substring(0, maxLength - 3) + "..."
        : fullMessage;
    } catch (error) {
      console.error(`[MessageTemplates] Error formatting template for ${type}:`, error);
      return this.getDefaultTemplate(type, data);
    }
  }

  /**
   * Add or update a template
   */
  setTemplate(type: StatusMessageType, template: MessageTemplate): void {
    this.templates.set(type, template);
  }

  /**
   * Get all available templates
   */
  getAvailableTemplates(): StatusMessageType[] {
    return Array.from(this.templates.keys());
  }

  /**
   * Check if template exists
   */
  hasTemplate(type: StatusMessageType): boolean {
    return this.templates.has(type);
  }

  // Private methods

  /**
   * Initialize default templates
   */
  private initializeTemplates(): void {
    // Operation start messages
    this.templates.set("operation_start", {
      template: "Starting to {operationType}... I'll keep you updated on the progress.",
      emoji: "🚀",
      priority: "high",
      variables: ["operationType"],
    });

    // AI generation messages
    this.templates.set("ai_generation_start", {
      template: "AI is generating your website code... This might take a moment.",
      emoji: "🤖",
      priority: "medium",
    });

    this.templates.set("ai_generation_progress", {
      template: "AI is working on: {stage}",
      emoji: "⚡",
      priority: "medium",
      variables: ["stage"],
    });

    this.templates.set("ai_generation_complete", {
      template: "Website code generated successfully! Moving to the next step.",
      emoji: "✅",
      priority: "medium",
    });

    // Project setup messages
    this.templates.set("project_setup_start", {
      template: "Setting up project structure...",
      emoji: "🔧",
      priority: "medium",
    });

    this.templates.set("project_setup_complete", {
      template: "Project structure created successfully!",
      emoji: "📁",
      priority: "medium",
    });

    // GitHub repository messages
    this.templates.set("github_creation_start", {
      template: "Creating GitHub repository for version control...",
      emoji: "📦",
      priority: "medium",
    });

    this.templates.set("github_creation_complete", {
      template:
        'GitHub repository created successfully!{repoUrl ? `\n\n📁 Repository: ${repoUrl}` : ""}',
      emoji: "✅",
      priority: "medium",
      variables: ["repoUrl"],
    });

    // Deployment messages
    this.templates.set("deployment_start", {
      template: "Deploying your website to Vercel...",
      emoji: "🌐",
      priority: "medium",
    });

    this.templates.set("deployment_progress", {
      template: "Deployment progress: {stage}",
      emoji: "📊",
      priority: "medium",
      variables: ["stage"],
    });

    this.templates.set("deployment_complete", {
      template: 'Website deployed successfully!{url ? `\n\n🌐 Live URL: ${url}` : ""}',
      emoji: "🎉",
      priority: "high",
      variables: ["url"],
    });

    // Operation success messages
    this.templates.set("operation_success", {
      template:
        'Your {operationType} completed successfully!{url ? `\n\n🌐 Website: ${url}` : ""}{repoUrl ? `\n📁 Repository: ${repoUrl}` : ""}',
      emoji: "✅",
      priority: "high",
      variables: ["operationType", "url", "repoUrl"],
    });

    // Error messages
    this.templates.set("operation_error", {
      template: "Error during {operationType}: {userMessage || error}",
      emoji: "❌",
      priority: "urgent",
      variables: ["operationType", "userMessage", "error"],
    });

    this.templates.set("operation_timeout", {
      template: 'Operation timed out. {message || "Please try again with a simpler request."}',
      emoji: "⏳",
      priority: "high",
      variables: ["message"],
    });

    // Progress update messages
    this.templates.set("progress_update", {
      template:
        '{message || `Progress: ${progress}%`}{estimatedTime ? ` (Est. ${estimatedTime}s remaining)` : ""}',
      emoji: "📊",
      priority: "medium",
      variables: ["progress", "message", "estimatedTime"],
    });

    // Warning messages
    this.templates.set("warning", {
      template: "{message}",
      emoji: "⚠️",
      priority: "medium",
      variables: ["message"],
    });

    // Info messages
    this.templates.set("info", {
      template: "{message}",
      emoji: "ℹ️",
      priority: "low",
      variables: ["message"],
    });

    // Specific website creation messages
    this.templates.set("website_creation_start", {
      template: 'Creating your website: "{projectName}"...',
      emoji: "🏗️",
      priority: "high",
      variables: ["projectName"],
    });

    this.templates.set("website_editing_start", {
      template: 'Editing your website: "{projectName}"...',
      emoji: "✏️",
      priority: "high",
      variables: ["projectName"],
    });

    this.templates.set("repository_import_start", {
      template: "Importing repository: {repoName}...",
      emoji: "📥",
      priority: "high",
      variables: ["repoName"],
    });

    // Enhanced editing messages
    this.templates.set("enhanced_editing_start", {
      template: "Using enhanced vector-based editing! Analyzing your codebase...",
      emoji: "🚀",
      priority: "medium",
    });

    this.templates.set("enhanced_changes_generated", {
      template: "Generated {changeCount} intelligent code changes based on your codebase.",
      emoji: "✨",
      priority: "medium",
      variables: ["changeCount"],
    });

    this.templates.set("enhanced_changes_applied", {
      template: "Applied {changeCount} enhanced changes to your project.",
      emoji: "⚡",
      priority: "medium",
      variables: ["changeCount"],
    });

    // Scaffold project messages
    this.templates.set("scaffold_generation_start", {
      template: "Creating {projectType} project scaffold with all necessary files...",
      emoji: "🏗️",
      priority: "medium",
      variables: ["projectType"],
    });

    this.templates.set("scaffold_files_generated", {
      template: "Generated {fileCount} files for your {projectType} project.",
      emoji: "📄",
      priority: "medium",
      variables: ["fileCount", "projectType"],
    });

    // Long-running operation messages
    this.templates.set("long_operation_notice", {
      template:
        "This is taking longer than expected... Please wait while I complete the {operationType}.",
      emoji: "⏳",
      priority: "medium",
      variables: ["operationType"],
    });

    this.templates.set("operation_retry", {
      template: "Retrying {operationType} (attempt {attempt}/{maxAttempts})...",
      emoji: "🔄",
      priority: "medium",
      variables: ["operationType", "attempt", "maxAttempts"],
    });

    // Rate limiting messages
    this.templates.set("rate_limit_warning", {
      template: "You're sending requests quickly. Please wait a moment before sending another.",
      emoji: "⏰",
      priority: "medium",
    });

    // Completion with analytics
    this.templates.set("operation_complete_with_stats", {
      template:
        'Operation completed!{timeElapsed ? `\n⏱️ Time taken: ${timeElapsed}s` : ""}{url ? `\n🌐 URL: ${url}` : ""}{repoUrl ? `\n📁 Repository: ${repoUrl}` : ""}',
      emoji: "🎯",
      priority: "high",
      variables: ["timeElapsed", "url", "repoUrl"],
    });
  }

  /**
   * Format template with provided data
   */
  private formatTemplate(template: string, data: MessageTemplateData): string {
    // Handle conditional expressions in templates
    let formatted = template.replace(/\{([^}]+)\}/g, (_match, expression) => {
      try {
        // Handle simple conditional expressions like {condition ? "true" : "false"}
        if (
          typeof expression === "string" &&
          expression.includes("?") &&
          expression.includes(":")
        ) {
          return this.evaluateConditional(expression, data);
        }

        // Handle simple variable substitution
        const value = this.getNestedValue(
          data,
          typeof expression === "string" ? expression.trim() : ""
        );
        return value !== undefined ? String(value) : "";
      } catch (error) {
        console.warn(
          `[MessageTemplates] Error evaluating expression: ${typeof expression === "string" ? expression : "unknown"}`,
          error
        );
        return "";
      }
    });

    // Clean up any remaining empty conditional blocks
    formatted = formatted.replace(/\n\n+/g, "\n\n").trim();

    return formatted;
  }

  /**
   * Evaluate conditional expressions in templates
   */
  private evaluateConditional(expression: string, data: MessageTemplateData): string {
    try {
      const parts = expression.split("?", 2);
      if (parts.length !== 2) return "";

      const condition = parts[0];
      const rest = parts[1];
      if (!rest) return "";

      const valueParts = rest.split(":", 2);
      if (valueParts.length !== 2) return "";

      const trueValue = valueParts[0];
      const falseValue = valueParts[1];

      const conditionValue = this.getNestedValue(data, condition?.trim() || "");
      const isTrue = Boolean(conditionValue);

      if (isTrue && trueValue) {
        const trueText = trueValue.trim();
        // Handle nested template expressions in true value
        return this.formatTemplate(trueText.replace(/^["']|["']$/g, ""), data);
      } else {
        const falseText = falseValue ? falseValue.trim() : "";
        return falseText.replace(/^["']|["']$/g, "");
      }
    } catch (error) {
      console.warn(`[MessageTemplates] Error evaluating conditional: ${expression}`, error);
      return "";
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split(".").reduce((current: unknown, key: string) => {
      return current && typeof current === "object" && current !== null && key in current
        ? (current as Record<string, unknown>)[key]
        : undefined;
    }, obj as unknown);
  }

  /**
   * Get default template for unknown message types
   */
  private getDefaultTemplate(type: StatusMessageType, data: MessageTemplateData): string {
    const operationType = data.operationType || "operation";
    const message = data.message || "Processing your request...";

    switch (type) {
      case "operation_start":
        return `🚀 Starting ${operationType}...`;
      case "operation_success":
        return `✅ ${operationType} completed successfully!`;
      case "operation_error":
        return `❌ Error during ${operationType}: ${data.userMessage || data.error || "Unknown error"}`;
      case "progress_update":
        return `📊 ${message}`;
      case "warning":
        return `⚠️ ${message}`;
      case "info":
        return `ℹ️ ${message}`;
      default:
        return `🔄 ${message}`;
    }
  }
}

/**
 * Default message templates instance
 */
export const messageTemplates = new MessageTemplates();

/**
 * Helper function to get formatted message
 */
export function formatStatusMessage(
  type: StatusMessageType,
  data: MessageTemplateData = {}
): string {
  return messageTemplates.getTemplate(type, data);
}
