/**
 * Fallback Service
 * Provides fallback mechanisms when primary systems fail
 */

import { EventEmitter } from "events";
import { randomUUID } from "crypto";

import { BotError } from "@whatsite-bot/core";

/**
 * Fallback strategy types
 */
export type FallbackType =
  | "cached_response"
  | "simple_response"
  | "alternative_service"
  | "degraded_functionality"
  | "queue_for_later"
  | "manual_intervention"
  | "default_action"
  | "graceful_failure";

/**
 * Fallback configuration
 */
export interface FallbackConfig {
  /** Enable fallback mechanisms */
  enableFallbacks: boolean;
  /** Fallback timeout */
  fallbackTimeout: number;
  /** Cache expiry time */
  cacheExpiryMs: number;
  /** Maximum queue size for queued operations */
  maxQueueSize: number;
  /** Default fallback responses */
  defaultResponses: Record<string, string>;
  /** Fallback priorities by error type */
  fallbackPriorities: Record<string, FallbackType[]>;
}

/**
 * Fallback execution result
 */
export interface FallbackResult {
  /** Result ID */
  id: string;
  /** Fallback type used */
  type: FallbackType;
  /** Success flag */
  success: boolean;
  /** Result data */
  data?: unknown;
  /** Error if failed */
  error?: Error;
  /** Execution time */
  executionTime: number;
  /** Timestamp */
  timestamp: Date;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Cached fallback data
 */
interface CachedFallback {
  /** Cache key */
  key: string;
  /** Cached data */
  data: unknown;
  /** Timestamp */
  timestamp: Date;
  /** Expiry time */
  expiryTime: Date;
  /** Hit count */
  hitCount: number;
}

/**
 * Queued operation
 */
interface QueuedOperation {
  /** Operation ID */
  id: string;
  /** Operation type */
  type: string;
  /** Operation data */
  data: unknown;
  /** Context */
  context: Record<string, unknown>;
  /** Timestamp */
  timestamp: Date;
  /** Retry count */
  retryCount: number;
}

/**
 * Fallback service events
 */
export interface FallbackServiceEvents {
  "fallback:executed": (result: FallbackResult) => void;
  "fallback:cached": (key: string, data: unknown) => void;
  "fallback:cache:hit": (key: string, data: unknown) => void;
  "fallback:cache:miss": (key: string) => void;
  "fallback:queued": (operation: QueuedOperation) => void;
  "fallback:dequeued": (operation: QueuedOperation) => void;
  "fallback:manual:required": (error: BotError, context: Record<string, unknown>) => void;
}

/**
 * Fallback Service Implementation
 */
export class FallbackService extends EventEmitter {
  private config: FallbackConfig;
  private cache: Map<string, CachedFallback> = new Map();
  private operationQueue: QueuedOperation[] = [];
  private fallbackStats: {
    totalFallbacks: number;
    successfulFallbacks: number;
    failedFallbacks: number;
    fallbacksByType: Record<FallbackType, number>;
    cacheHitRate: number;
    cacheHits: number;
    cacheMisses: number;
    averageExecutionTime: number;
  };

  constructor(config: FallbackConfig) {
    super();
    this.config = config;
    this.fallbackStats = {
      totalFallbacks: 0,
      successfulFallbacks: 0,
      failedFallbacks: 0,
      fallbacksByType: {
        cached_response: 0,
        simple_response: 0,
        alternative_service: 0,
        degraded_functionality: 0,
        queue_for_later: 0,
        manual_intervention: 0,
        default_action: 0,
        graceful_failure: 0,
      },
      cacheHitRate: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageExecutionTime: 0,
    };

    // Set up cache cleanup
    setInterval(() => {
      this.cleanupCache();
    }, 60000); // Every minute

    console.log("[FallbackService] Initialized with configuration:", config);
  }

  /**
   * Get fallback for error
   */
  async getFallback(error: BotError, context: Record<string, unknown>): Promise<string | null> {
    if (!this.config.enableFallbacks) {
      return null;
    }

    const fallbackTypes = this.getFallbackTypes(error);

    for (const fallbackType of fallbackTypes) {
      try {
        const result = await this.executeFallback(fallbackType, error, context);

        if (result.success) {
          this.updateStats(result);
          this.emit("fallback:executed", result);
          return fallbackType;
        }
      } catch (fallbackError) {
        console.error(`[FallbackService] Fallback ${fallbackType} failed:`, fallbackError);
      }
    }

    return null;
  }

  /**
   * Execute specific fallback type
   */
  async executeFallback(
    type: FallbackType,
    error: BotError,
    context: Record<string, unknown>
  ): Promise<FallbackResult> {
    const startTime = Date.now();
    const result: FallbackResult = {
      id: randomUUID(),
      type,
      success: false,
      executionTime: 0,
      timestamp: new Date(),
    };

    try {
      switch (type) {
        case "cached_response":
          result.data = await this.getCachedResponse(error, context);
          result.success = result.data !== null;
          break;

        case "simple_response":
          result.data = await this.getSimpleResponse(error, context);
          result.success = result.data !== null;
          break;

        case "alternative_service":
          result.data = await this.useAlternativeService(error, context);
          result.success = result.data !== null;
          break;

        case "degraded_functionality":
          result.data = await this.provideDegradedFunctionality(error, context);
          result.success = result.data !== null;
          break;

        case "queue_for_later":
          result.data = this.queueForLater(error, context);
          result.success = result.data !== null;
          break;

        case "manual_intervention":
          result.data = this.requestManualIntervention(error, context);
          result.success = result.data !== null;
          break;

        case "default_action":
          result.data = this.performDefaultAction(error, context);
          result.success = result.data !== null;
          break;

        case "graceful_failure":
          result.data = this.gracefulFailure(error, context);
          result.success = true; // Graceful failure is always "successful"
          break;

        default:
          throw new Error(`Unknown fallback type: ${String(type)}`);
      }
    } catch (fallbackError) {
      result.error =
        fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError));
      result.success = false;
    } finally {
      result.executionTime = Date.now() - startTime;
    }

    return result;
  }

  /**
   * Get cached response fallback
   */
  private getCachedResponse(error: BotError, context: Record<string, unknown>): unknown {
    const cacheKey = this.generateCacheKey(error, context);
    const cached = this.cache.get(cacheKey);

    if (cached && cached.expiryTime > new Date()) {
      cached.hitCount++;
      this.fallbackStats.cacheHits++;
      this.emit("fallback:cache:hit", cacheKey, cached.data);
      return cached.data;
    }

    this.fallbackStats.cacheMisses++;
    this.emit("fallback:cache:miss", cacheKey);
    return null;
  }

  /**
   * Get simple response fallback
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private getSimpleResponse(error: BotError, _context: Record<string, unknown>): unknown {
    const errorType = error.constructor.name;
    const category = error.category;

    // Check for specific error type response
    if (this.config.defaultResponses[errorType]) {
      return {
        type: "simple_response",
        message: this.config.defaultResponses[errorType],
        userMessage: error.getUserMessage(),
      };
    }

    // Check for category response
    if (this.config.defaultResponses[category]) {
      return {
        type: "simple_response",
        message: this.config.defaultResponses[category],
        userMessage: error.getUserMessage(),
      };
    }

    // Default generic response
    return {
      type: "simple_response",
      message: "I encountered an issue but I'm working on it. Please try again in a moment.",
      userMessage: error.getUserMessage(),
    };
  }

  /**
   * Use alternative service fallback
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private useAlternativeService(error: BotError, _context: Record<string, unknown>): unknown {
    // This would implement alternative service logic
    // For now, simulate based on error type

    if (error.category === "ai") {
      return {
        type: "alternative_service",
        service: "backup_ai",
        message: "Using backup AI service",
        data: "Fallback AI response",
      };
    }

    if (error.category === "deployment") {
      return {
        type: "alternative_service",
        service: "backup_deployment",
        message: "Using backup deployment service",
        data: "Fallback deployment initiated",
      };
    }

    if (error.category === "communication") {
      return {
        type: "alternative_service",
        service: "backup_messaging",
        message: "Using backup messaging service",
        data: "Message queued for backup delivery",
      };
    }

    return null;
  }

  /**
   * Provide degraded functionality fallback
   */
  private provideDegradedFunctionality(
    error: BotError,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _context: Record<string, unknown>
  ): unknown {
    const category = error.category;

    switch (category) {
      case "ai":
        return {
          type: "degraded_functionality",
          functionality: "basic_responses",
          message: "Providing basic responses while AI service is unavailable",
          data: "Basic functionality available",
        };

      case "deployment":
        return {
          type: "degraded_functionality",
          functionality: "manual_deployment",
          message: "Manual deployment required",
          data: "Deployment instructions provided",
        };

      case "workspace":
        return {
          type: "degraded_functionality",
          functionality: "read_only_mode",
          message: "Workspace in read-only mode",
          data: "Read-only access available",
        };

      default:
        return {
          type: "degraded_functionality",
          functionality: "limited_features",
          message: "Limited functionality available",
          data: "Some features may be unavailable",
        };
    }
  }

  /**
   * Queue operation for later fallback
   */
  private queueForLater(error: BotError, context: Record<string, unknown>): unknown {
    if (this.operationQueue.length >= this.config.maxQueueSize) {
      return null;
    }

    const operation: QueuedOperation = {
      id: randomUUID(),
      type: error.constructor.name,
      data: error,
      context,
      timestamp: new Date(),
      retryCount: 0,
    };

    this.operationQueue.push(operation);
    this.emit("fallback:queued", operation);

    return {
      type: "queue_for_later",
      operationId: operation.id,
      message: "Operation queued for retry when service is available",
      queuePosition: this.operationQueue.length,
    };
  }

  /**
   * Request manual intervention fallback
   */
  private requestManualIntervention(error: BotError, context: Record<string, unknown>): unknown {
    this.emit("fallback:manual:required", error, context);

    return {
      type: "manual_intervention",
      message: "Manual intervention required",
      ticketId: randomUUID(),
      priority: error.severity,
      details: {
        error: error.message,
        code: error.code,
        category: error.category,
        context,
      },
    };
  }

  /**
   * Perform default action fallback
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private performDefaultAction(error: BotError, _context: Record<string, unknown>): unknown {
    const category = error.category;

    switch (category) {
      case "session":
        return {
          type: "default_action",
          action: "create_new_session",
          message: "Creating new session",
          data: "New session will be created",
        };

      case "workspace":
        return {
          type: "default_action",
          action: "use_template",
          message: "Using default template",
          data: "Default workspace template applied",
        };

      case "communication":
        return {
          type: "default_action",
          action: "send_acknowledgment",
          message: "Sending acknowledgment",
          data: "Message received and queued",
        };

      default:
        return {
          type: "default_action",
          action: "log_and_continue",
          message: "Error logged, continuing operation",
          data: "Operation will continue with default behavior",
        };
    }
  }

  /**
   * Graceful failure fallback
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private gracefulFailure(error: BotError, _context: Record<string, unknown>): unknown {
    return {
      type: "graceful_failure",
      message: "Operation failed gracefully",
      userMessage: error.getUserMessage(),
      suggestedAction: "Please try again later or contact support",
      errorDetails: {
        code: error.code,
        category: error.category,
        severity: error.severity,
        retryable: error.retryable,
      },
    };
  }

  /**
   * Get fallback types for error
   */
  private getFallbackTypes(error: BotError): FallbackType[] {
    const errorType = error.constructor.name;
    const category = error.category;

    // Check specific error type priorities
    if (this.config.fallbackPriorities[errorType]) {
      return this.config.fallbackPriorities[errorType];
    }

    // Check category priorities
    if (this.config.fallbackPriorities[category]) {
      return this.config.fallbackPriorities[category];
    }

    // Default fallback priorities based on error characteristics
    const fallbacks: FallbackType[] = [];

    // Always try cached response first
    fallbacks.push("cached_response");

    // Add fallbacks based on error properties
    if (error.retryable) {
      fallbacks.push("queue_for_later");
    }

    if (error.severity === "low" || error.severity === "medium") {
      fallbacks.push("simple_response", "degraded_functionality", "default_action");
    }

    if (error.severity === "high") {
      fallbacks.push("alternative_service", "degraded_functionality", "manual_intervention");
    }

    if (error.severity === "critical") {
      fallbacks.push("alternative_service", "manual_intervention");
    }

    // Always have graceful failure as last resort
    fallbacks.push("graceful_failure");

    return fallbacks;
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(error: BotError, context: Record<string, unknown>): string {
    const keyParts = [
      error.constructor.name,
      error.code,
      error.category,
      JSON.stringify(context?.sessionId || ""),
      JSON.stringify(context?.operation || ""),
    ];

    return keyParts.join(":");
  }

  /**
   * Cache response
   */
  cacheResponse(key: string, data: unknown, ttl?: number): void {
    const expiryTime = new Date(Date.now() + (ttl || this.config.cacheExpiryMs));

    const cached: CachedFallback = {
      key,
      data,
      timestamp: new Date(),
      expiryTime,
      hitCount: 0,
    };

    this.cache.set(key, cached);
    this.emit("fallback:cached", key, data);
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = new Date();
    let cleaned = 0;

    for (const [key, cached] of this.cache.entries()) {
      if (cached.expiryTime < now) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`[FallbackService] Cleaned up ${cleaned} expired cache entries`);
    }
  }

  /**
   * Process queued operations
   */
  processQueue(): void {
    const processed: QueuedOperation[] = [];

    for (const operation of this.operationQueue) {
      try {
        // This would retry the original operation
        // For now, we'll just mark as processed
        processed.push(operation);
        this.emit("fallback:dequeued", operation);
      } catch {
        operation.retryCount++;

        // Remove from queue if max retries exceeded
        if (operation.retryCount >= 3) {
          processed.push(operation);
        }
      }
    }

    // Remove processed operations
    this.operationQueue = this.operationQueue.filter(op => !processed.includes(op));
  }

  /**
   * Update fallback statistics
   */
  private updateStats(result: FallbackResult): void {
    this.fallbackStats.totalFallbacks++;

    if (result.success) {
      this.fallbackStats.successfulFallbacks++;
    } else {
      this.fallbackStats.failedFallbacks++;
    }

    this.fallbackStats.fallbacksByType[result.type]++;

    // Update cache hit rate
    const totalCacheRequests = this.fallbackStats.cacheHits + this.fallbackStats.cacheMisses;
    if (totalCacheRequests > 0) {
      this.fallbackStats.cacheHitRate = this.fallbackStats.cacheHits / totalCacheRequests;
    }

    // Update average execution time
    const totalExecutionTime =
      this.fallbackStats.averageExecutionTime * (this.fallbackStats.totalFallbacks - 1) +
      result.executionTime;
    this.fallbackStats.averageExecutionTime =
      totalExecutionTime / this.fallbackStats.totalFallbacks;
  }

  /**
   * Get fallback statistics
   */
  getStats(): typeof this.fallbackStats {
    return { ...this.fallbackStats };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; hitCount: number; age: number }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.cache.values()).map(cached => ({
      key: cached.key,
      hitCount: cached.hitCount,
      age: now - cached.timestamp.getTime(),
    }));

    return {
      size: this.cache.size,
      hitRate: this.fallbackStats.cacheHitRate,
      entries,
    };
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): {
    size: number;
    maxSize: number;
    oldestOperation: Date | null;
    operationTypes: Record<string, number>;
  } {
    const operationTypes: Record<string, number> = {};
    let oldestOperation: Date | null = null;

    for (const operation of this.operationQueue) {
      operationTypes[operation.type] = (operationTypes[operation.type] || 0) + 1;

      if (!oldestOperation || operation.timestamp < oldestOperation) {
        oldestOperation = operation.timestamp;
      }
    }

    return {
      size: this.operationQueue.length,
      maxSize: this.config.maxQueueSize,
      oldestOperation,
      operationTypes,
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log("[FallbackService] Cache cleared");
  }

  /**
   * Clear queue
   */
  clearQueue(): void {
    this.operationQueue = [];
    console.log("[FallbackService] Queue cleared");
  }

  /**
   * Get configuration
   */
  getConfig(): FallbackConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<FallbackConfig>): void {
    this.config = { ...this.config, ...config };
    console.log("[FallbackService] Configuration updated:", this.config);
  }

  /**
   * Shutdown fallback service
   */
  shutdown(): void {
    console.log("[FallbackService] Shutting down...");

    // Process remaining queue
    this.processQueue();

    // Clear cache
    this.clearCache();

    console.log("[FallbackService] Shutdown complete");
  }
}

/**
 * Default fallback configuration
 */
export const DEFAULT_FALLBACK_CONFIG: FallbackConfig = {
  enableFallbacks: true,
  fallbackTimeout: 5000,
  cacheExpiryMs: 300000, // 5 minutes
  maxQueueSize: 100,
  defaultResponses: {
    SessionError: "I encountered a session issue. Let me start fresh.",
    WorkspaceError: "I had trouble with your workspace. Let me try again.",
    CommunicationError: "I had trouble sending your message. It will be retried.",
    AIError: "I had trouble processing your request. Using backup response.",
    DeploymentError: "I had trouble deploying your site. Will retry shortly.",
    SystemError: "I encountered a system issue. Please try again later.",
    ai: "AI service temporarily unavailable. Using backup responses.",
    deployment: "Deployment service temporarily unavailable. Will retry.",
    communication: "Messaging service temporarily unavailable. Messages queued.",
    workspace: "Workspace service temporarily unavailable. Using read-only mode.",
    session: "Session service temporarily unavailable. Creating new session.",
  },
  fallbackPriorities: {
    SessionError: ["cached_response", "simple_response", "default_action"],
    WorkspaceError: ["cached_response", "degraded_functionality", "default_action"],
    CommunicationError: ["queue_for_later", "alternative_service", "simple_response"],
    AIError: ["cached_response", "simple_response", "alternative_service"],
    DeploymentError: ["queue_for_later", "alternative_service", "manual_intervention"],
    SystemError: ["alternative_service", "manual_intervention", "graceful_failure"],
    ai: ["cached_response", "simple_response", "alternative_service"],
    deployment: ["queue_for_later", "alternative_service", "manual_intervention"],
    communication: ["queue_for_later", "alternative_service", "simple_response"],
    workspace: ["degraded_functionality", "default_action", "simple_response"],
    session: ["default_action", "simple_response", "graceful_failure"],
  },
};

/**
 * Create fallback service instance
 */
export function createFallbackService(
  config: FallbackConfig = DEFAULT_FALLBACK_CONFIG
): FallbackService {
  return new FallbackService(config);
}
