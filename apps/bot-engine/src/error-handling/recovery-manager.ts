/**
 * Recovery Manager
 * Implements automatic recovery mechanisms for different types of failures
 */

import { EventEmitter } from "events";
import { randomUUID } from "crypto";

import {
  BotError,
  ErrorRecoveryStrategy,
  SessionError,
  WorkspaceError,
  GitError,
  CommunicationError,
  AIError,
  DeploymentError,
  SystemError,
  ExternalServiceError,
  DatabaseError,
} from "@whatsite-bot/core";

/**
 * Recovery strategy types
 */
export type RecoveryStrategyType =
  | "retry"
  | "fallback"
  | "reset"
  | "restart"
  | "cleanup"
  | "rollback"
  | "skip"
  | "escalate";

/**
 * Recovery configuration
 */
export interface RecoveryConfig {
  /** Maximum recovery attempts */
  maxRecoveryAttempts: number;
  /** Recovery timeout */
  recoveryTimeout: number;
  /** Enable automatic recovery */
  enableAutoRecovery: boolean;
  /** Recovery strategies by error type */
  strategies: Record<string, RecoveryStrategyType[]>;
  /** Custom recovery handlers */
  customHandlers: Record<
    string,
    (error: BotError, context: Record<string, unknown>) => Promise<boolean>
  >;
  /** Recovery cooldown period */
  cooldownPeriod: number;
}

/**
 * Recovery attempt information
 */
export interface RecoveryAttempt {
  /** Attempt ID */
  id: string;
  /** Attempt number */
  attempt: number;
  /** Strategy used */
  strategy: RecoveryStrategyType;
  /** Start time */
  startTime: Date;
  /** End time */
  endTime?: Date;
  /** Duration */
  duration: number;
  /** Success flag */
  success: boolean;
  /** Error during recovery */
  error?: Error;
  /** Recovery details */
  details?: Record<string, unknown>;
}

/**
 * Recovery context
 */
export interface RecoveryContext {
  /** Recovery session ID */
  id: string;
  /** Original error */
  originalError: BotError;
  /** Recovery attempts */
  attempts: RecoveryAttempt[];
  /** Start time */
  startTime: Date;
  /** End time */
  endTime?: Date;
  /** Total duration */
  totalDuration: number;
  /** Success flag */
  success: boolean;
  /** Final strategy used */
  finalStrategy?: RecoveryStrategyType;
  /** Context data */
  context?: Record<string, unknown>;
}

/**
 * Recovery manager events
 */
export interface RecoveryManagerEvents {
  "recovery:started": (context: RecoveryContext) => void;
  "recovery:attempt": (context: RecoveryContext, attempt: RecoveryAttempt) => void;
  "recovery:success": (context: RecoveryContext) => void;
  "recovery:failed": (context: RecoveryContext, error: Error) => void;
  "recovery:timeout": (context: RecoveryContext) => void;
  "recovery:exhausted": (context: RecoveryContext) => void;
  "strategy:applied": (strategy: RecoveryStrategyType, error: BotError, success: boolean) => void;
}

/**
 * Recovery Manager Implementation
 */
export class RecoveryManager extends EventEmitter implements ErrorRecoveryStrategy {
  private config: RecoveryConfig;
  private activeRecoveries: Map<string, RecoveryContext> = new Map();
  private recoveryStats: {
    totalRecoveries: number;
    successfulRecoveries: number;
    failedRecoveries: number;
    averageAttempts: number;
    averageDuration: number;
    strategiesUsed: Record<RecoveryStrategyType, number>;
  };
  private lastRecoveryTime: Map<string, number> = new Map();

  constructor(config: RecoveryConfig) {
    super();
    this.config = config;
    this.recoveryStats = {
      totalRecoveries: 0,
      successfulRecoveries: 0,
      failedRecoveries: 0,
      averageAttempts: 0,
      averageDuration: 0,
      strategiesUsed: {
        retry: 0,
        fallback: 0,
        reset: 0,
        restart: 0,
        cleanup: 0,
        rollback: 0,
        skip: 0,
        escalate: 0,
      },
    };

    console.log("[RecoveryManager] Initialized with configuration:", config);
  }

  /**
   * Attempt to recover from error (implements ErrorRecoveryStrategy)
   */
  async recover(error: BotError, context?: Record<string, unknown>): Promise<boolean> {
    if (!this.canRecover(error)) {
      return false;
    }

    const recoveryContext = this.createRecoveryContext(error, context);
    this.activeRecoveries.set(recoveryContext.id, recoveryContext);

    try {
      this.emit("recovery:started", recoveryContext);

      const success = await this.executeRecovery(recoveryContext);

      recoveryContext.success = success;
      recoveryContext.endTime = new Date();
      recoveryContext.totalDuration =
        recoveryContext.endTime.getTime() - recoveryContext.startTime.getTime();

      this.updateStats(recoveryContext);

      if (success) {
        this.emit("recovery:success", recoveryContext);
        console.log(`[RecoveryManager] Recovery successful for error: ${error.code}`);
      } else {
        this.emit("recovery:exhausted", recoveryContext);
        console.log(`[RecoveryManager] Recovery exhausted for error: ${error.code}`);
      }

      return success;
    } catch (recoveryError) {
      const finalError =
        recoveryError instanceof Error ? recoveryError : new Error(String(recoveryError));

      recoveryContext.success = false;
      recoveryContext.endTime = new Date();
      recoveryContext.totalDuration =
        recoveryContext.endTime.getTime() - recoveryContext.startTime.getTime();

      this.updateStats(recoveryContext);
      this.emit("recovery:failed", recoveryContext, finalError);

      console.error(`[RecoveryManager] Recovery failed for error: ${error.code}:`, finalError);
      return false;
    } finally {
      this.activeRecoveries.delete(recoveryContext.id);
    }
  }

  /**
   * Check if recovery is possible (implements ErrorRecoveryStrategy)
   */
  canRecover(error: BotError): boolean {
    if (!this.config.enableAutoRecovery) {
      return false;
    }

    // Check cooldown period
    const errorKey = `${error.code}-${error.category}`;
    const lastRecovery = this.lastRecoveryTime.get(errorKey) || 0;
    const cooldownExpired = Date.now() - lastRecovery > this.config.cooldownPeriod;

    if (!cooldownExpired) {
      console.log(`[RecoveryManager] Recovery in cooldown for error: ${error.code}`);
      return false;
    }

    // Check if strategies are available for this error type
    const strategies = this.getStrategiesForError(error);
    return strategies.length > 0;
  }

  /**
   * Execute recovery attempts
   */
  private async executeRecovery(context: RecoveryContext): Promise<boolean> {
    const strategies = this.getStrategiesForError(context.originalError);

    for (let attemptNum = 1; attemptNum <= this.config.maxRecoveryAttempts; attemptNum++) {
      // Check timeout
      const elapsed = Date.now() - context.startTime.getTime();
      if (elapsed >= this.config.recoveryTimeout) {
        this.emit("recovery:timeout", context);
        return false;
      }

      // Try each strategy
      for (const strategy of strategies) {
        const attempt = this.createRecoveryAttempt(attemptNum, strategy);
        context.attempts.push(attempt);

        try {
          console.log(
            `[RecoveryManager] Attempting recovery with strategy: ${strategy} (attempt ${attemptNum})`
          );

          const success = await this.applyStrategy(
            strategy,
            context.originalError,
            context.context || {}
          );

          attempt.success = success;
          attempt.endTime = new Date();
          attempt.duration = attempt.endTime.getTime() - attempt.startTime.getTime();

          this.emit("recovery:attempt", context, attempt);
          this.emit("strategy:applied", strategy, context.originalError, success);

          this.recoveryStats.strategiesUsed[strategy]++;

          if (success) {
            context.finalStrategy = strategy;
            this.updateRecoveryTime(context.originalError);
            return true;
          }
        } catch (strategyError) {
          attempt.error =
            strategyError instanceof Error ? strategyError : new Error(String(strategyError));
          attempt.endTime = new Date();
          attempt.duration = attempt.endTime.getTime() - attempt.startTime.getTime();

          this.emit("recovery:attempt", context, attempt);
          this.emit("strategy:applied", strategy, context.originalError, false);

          console.error(`[RecoveryManager] Strategy ${strategy} failed:`, strategyError);
        }
      }
    }

    return false;
  }

  /**
   * Apply recovery strategy
   */
  private async applyStrategy(
    strategy: RecoveryStrategyType,
    error: BotError,
    context: Record<string, unknown>
  ): Promise<boolean> {
    switch (strategy) {
      case "retry":
        return this.retryStrategy(error, context);
      case "fallback":
        return this.fallbackStrategy(error, context);
      case "reset":
        return this.resetStrategy(error, context);
      case "restart":
        return this.restartStrategy(error, context);
      case "cleanup":
        return this.cleanupStrategy(error, context);
      case "rollback":
        return this.rollbackStrategy(error, context);
      case "skip":
        return this.skipStrategy(error, context);
      case "escalate":
        return this.escalateStrategy(error, context);
      default:
        return false;
    }
  }

  /**
   * Retry strategy - retry the failed operation
   */
  private async retryStrategy(
    error: BotError,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _context: Record<string, unknown>
  ): Promise<boolean> {
    try {
      // Wait before retry
      await this.sleep(1000);

      // This would retry the original operation
      // For now, we'll simulate based on error type
      if (error instanceof CommunicationError || error instanceof ExternalServiceError) {
        // Simulate retry success for transient errors
        return Math.random() > 0.3;
      }

      return false;
    } catch (retryError) {
      console.error("[RecoveryManager] Retry strategy failed:", retryError);
      return false;
    }
  }

  /**
   * Fallback strategy - use alternative approach
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private fallbackStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      if (error instanceof AIError) {
        // Fallback to simpler AI model or pre-defined response
        console.log("[RecoveryManager] Using fallback AI response");
        return true;
      }

      if (error instanceof DeploymentError) {
        // Fallback to alternative deployment method
        console.log("[RecoveryManager] Using fallback deployment method");
        return true;
      }

      if (error instanceof ExternalServiceError) {
        // Fallback to cached data or alternative service
        console.log("[RecoveryManager] Using fallback service");
        return true;
      }

      return false;
    } catch (fallbackError) {
      console.error("[RecoveryManager] Fallback strategy failed:", fallbackError);
      return false;
    }
  }

  /**
   * Reset strategy - reset components to known good state
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private resetStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      if (error instanceof SessionError) {
        // Reset session state
        console.log("[RecoveryManager] Resetting session state");
        return true;
      }

      if (error instanceof WorkspaceError) {
        // Reset workspace to clean state
        console.log("[RecoveryManager] Resetting workspace");
        return true;
      }

      if (error instanceof DatabaseError) {
        // Reset database connection
        console.log("[RecoveryManager] Resetting database connection");
        return true;
      }

      return false;
    } catch (resetError) {
      console.error("[RecoveryManager] Reset strategy failed:", resetError);
      return false;
    }
  }

  /**
   * Restart strategy - restart failed services
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private restartStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      if (error instanceof SystemError) {
        // Restart system components
        console.log("[RecoveryManager] Restarting system components");
        return true;
      }

      return false;
    } catch (restartError) {
      console.error("[RecoveryManager] Restart strategy failed:", restartError);
      return false;
    }
  }

  /**
   * Cleanup strategy - clean up resources and retry
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private cleanupStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      if (error instanceof WorkspaceError) {
        // Clean up workspace resources
        console.log("[RecoveryManager] Cleaning up workspace resources");
        return true;
      }

      if (error instanceof SessionError) {
        // Clean up session resources
        console.log("[RecoveryManager] Cleaning up session resources");
        return true;
      }

      return false;
    } catch (cleanupError) {
      console.error("[RecoveryManager] Cleanup strategy failed:", cleanupError);
      return false;
    }
  }

  /**
   * Rollback strategy - rollback to previous state
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private rollbackStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      if (error instanceof DeploymentError) {
        // Rollback to previous deployment
        console.log("[RecoveryManager] Rolling back deployment");
        return true;
      }

      if (error instanceof GitError) {
        // Rollback git changes
        console.log("[RecoveryManager] Rolling back git changes");
        return true;
      }

      return false;
    } catch (rollbackError) {
      console.error("[RecoveryManager] Rollback strategy failed:", rollbackError);
      return false;
    }
  }

  /**
   * Skip strategy - skip the failed operation and continue
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private skipStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      // Skip non-critical operations
      if (error.severity === "low" || error.severity === "medium") {
        console.log("[RecoveryManager] Skipping non-critical operation");
        return true;
      }

      return false;
    } catch (skipError) {
      console.error("[RecoveryManager] Skip strategy failed:", skipError);
      return false;
    }
  }

  /**
   * Escalate strategy - escalate to manual intervention
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private escalateStrategy(error: BotError, _context: Record<string, unknown>): boolean {
    try {
      // Escalate critical errors
      if (error.severity === "critical") {
        console.log("[RecoveryManager] Escalating critical error for manual intervention");
        // This would notify administrators
        return true;
      }

      return false;
    } catch (escalateError) {
      console.error("[RecoveryManager] Escalate strategy failed:", escalateError);
      return false;
    }
  }

  /**
   * Get recovery strategies for error type
   */
  private getStrategiesForError(error: BotError): RecoveryStrategyType[] {
    const errorType = error.constructor.name;
    const category = error.category;

    // Check specific error type strategies
    if (this.config.strategies[errorType]) {
      return this.config.strategies[errorType];
    }

    // Check category strategies
    if (this.config.strategies[category]) {
      return this.config.strategies[category];
    }

    // Default strategies based on error characteristics
    const strategies: RecoveryStrategyType[] = [];

    if (error.retryable) {
      strategies.push("retry");
    }

    if (error.severity === "low" || error.severity === "medium") {
      strategies.push("fallback", "skip");
    }

    if (error.severity === "high" || error.severity === "critical") {
      strategies.push("reset", "cleanup", "escalate");
    }

    if (error instanceof DeploymentError || error instanceof GitError) {
      strategies.push("rollback");
    }

    if (error instanceof SystemError) {
      strategies.push("restart");
    }

    return strategies;
  }

  /**
   * Create recovery context
   */
  private createRecoveryContext(
    error: BotError,
    context: Record<string, unknown> | undefined
  ): RecoveryContext {
    return {
      id: randomUUID(),
      originalError: error,
      attempts: [],
      startTime: new Date(),
      totalDuration: 0,
      success: false,
      context,
    };
  }

  /**
   * Create recovery attempt
   */
  private createRecoveryAttempt(attempt: number, strategy: RecoveryStrategyType): RecoveryAttempt {
    return {
      id: randomUUID(),
      attempt,
      strategy,
      startTime: new Date(),
      duration: 0,
      success: false,
    };
  }

  /**
   * Update recovery time
   */
  private updateRecoveryTime(error: BotError): void {
    const errorKey = `${error.code}-${error.category}`;
    this.lastRecoveryTime.set(errorKey, Date.now());
  }

  /**
   * Update recovery statistics
   */
  private updateStats(context: RecoveryContext): void {
    this.recoveryStats.totalRecoveries++;

    if (context.success) {
      this.recoveryStats.successfulRecoveries++;
    } else {
      this.recoveryStats.failedRecoveries++;
    }

    // Update average attempts
    const totalAttempts = Array.from(this.activeRecoveries.values())
      .concat([context])
      .reduce((sum, ctx) => sum + ctx.attempts.length, 0);

    this.recoveryStats.averageAttempts = totalAttempts / this.recoveryStats.totalRecoveries;

    // Update average duration
    const totalDuration = Array.from(this.activeRecoveries.values())
      .concat([context])
      .reduce((sum, ctx) => sum + ctx.totalDuration, 0);

    this.recoveryStats.averageDuration = totalDuration / this.recoveryStats.totalRecoveries;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get recovery statistics
   */
  getStats(): typeof this.recoveryStats {
    return { ...this.recoveryStats };
  }

  /**
   * Get active recovery contexts
   */
  getActiveRecoveries(): RecoveryContext[] {
    return Array.from(this.activeRecoveries.values());
  }

  /**
   * Get recovery context by ID
   */
  getRecoveryContext(id: string): RecoveryContext | undefined {
    return this.activeRecoveries.get(id);
  }

  /**
   * Cancel recovery
   */
  cancel(id: string): boolean {
    const context = this.activeRecoveries.get(id);
    if (context) {
      context.success = false;
      context.endTime = new Date();
      context.totalDuration = context.endTime.getTime() - context.startTime.getTime();

      this.activeRecoveries.delete(id);
      this.emit("recovery:failed", context, new Error("Recovery cancelled"));

      return true;
    }
    return false;
  }

  /**
   * Cancel all active recoveries
   */
  cancelAll(): void {
    for (const id of this.activeRecoveries.keys()) {
      this.cancel(id);
    }
  }

  /**
   * Add custom recovery handler
   */
  addCustomHandler(
    errorType: string,
    handler: (error: BotError, context: Record<string, unknown>) => Promise<boolean>
  ): void {
    this.config.customHandlers[errorType] = handler;
  }

  /**
   * Remove custom recovery handler
   */
  removeCustomHandler(errorType: string): void {
    delete this.config.customHandlers[errorType];
  }

  /**
   * Get recovery configuration
   */
  getConfig(): RecoveryConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<RecoveryConfig>): void {
    this.config = { ...this.config, ...config };
    console.log("[RecoveryManager] Configuration updated:", this.config);
  }

  /**
   * Shutdown recovery manager
   */
  shutdown(): void {
    console.log("[RecoveryManager] Shutting down...");

    // Cancel all active recoveries
    this.cancelAll();

    console.log("[RecoveryManager] Shutdown complete");
  }
}

/**
 * Default recovery configuration
 */
export const DEFAULT_RECOVERY_CONFIG: RecoveryConfig = {
  maxRecoveryAttempts: 3,
  recoveryTimeout: 30000,
  enableAutoRecovery: true,
  cooldownPeriod: 60000,
  strategies: {
    // Session errors
    SessionError: ["retry", "reset", "cleanup"],
    SessionTimeoutError: ["retry", "reset"],
    SessionCreationError: ["retry", "cleanup"],

    // Workspace errors
    WorkspaceError: ["reset", "cleanup", "retry"],
    WorkspaceCreationError: ["cleanup", "retry"],

    // Communication errors
    CommunicationError: ["retry", "fallback"],
    MessageSendError: ["retry", "fallback"],

    // AI errors
    AIError: ["retry", "fallback"],
    AIServiceUnavailableError: ["retry", "fallback"],
    AIRateLimitError: ["retry", "fallback"],

    // Deployment errors
    DeploymentError: ["retry", "rollback", "fallback"],
    BuildError: ["retry", "cleanup", "rollback"],

    // System errors
    SystemError: ["restart", "reset", "escalate"],
    DatabaseError: ["retry", "reset"],
    ExternalServiceError: ["retry", "fallback"],
    RateLimitError: ["retry", "fallback"],

    // Git errors
    GitError: ["retry", "rollback", "cleanup"],
    GitCloneError: ["retry", "cleanup"],
    GitAuthenticationError: ["retry", "escalate"],
  },
  customHandlers: {},
};

/**
 * Create recovery manager instance
 */
export function createRecoveryManager(
  config: RecoveryConfig = DEFAULT_RECOVERY_CONFIG
): RecoveryManager {
  return new RecoveryManager(config);
}
