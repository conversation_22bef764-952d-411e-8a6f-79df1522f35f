/**
 * Retry Manager
 * Implements intelligent retry logic with exponential backoff and jitter
 */

import { EventEmitter } from 'events';
import { randomUUID } from 'crypto';

import { BotError } from '@whatsite-bot/core';

/**
 * Retry strategy types
 */
export type RetryStrategy = 'exponential' | 'linear' | 'fixed' | 'custom';

/**
 * Retry configuration
 */
export interface RetryConfig {
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Base delay in milliseconds */
  baseDelay: number;
  /** Maximum delay in milliseconds */
  maxDelay: number;
  /** Backoff multiplier for exponential strategy */
  backoffMultiplier: number;
  /** Enable jitter to prevent thundering herd */
  jitter: boolean;
  /** Jitter factor (0-1) */
  jitterFactor: number;
  /** Retry strategy */
  strategy: RetryStrategy;
  /** Custom delay function for custom strategy */
  customDelayFn?: (attempt: number, baseDelay: number) => number;
  /** Function to determine if error is retryable */
  isRetryable?: (error: Error) => boolean;
  /** Timeout for entire retry sequence */
  totalTimeout?: number;
}

/**
 * Retry attempt information
 */
export interface RetryAttempt {
  /** Attempt number (1-based) */
  attempt: number;
  /** Delay before this attempt */
  delay: number;
  /** Timestamp of attempt */
  timestamp: Date;
  /** Error from this attempt */
  error?: Error;
  /** Success flag */
  success: boolean;
  /** Duration of attempt */
  duration: number;
}

/**
 * Retry context
 */
export interface RetryContext {
  /** Retry operation ID */
  id: string;
  /** Operation name */
  operation: string;
  /** Service name */
  service?: string;
  /** Retry attempts */
  attempts: RetryAttempt[];
  /** Start time */
  startTime: Date;
  /** End time */
  endTime?: Date;
  /** Total duration */
  totalDuration: number;
  /** Final result */
  result?: unknown;
  /** Final error */
  finalError?: Error;
  /** Success flag */
  success: boolean;
  /** Retry configuration used */
  config: RetryConfig;
}

/**
 * Retry manager events
 */
export interface RetryManagerEvents {
  'retry:started': (context: RetryContext) => void;
  'retry:attempt': (context: RetryContext, attempt: RetryAttempt) => void;
  'retry:success': (context: RetryContext, result: unknown) => void;
  'retry:failed': (context: RetryContext, error: Error) => void;
  'retry:timeout': (context: RetryContext) => void;
  'retry:exhausted': (context: RetryContext) => void;
}

/**
 * Retry Manager Implementation
 */
export class RetryManager extends EventEmitter {
  private config: RetryConfig;
  private activeRetries: Map<string, RetryContext> = new Map();
  private retryStats: {
    totalRetries: number;
    successfulRetries: number;
    failedRetries: number;
    averageAttempts: number;
    averageDuration: number;
  };

  constructor(config: RetryConfig) {
    super();
    this.config = config;
    this.retryStats = {
      totalRetries: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageAttempts: 0,
      averageDuration: 0,
    };

    console.log('[RetryManager] Initialized with configuration:', config);
  }

  /**
   * Execute operation with retry logic
   */
  async execute<T>(
    operation: () => Promise<T>,
    operationName: string,
    config?: Partial<RetryConfig>
  ): Promise<T> {
    const effectiveConfig = { ...this.config, ...config };
    const context = this.createRetryContext(operationName, effectiveConfig);

    this.activeRetries.set(context.id, context);
    this.emit('retry:started', context);

    try {
      const result = await this.executeWithRetry(operation, context);

      context.success = true;
      context.result = result;
      context.endTime = new Date();
      context.totalDuration = context.endTime.getTime() - context.startTime.getTime();

      this.updateStats(context);
      this.emit('retry:success', context, result);

      return result;
    } catch (error) {
      context.success = false;
      context.finalError = error instanceof Error ? error : new Error(String(error));
      context.endTime = new Date();
      context.totalDuration = context.endTime.getTime() - context.startTime.getTime();

      this.updateStats(context);
      this.emit('retry:failed', context, context.finalError);

      throw context.finalError;
    } finally {
      this.activeRetries.delete(context.id);
    }
  }

  /**
   * Execute operation with retry attempts
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: RetryContext
  ): Promise<T> {
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= context.config.maxRetries + 1; attempt++) {
      // Check total timeout
      if (context.config.totalTimeout) {
        const elapsed = Date.now() - context.startTime.getTime();
        if (elapsed >= context.config.totalTimeout) {
          this.emit('retry:timeout', context);
          throw new Error(`Retry sequence timed out after ${elapsed}ms`);
        }
      }

      const delay = attempt === 1 ? 0 : this.calculateDelay(attempt - 1, context.config);

      // Wait before attempt (except first attempt)
      if (delay > 0) {
        await this.sleep(delay);
      }

      const attemptStart = Date.now();
      const attemptInfo: RetryAttempt = {
        attempt,
        delay,
        timestamp: new Date(),
        success: false,
        duration: 0,
      };

      try {
        const result = await operation();

        attemptInfo.success = true;
        attemptInfo.duration = Date.now() - attemptStart;
        context.attempts.push(attemptInfo);

        this.emit('retry:attempt', context, attemptInfo);

        console.log(
          `[RetryManager] Operation succeeded on attempt ${attempt}: ${context.operation}`
        );
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        attemptInfo.error = lastError;
        attemptInfo.duration = Date.now() - attemptStart;
        context.attempts.push(attemptInfo);

        this.emit('retry:attempt', context, attemptInfo);

        console.log(
          `[RetryManager] Attempt ${attempt} failed for ${context.operation}: ${lastError.message}`
        );

        // Check if error is retryable
        if (!this.isRetryable(lastError, context.config)) {
          console.log(
            `[RetryManager] Error is not retryable, stopping retry sequence: ${context.operation}`
          );
          break;
        }

        // Check if this was the last attempt
        if (attempt > context.config.maxRetries) {
          console.log(`[RetryManager] Max retries exceeded for ${context.operation}`);
          break;
        }
      }
    }

    this.emit('retry:exhausted', context);
    throw lastError || new Error('Unknown error during retry sequence');
  }

  /**
   * Calculate delay for retry attempt
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    let delay: number;

    switch (config.strategy) {
      case 'exponential':
        delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelay
        );
        break;

      case 'linear':
        delay = Math.min(config.baseDelay * attempt, config.maxDelay);
        break;

      case 'fixed':
        delay = config.baseDelay;
        break;

      case 'custom':
        if (config.customDelayFn) {
          delay = Math.min(config.customDelayFn(attempt, config.baseDelay), config.maxDelay);
        } else {
          delay = config.baseDelay;
        }
        break;

      default:
        delay = config.baseDelay;
    }

    // Apply jitter if enabled
    if (config.jitter) {
      const jitterAmount = delay * config.jitterFactor * Math.random();
      delay += jitterAmount;
    }

    return Math.min(delay, config.maxDelay);
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(error: Error, config: RetryConfig): boolean {
    // Use custom retry check if provided
    if (config.isRetryable) {
      return config.isRetryable(error);
    }

    // Default retryable check
    if (error instanceof BotError) {
      return error.retryable;
    }

    // Check for common retryable error patterns
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /rate limit/i,
      /service unavailable/i,
      /internal server error/i,
      /bad gateway/i,
      /gateway timeout/i,
      /temporary/i,
      /ECONNRESET/i,
      /ETIMEDOUT/i,
      /ENOTFOUND/i,
    ];

    return retryablePatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Create retry context
   */
  private createRetryContext(operation: string, config: RetryConfig): RetryContext {
    return {
      id: randomUUID(),
      operation,
      attempts: [],
      startTime: new Date(),
      totalDuration: 0,
      success: false,
      config,
    };
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update retry statistics
   */
  private updateStats(context: RetryContext): void {
    this.retryStats.totalRetries++;

    if (context.success) {
      this.retryStats.successfulRetries++;
    } else {
      this.retryStats.failedRetries++;
    }

    // Update average attempts
    const totalAttempts = Array.from(this.activeRetries.values())
      .concat([context])
      .reduce((sum, ctx) => sum + ctx.attempts.length, 0);

    this.retryStats.averageAttempts = totalAttempts / this.retryStats.totalRetries;

    // Update average duration
    const totalDuration = Array.from(this.activeRetries.values())
      .concat([context])
      .reduce((sum, ctx) => sum + ctx.totalDuration, 0);

    this.retryStats.averageDuration = totalDuration / this.retryStats.totalRetries;
  }

  /**
   * Get retry statistics
   */
  getStats(): typeof this.retryStats {
    return { ...this.retryStats };
  }

  /**
   * Get active retry contexts
   */
  getActiveRetries(): RetryContext[] {
    return Array.from(this.activeRetries.values());
  }

  /**
   * Get retry context by ID
   */
  getRetryContext(id: string): RetryContext | undefined {
    return this.activeRetries.get(id);
  }

  /**
   * Cancel retry sequence
   */
  cancel(id: string): boolean {
    const context = this.activeRetries.get(id);
    if (context) {
      context.finalError = new Error('Retry sequence cancelled');
      context.endTime = new Date();
      context.totalDuration = context.endTime.getTime() - context.startTime.getTime();

      this.activeRetries.delete(id);
      this.emit('retry:failed', context, context.finalError);

      return true;
    }
    return false;
  }

  /**
   * Cancel all active retries
   */
  cancelAll(): void {
    for (const id of this.activeRetries.keys()) {
      this.cancel(id);
    }
  }

  /**
   * Create retry policy for specific scenarios
   */
  createPolicy(scenario: string): Partial<RetryConfig> {
    switch (scenario) {
      case 'api_call':
        return {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 30000,
          strategy: 'exponential',
          backoffMultiplier: 2,
          jitter: true,
          totalTimeout: 60000,
        };

      case 'database':
        return {
          maxRetries: 5,
          baseDelay: 500,
          maxDelay: 10000,
          strategy: 'exponential',
          backoffMultiplier: 1.5,
          jitter: true,
          totalTimeout: 30000,
        };

      case 'file_operation':
        return {
          maxRetries: 3,
          baseDelay: 100,
          maxDelay: 1000,
          strategy: 'linear',
          jitter: false,
          totalTimeout: 10000,
        };

      case 'network':
        return {
          maxRetries: 5,
          baseDelay: 2000,
          maxDelay: 60000,
          strategy: 'exponential',
          backoffMultiplier: 2,
          jitter: true,
          totalTimeout: 300000,
        };

      default:
        return {};
    }
  }

  /**
   * Get retry configuration
   */
  getConfig(): RetryConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<RetryConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('[RetryManager] Configuration updated:', this.config);
  }

  /**
   * Shutdown retry manager
   */
  shutdown(): void {
    console.log('[RetryManager] Shutting down...');

    // Cancel all active retries
    this.cancelAll();

    console.log('[RetryManager] Shutdown complete');
  }
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2,
  jitter: true,
  jitterFactor: 0.1,
  strategy: 'exponential',
  totalTimeout: 60000,
};

/**
 * Create retry manager instance
 */
export function createRetryManager(config: RetryConfig = DEFAULT_RETRY_CONFIG): RetryManager {
  return new RetryManager(config);
}
