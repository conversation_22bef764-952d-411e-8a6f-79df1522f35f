import { createServer } from "http";

import { WebSocketServer } from "ws";
import express, { Request, Response, NextFunction } from "express";

import { <PERSON>hookHandler, createWebhookHandler } from "../communication/webhook-handler.js";
import {
  StatusCommunicator,
  createStatusCommunicator,
} from "../communication/status-communicator.js";
import { WebhookServer, createWebhookServer } from "../communication/webhook-server.js";
import { HealthCheckService, createHealthCheckService } from "../monitoring/health-checks.js";
import {
  MonitoringService,
  createMonitoringService,
  DEFAULT_MONITORING_CONFIG,
} from "../monitoring/monitoring.js";
import { SessionManager } from "../session/session-manager.js";
import { WebsiteManager } from "../website/website-manager.js";

import { BotEngine, createBotEngine } from "./bot-engine.js";
import type { BotEngineConfig } from "./bot-engine.js";

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || "0.0.0.0";

// Bot Engine Configuration
const botEngineConfig: BotEngineConfig = {
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || "",
    authToken: process.env.TWILIO_AUTH_TOKEN || "",
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || "",
    webhookUrl: process.env.TWILIO_WEBHOOK_URL || `http://localhost:${PORT}/webhook`,
  },
  google: {
    apiKey: process.env.GOOGLE_API_KEY || "",
    model: process.env.GOOGLE_MODEL || "gemini-pro",
    maxTokens: parseInt(process.env.GOOGLE_MAX_TOKENS || "4096"),
  },
  github: {
    token: process.env.GITHUB_TOKEN || "",
    organization: process.env.GITHUB_ORGANIZATION,
  },
  vercel: {
    token: process.env.VERCEL_TOKEN || "",
    teamId: process.env.VERCEL_TEAM_ID,
  },
  server: {
    host: HOST,
    port: parseInt(PORT.toString()),
    webhookPath: "/webhook",
  },
  workspace: {
    baseDir: process.env.WORKSPACE_BASE_DIR || "/tmp/bot-workspaces",
    maxWorkspacesPerUser: parseInt(process.env.MAX_WORKSPACES_PER_USER || "5"),
    timeout: parseInt(process.env.WORKSPACE_TIMEOUT || "1800000"), // 30 minutes
  },
  features: {
    enableWebsiteCreation: process.env.ENABLE_WEBSITE_CREATION !== "false",
    enableWebsiteEditing: process.env.ENABLE_WEBSITE_EDITING !== "false",
    enableRepositoryImport: process.env.ENABLE_REPOSITORY_IMPORT !== "false",
    enableDeployment: process.env.ENABLE_DEPLOYMENT !== "false",
    enableGitIntegration: process.env.ENABLE_GIT_INTEGRATION !== "false",
    enableStatusUpdates: process.env.ENABLE_STATUS_UPDATES !== "false",
    enableErrorRecovery: process.env.ENABLE_ERROR_RECOVERY !== "false",
  },
  performance: {
    enableOptimizations: process.env.PERFORMANCE_OPTIMIZATIONS_ENABLED !== "false",
    enableCaching: process.env.PERFORMANCE_CACHING_ENABLED !== "false",
    enableConnectionPooling: process.env.PERFORMANCE_CONNECTION_POOLING_ENABLED !== "false",
    enableMetrics: process.env.PERFORMANCE_METRICS_ENABLED !== "false",
    enableResourceManagement: process.env.PERFORMANCE_RESOURCE_MANAGEMENT_ENABLED !== "false",
    cacheConfig: {
      maxSize: parseInt(process.env.CACHE_MAX_SIZE || "1000"),
      ttl: parseInt(process.env.CACHE_TTL || "300000"),
      evictionPolicy: (process.env.CACHE_EVICTION_POLICY as "lru" | "lfu" | "fifo") || "lru",
    },
    connectionPoolConfig: {
      maxConnections: parseInt(process.env.CONNECTION_POOL_MAX_CONNECTIONS || "100"),
      idleTimeout: parseInt(process.env.CONNECTION_POOL_IDLE_TIMEOUT || "30000"),
      requestTimeout: parseInt(process.env.CONNECTION_POOL_REQUEST_TIMEOUT || "30000"),
    },
    metricsConfig: {
      enablePrometheus: process.env.METRICS_PROMETHEUS_ENABLED !== "false",
      metricsInterval: parseInt(process.env.METRICS_INTERVAL || "60000"),
    },
    resourceConfig: {
      maxMemoryUsage: parseInt(process.env.RESOURCE_MAX_MEMORY_USAGE || "536870912"), // 512MB
      gcThreshold: parseInt(process.env.RESOURCE_GC_THRESHOLD || "104857600"), // 100MB
      cleanupInterval: parseInt(process.env.RESOURCE_CLEANUP_INTERVAL || "60000"),
    },
  },
};

// Initialize components
let botEngine: BotEngine;
let webhookHandler: WebhookHandler;
let statusCommunicator: StatusCommunicator;
let webhookServer: WebhookServer;
let healthCheckService: HealthCheckService;
let monitoringService: MonitoringService;
let sessionManager: SessionManager;
let websiteManager: WebsiteManager;

// Middleware
app.use(express.json());

// Production health check endpoints
app.get("/health", (req: Request, res: Response) => {
  if (healthCheckService) {
    healthCheckService.healthCheck(req, res).catch(console.error);
  } else {
    res.status(503).json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: "Health check service not initialized",
    });
  }
});

app.get("/ready", (req: Request, res: Response) => {
  if (healthCheckService) {
    healthCheckService.readinessCheck(req, res).catch(console.error);
  } else {
    res.status(503).json({
      status: "not_ready",
      timestamp: new Date().toISOString(),
      error: "Health check service not initialized",
    });
  }
});

app.get("/startup", (req: Request, res: Response) => {
  if (healthCheckService) {
    healthCheckService.startupCheck(req, res);
  } else {
    res.status(503).json({
      status: "failed",
      timestamp: new Date().toISOString(),
      error: "Health check service not initialized",
    });
  }
});

app.get("/live", (req: Request, res: Response) => {
  if (healthCheckService) {
    healthCheckService.livenessCheck(req, res);
  } else {
    res.status(503).json({
      status: "dead",
      timestamp: new Date().toISOString(),
      error: "Health check service not initialized",
    });
  }
});

// Production metrics endpoint
app.get("/metrics", (req: Request, res: Response) => {
  try {
    if (monitoringService) {
      const acceptHeader = req.headers.accept || "";
      if (acceptHeader.includes("text/plain")) {
        // Prometheus format
        const prometheusMetrics = monitoringService.getPrometheusMetrics();
        res.set("Content-Type", "text/plain");
        res.send(prometheusMetrics);
      } else {
        // JSON format
        const metrics = monitoringService.getMetrics();
        res.json(metrics);
      }
    } else {
      // Fallback metrics
      const metrics = {
        websocket_connections: wss.clients.size,
        memory_usage: process.memoryUsage(),
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        error: "Monitoring service not initialized",
      };
      res.json(metrics);
    }
  } catch (error) {
    console.error("Metrics error:", error);
    res.status(500).json({
      error: "Failed to get metrics",
      timestamp: new Date().toISOString(),
    });
  }
});

// WebSocket connection handling (keeping for real-time communication)
wss.on("connection", ws => {
  console.log("New WebSocket connection established");

  ws.on("message", message => {
    const messageStr =
      typeof message === "string"
        ? message
        : message &&
            typeof message === "object" &&
            message !== null &&
            "toString" in message &&
            typeof (message as { toString: () => string }).toString === "function"
          ? (message as { toString: () => string }).toString()
          : JSON.stringify(message);
    console.log("Received:", messageStr);
    try {
      const data = JSON.parse(messageStr) as Record<string, unknown>;

      // Handle WebSocket messages for real-time communication
      if (data.type === "status_subscribe" && data.sessionId) {
        // Subscribe to status updates for a specific session
        const statusHandler = (update: Record<string, unknown>) => {
          if (update.sessionId === data.sessionId) {
            ws.send(
              JSON.stringify({
                type: "status_update",
                sessionId: update.sessionId,
                status: update.message,
                data: update.data,
                timestamp: new Date().toISOString(),
              })
            );
          }
        };

        if (statusCommunicator) {
          statusCommunicator.on("status:sent", statusHandler);
        }
      }

      // Generic response
      ws.send(
        JSON.stringify({
          type: "response",
          data: `Received: ${typeof data.type === "string" ? data.type : "unknown"}`,
          timestamp: new Date().toISOString(),
        })
      );
    } catch (error) {
      console.error("Error processing WebSocket message:", error);
      ws.send(
        JSON.stringify({
          type: "error",
          message: "Invalid message format",
          timestamp: new Date().toISOString(),
        })
      );
    }
  });

  ws.on("close", () => {
    console.log("WebSocket connection closed");
  });

  ws.on("error", error => {
    console.error("WebSocket error:", error);
  });

  // Send initial connection message
  ws.send(
    JSON.stringify({
      type: "connected",
      timestamp: new Date().toISOString(),
    })
  );
});

// Error handling middleware
// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error("Express error:", err);
  res.status(500).json({
    error: "Internal server error",
    timestamp: new Date().toISOString(),
  });
});

// Initialize and start services
function initializeServices(): void {
  try {
    console.log("[Main] Initializing services...");

    // Record initialization steps for startup monitoring
    const recordStep = (
      step: string,
      status: "completed" | "in_progress" | "failed",
      duration?: number,
      error?: string
    ) => {
      if (healthCheckService) {
        healthCheckService.recordInitializationStep(step, status, duration, error);
      }
    };

    // Initialize bot engine
    recordStep("bot_engine_init", "in_progress");
    const botEngineStart = Date.now();
    botEngine = createBotEngine(botEngineConfig);
    recordStep("bot_engine_init", "completed", Date.now() - botEngineStart);

    // Initialize session manager
    recordStep("session_manager_init", "in_progress");
    const sessionManagerStart = Date.now();
    sessionManager = new SessionManager(botEngineConfig.workspace.baseDir);
    recordStep("session_manager_init", "completed", Date.now() - sessionManagerStart);

    // Initialize website manager
    recordStep("website_manager_init", "in_progress");
    const websiteManagerStart = Date.now();
    websiteManager = new WebsiteManager(
      {
        github: {
          token: botEngineConfig.github.token,
          organization: botEngineConfig.github.organization,
          baseUrl: "https://api.github.com",
        },
        vercel: {
          token: botEngineConfig.vercel.token,
          teamId: botEngineConfig.vercel.teamId,
        },
        workspaceDir: botEngineConfig.workspace.baseDir,
        sessionTimeout: botEngineConfig.workspace.timeout,
        enableGitIntegration: botEngineConfig.features.enableGitIntegration,
        enableDeployment: botEngineConfig.features.enableDeployment,
      },
      {
        createWorkspace: () =>
          Promise.resolve({
            id: "",
            path: "",
            isGitWorktree: false,
            type: "temporary" as const,
            user: { phoneNumber: "" },
            createdAt: new Date(),
            lastAccessedAt: new Date(),
            isActive: false,
          }),
        getWorkspace: () => Promise.resolve(null),
        listWorkspaceFiles: () => Promise.resolve([]),
        readWorkspaceFile: () => Promise.resolve(""),
        writeWorkspaceFile: () => Promise.resolve(),
        cleanupWorkspace: () => Promise.resolve(),
      }, // WorkspaceManager placeholder
      {
        initialize: () => {},
        generateCode: () => Promise.resolve({ success: false }),
        updateCode: () => Promise.resolve(""),
      }, // GeminiRunner placeholder
      undefined // StatusMessenger - optional parameter
    );
    recordStep("website_manager_init", "completed", Date.now() - websiteManagerStart);

    // Initialize health check service
    recordStep("health_check_init", "in_progress");
    const healthCheckStart = Date.now();
    healthCheckService = createHealthCheckService(botEngine, sessionManager, websiteManager);
    recordStep("health_check_init", "completed", Date.now() - healthCheckStart);

    // Initialize monitoring service
    recordStep("monitoring_init", "in_progress");
    const monitoringStart = Date.now();
    const monitoringConfig = {
      ...DEFAULT_MONITORING_CONFIG,
      enabled: process.env.METRICS_ENABLED === "true",
      logLevel: (process.env.LOG_LEVEL as "debug" | "info" | "warn" | "error") || "info",
      alertWebhook: process.env.ALERT_WEBHOOK_URL,
    };
    monitoringService = createMonitoringService(
      monitoringConfig,
      botEngine,
      sessionManager,
      websiteManager
    );
    recordStep("monitoring_init", "completed", Date.now() - monitoringStart);

    // Initialize status communicator
    recordStep("status_communicator_init", "in_progress");
    const statusCommStart = Date.now();
    statusCommunicator = createStatusCommunicator({
      vercelWebhookUrl: process.env.VERCEL_WEBHOOK_URL,
      vercelApiKey: process.env.VERCEL_API_KEY,
      twilio: {
        accountSid: botEngineConfig.twilio.accountSid,
        authToken: botEngineConfig.twilio.authToken,
        phoneNumber: botEngineConfig.twilio.phoneNumber,
      },
      enableWhatsAppUpdates: botEngineConfig.features.enableStatusUpdates,
      enableVercelUpdates: !!process.env.VERCEL_WEBHOOK_URL,
      maxRetries: 3,
      retryDelay: 2000,
      enableBatching: false,
      batchSize: 5,
      batchTimeout: 5000,
    });
    recordStep("status_communicator_init", "completed", Date.now() - statusCommStart);

    // Initialize webhook handler
    recordStep("webhook_handler_init", "in_progress");
    const webhookHandlerStart = Date.now();
    webhookHandler = createWebhookHandler(botEngine, {
      maxProcessingTime: 300000, // 5 minutes
      maxRetries: 3,
      retryDelay: 5000,
      enableAsyncProcessing: true,
      enableEventPersistence: true,
    });
    recordStep("webhook_handler_init", "completed", Date.now() - webhookHandlerStart);

    // Initialize webhook server
    recordStep("webhook_server_init", "in_progress");
    const webhookServerStart = Date.now();
    webhookServer = createWebhookServer(botEngine, webhookHandler, statusCommunicator, {
      port: parseInt(PORT.toString()),
      host: HOST,
      basePath: "/webhook",
      enableCors: true,
      enableLogging: true,
      enableRateLimit: true,
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100,
        message: "Too many requests from this IP, please try again later.",
      },
      auth: {
        apiKey: process.env.FLY_IO_API_KEY || process.env.WEBHOOK_API_KEY,
        secretKey: process.env.WEBHOOK_SECRET_KEY,
        requireSignature: process.env.WEBHOOK_REQUIRE_SIGNATURE === "true",
        enableRateLimit: true,
      },
      enableHealthCheck: true,
      enableMetrics: true,
      requestTimeout: 30000,
      bodySizeLimit: "10mb",
    });
    recordStep("webhook_server_init", "completed", Date.now() - webhookServerStart);

    // Set up event listeners for cross-component communication
    recordStep("event_listeners_setup", "in_progress");
    const eventListenersStart = Date.now();
    setupEventListeners();
    recordStep("event_listeners_setup", "completed", Date.now() - eventListenersStart);

    // Start monitoring service
    recordStep("monitoring_start", "in_progress");
    const monitoringStartTime = Date.now();
    monitoringService.start();
    recordStep("monitoring_start", "completed", Date.now() - monitoringStartTime);

    console.log("[Main] Services initialized successfully");
  } catch (error) {
    console.error("[Main] Failed to initialize services:", error);
    if (healthCheckService) {
      healthCheckService.recordInitializationStep(
        "initialization",
        "failed",
        0,
        error instanceof Error ? error.message : "Unknown error"
      );
    }
    throw error;
  }
}

// Set up event listeners for cross-component communication
function setupEventListeners(): void {
  // Forward webhook handler events to status communicator
  webhookHandler.on("webhook:received", (event: Record<string, unknown>, processingId: string) => {
    const eventType = typeof event.type === "string" ? event.type : "unknown";
    console.log(`[Main] Webhook received: ${eventType} (${processingId})`);
  });

  webhookHandler.on(
    "webhook:completed",
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (event: Record<string, unknown>, processingId: string, _response: unknown) => {
      const eventType = typeof event.type === "string" ? event.type : "unknown";
      console.log(`[Main] Webhook completed: ${eventType} (${processingId})`);
    }
  );

  webhookHandler.on(
    "webhook:failed",
    (event: Record<string, unknown>, processingId: string, error: Error) => {
      const eventType = typeof event.type === "string" ? event.type : "unknown";
      console.error(`[Main] Webhook failed: ${eventType} (${processingId}):`, error);
    }
  );

  // Forward status updates to WebSocket clients
  statusCommunicator.on("status:sent", (update: Record<string, unknown>) => {
    wss.clients.forEach(client => {
      if (client.readyState === client.OPEN) {
        client.send(
          JSON.stringify({
            type: "status_update",
            sessionId: typeof update.sessionId === "string" ? update.sessionId : "",
            status: typeof update.message === "string" ? update.message : "",
            data: update.data,
            timestamp:
              typeof update.timestamp === "object" &&
              update.timestamp !== null &&
              typeof (update.timestamp as Record<string, unknown>).toISOString === "function"
                ? ((update.timestamp as Record<string, unknown>).toISOString as () => string)()
                : new Date().toISOString(),
          })
        );
      }
    });
  });

  // Forward bot engine events
  botEngine.on(
    "session:started",
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (sessionId: string, user: Record<string, unknown>, _operation: unknown) => {
      const phoneNumber = typeof user.phoneNumber === "string" ? user.phoneNumber : "unknown";
      console.log(`[Main] Session started: ${sessionId} for ${phoneNumber}`);
    }
  );

  botEngine.on(
    "session:completed",
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (sessionId: string, user: Record<string, unknown>, _result: unknown) => {
      const phoneNumber = typeof user.phoneNumber === "string" ? user.phoneNumber : "unknown";
      console.log(`[Main] Session completed: ${sessionId} for ${phoneNumber}`);
    }
  );

  botEngine.on("error", (error: Error, context: Record<string, unknown>) => {
    console.error("[Main] Bot engine error:", error, context);
  });
}

// Start server
async function startServer(): Promise<void> {
  try {
    // Initialize services
    initializeServices();

    // Start bot engine
    await botEngine.start();

    // Start webhook server
    await webhookServer.start();

    // Start legacy HTTP server for WebSocket and compatibility endpoints
    await new Promise<void>((resolve, reject) => {
      server.listen(parseInt(PORT.toString()) + 1, HOST, (error?: Error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });

    // Record successful startup
    if (healthCheckService) {
      healthCheckService.recordInitializationStep("server_start", "completed", Date.now());
    }

    console.log(`[Main] Bot engine server running on port ${PORT}`);
    console.log(`[Main] WebSocket server running on port ${parseInt(PORT.toString()) + 1}`);
    console.log(`[Main] Webhook endpoints: http://${HOST}:${PORT}/webhook`);
    console.log(`[Main] Health check: http://${HOST}:${PORT}/health`);
    console.log(`[Main] Ready check: http://${HOST}:${PORT}/ready`);
    console.log(`[Main] Startup check: http://${HOST}:${PORT}/startup`);
    console.log(`[Main] Liveness check: http://${HOST}:${PORT}/live`);
    console.log(`[Main] Metrics: http://${HOST}:${PORT}/metrics`);

    // Set up request monitoring middleware
    if (monitoringService) {
      app.use((req, res, next) => {
        const start = Date.now();
        monitoringService.setGauge("http_requests_in_flight", 1);

        res.on("finish", () => {
          const duration = Date.now() - start;
          monitoringService.recordRequestDuration(duration, req.method, res.statusCode, req.path);
          monitoringService.setGauge("http_requests_in_flight", -1);
        });

        next();
      });
    }
  } catch (error) {
    console.error("[Main] Failed to start server:", error);
    if (healthCheckService) {
      healthCheckService.recordInitializationStep(
        "server_start",
        "failed",
        0,
        error instanceof Error ? error.message : "Unknown error"
      );
    }
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async () => {
  console.log("[Main] Received shutdown signal, shutting down gracefully");

  try {
    // Close WebSocket connections
    wss.clients.forEach(client => {
      client.close();
    });

    // Stop webhook server
    if (webhookServer) {
      await webhookServer.stop();
    }

    // Stop monitoring service
    if (monitoringService) {
      monitoringService.stop();
    }

    // Stop bot engine
    if (botEngine) {
      await botEngine.stop();
    }

    // Shutdown status communicator
    if (statusCommunicator) {
      await statusCommunicator.shutdown();
    }

    // Shutdown webhook handler
    if (webhookHandler) {
      webhookHandler.shutdown();
    }

    // Close HTTP server
    server.close(() => {
      console.log("[Main] HTTP server closed");
      process.exit(0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
      console.error("[Main] Could not close connections in time, forcefully shutting down");
      process.exit(1);
    }, 10000);
  } catch (error) {
    console.error("[Main] Error during shutdown:", error);
    process.exit(1);
  }
};

process.on("SIGTERM", () => {
  gracefulShutdown().catch(console.error);
});
process.on("SIGINT", () => {
  gracefulShutdown().catch(console.error);
});

// Handle uncaught exceptions
process.on("uncaughtException", error => {
  console.error("[Main] Uncaught exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("[Main] Unhandled rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Start the application
startServer().catch(error => {
  console.error("[Main] Failed to start application:", error);
  process.exit(1);
});

// Export the main components for use by other modules
export { BotEngine, createBotEngine };
export { WebhookHandler, createWebhookHandler };
export { StatusCommunicator, createStatusCommunicator };
export { WebhookServer, createWebhookServer };
export type { BotEngineConfig };
