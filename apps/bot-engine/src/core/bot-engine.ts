/**
 * <PERSON><PERSON><PERSON><PERSON>ine - Main orchestrator for WhatsApp Website Bot
 * Coordinates SessionManager, WebsiteManager, WorkspaceManager, and GeminiRunner
 * Based on EdgeWorker patterns from <PERSON> adapted for WhatsApp bot operations
 */

import { EventEmitter } from "events";
import { randomUUID } from "crypto";
import { createServer, IncomingMessage, ServerResponse } from "http";
import { parse } from "url";

import twilio from "twilio";
import type {
  User,
  WhatsAppMessage,
  ProjectData,
  IntentClassification,
  RepositoryInfo,
  StatusMessageData,
  WebhookEvent,
  WebhookResponse,
  MessageReceivedEvent,
  SessionStartedEvent,
  SessionCompletedEvent,
  SessionFailedEvent,
  SystemErrorEvent,
  WebhookHandler,
  WebhookEventType,
  SessionOperation,
  WorkspaceManager as IWorkspaceManager,
} from "@whatsite-bot/core";
import {
  isMessageReceivedEvent,
  isSessionStartedEvent,
  isSessionCompletedEvent,
  isSessionFailedEvent,
  isSystemErrorEvent,
} from "@whatsite-bot/core";
import { WorkspaceManager } from "@whatsite-bot/workspace-manager";
import { GeminiRunner } from "@whatsite-bot/gemini-runner";

import { PerformanceManager } from "../performance/performance-manager.js";
import { ConnectionPool } from "../performance/connection-pool.js";
import { CacheService } from "../performance/cache-service.js";
import { ResourceManager } from "../performance/resource-manager.js";
import { PerformanceMetrics } from "../monitoring/performance-metrics.js";
import { MemoryOptimizer, memoize } from "../utils/optimization-utils.js";
import { SessionManager, WebsiteSession } from "../session/session-manager.js";
import { WebsiteManager } from "../website/website-manager.js";
import { StatusMessenger, createStatusMessenger } from "../communication/status-messenger.js";
import {
  ErrorHandlerService,
  createErrorHandler,
  DEFAULT_ERROR_HANDLER_CONFIG,
} from "../error-handling/error-handler.js";

/**
 * BotEngine configuration
 */
export interface BotEngineConfig {
  /** Twilio configuration */
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
    webhookUrl: string;
  };
  /** Google/Gemini configuration */
  google: {
    apiKey: string;
    model: string;
    maxTokens: number;
  };
  /** GitHub configuration */
  github: {
    token: string;
    organization?: string;
  };
  /** Vercel configuration */
  vercel: {
    token: string;
    teamId?: string;
  };
  /** Server configuration */
  server: {
    host: string;
    port: number;
    webhookPath: string;
  };
  /** Workspace configuration */
  workspace: {
    baseDir: string;
    maxWorkspacesPerUser: number;
    timeout: number;
  };
  /** Feature flags */
  features: {
    enableWebsiteCreation: boolean;
    enableWebsiteEditing: boolean;
    enableRepositoryImport: boolean;
    enableDeployment: boolean;
    enableGitIntegration: boolean;
    enableStatusUpdates: boolean;
    enableErrorRecovery: boolean;
  };
  /** Performance optimization configuration */
  performance: {
    enableOptimizations: boolean;
    enableCaching: boolean;
    enableConnectionPooling: boolean;
    enableMetrics: boolean;
    enableResourceManagement: boolean;
    cacheConfig: {
      maxSize: number;
      ttl: number;
      evictionPolicy: "lru" | "lfu" | "fifo";
    };
    connectionPoolConfig: {
      maxConnections: number;
      idleTimeout: number;
      requestTimeout: number;
    };
    metricsConfig: {
      enablePrometheus: boolean;
      metricsInterval: number;
    };
    resourceConfig: {
      maxMemoryUsage: number;
      gcThreshold: number;
      cleanupInterval: number;
    };
  };
}

/**
 * BotEngine events
 */
export interface BotEngineEvents {
  "webhook:received": (event: WebhookEvent) => void;
  "message:received": (user: User, message: WhatsAppMessage) => void;
  "message:sent": (user: User, message: string) => void;
  "session:started": (sessionId: string, user: User, operation: SessionOperation) => void;
  "session:completed": (sessionId: string, user: User, result: ProjectData) => void;
  "session:failed": (sessionId: string, user: User, error: Error) => void;
  "status:update": (sessionId: string, status: string, data?: StatusMessageData) => void;
  error: (error: Error, context?: Record<string, unknown>) => void;
  connected: () => void;
  disconnected: () => void;
}

/**
 * BotEngine - Main orchestrator for WhatsApp Website Bot
 */
export class BotEngine extends EventEmitter implements WebhookHandler {
  private config: BotEngineConfig;
  private sessionManager: SessionManager;
  private websiteManager: WebsiteManager;
  private workspaceManager: IWorkspaceManager;
  private geminiRunner: GeminiRunner;
  private twilioClient: twilio.Twilio;
  private statusMessenger!: StatusMessenger;
  private errorHandler: ErrorHandlerService;
  private server: {
    listen: (port: number, host: string, callback: (error?: Error) => void) => void;
    close: (callback: () => void) => void;
  } | null = null;
  private activeOperations: Map<string, AbortController> = new Map();
  private userSessions: Map<string, Set<string>> = new Map();
  private sessionToUser: Map<string, User> = new Map();
  private isRunning: boolean = false;

  // Performance optimization components
  private performanceManager: PerformanceManager;
  private connectionPool: ConnectionPool;
  private cacheService: CacheService;
  private resourceManager: ResourceManager;
  private performanceMetrics: PerformanceMetrics;
  private memoryOptimizer: MemoryOptimizer;

  // Memoized functions
  private memoizedClassifyIntent: unknown;

  constructor(config: BotEngineConfig) {
    super();
    this.config = config;

    // Initialize Twilio client
    this.twilioClient = twilio(config.twilio.accountSid, config.twilio.authToken);

    // Initialize SessionManager
    this.sessionManager = new SessionManager(config.workspace.baseDir);

    // Initialize WorkspaceManager
    this.workspaceManager = new WorkspaceManager({
      baseWorkspaceDir: config.workspace.baseDir,
      maxWorkspacesPerUser: config.workspace.maxWorkspacesPerUser,
      workspaceTimeout: config.workspace.timeout,
      defaultBranch: "main",
      gitConfig: {
        userName: "WhatsApp Bot",
        userEmail: "<EMAIL>",
        authToken: config.github.token,
      },
    });

    // Initialize GeminiRunner
    this.geminiRunner = new GeminiRunner({
      geminiCliPath: "gemini-cli",
      model: config.google.model,
      temperature: 0.7,
      maxTokens: config.google.maxTokens,
    });

    // Initialize Error Handler
    this.errorHandler = createErrorHandler({
      ...DEFAULT_ERROR_HANDLER_CONFIG,
      notifications: {
        notifyUsers: config.features.enableStatusUpdates,
        notifyAdmins: true,
        rateLimitMs: 60000,
      },
    });

    // Initialize performance optimization components
    this.performanceManager = new PerformanceManager({
      alertThresholds: {
        responseTime: 5000,
        errorRate: 0.1,
        memoryUsage: 0.8,
        cpuUsage: 0.8,
      },
      metricsInterval: config.performance?.metricsConfig?.metricsInterval || 60000,
      historySize: 100,
      optimization: {
        enableAutoOptimization: true,
        gcInterval: 60000,
        cacheCleanupInterval: 300000,
        connectionPoolCleanup: 120000,
      },
    });
    this.connectionPool = new ConnectionPool({
      maxConnections: config.performance?.connectionPoolConfig?.maxConnections || 100,
      maxConnectionsPerHost: 10,
      keepAlive: true,
      keepAliveMsecs: 30000,
      timeout: config.performance?.connectionPoolConfig?.requestTimeout || 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      retryBackoff: 2,
      services: {
        github: {
          baseUrl: "https://api.github.com",
          maxConnections: 20,
          timeout: 30000,
          retryAttempts: 3,
          headers: {
            "User-Agent": "WhatsApp-Website-Bot/1.0",
            Accept: "application/vnd.github.v3+json",
          },
        },
        vercel: {
          baseUrl: "https://api.vercel.com",
          maxConnections: 15,
          timeout: 30000,
          retryAttempts: 3,
          headers: {
            "User-Agent": "WhatsApp-Website-Bot/1.0",
            "Content-Type": "application/json",
          },
        },
        twilio: {
          baseUrl: "https://api.twilio.com",
          maxConnections: 10,
          timeout: 15000,
          retryAttempts: 2,
          headers: {
            "User-Agent": "WhatsApp-Website-Bot/1.0",
            "Content-Type": "application/x-www-form-urlencoded",
          },
        },
        gemini: {
          baseUrl: "https://generativelanguage.googleapis.com",
          maxConnections: 5,
          timeout: 60000,
          retryAttempts: 2,
          headers: {
            "User-Agent": "WhatsApp-Website-Bot/1.0",
            "Content-Type": "application/json",
          },
        },
      },
    });
    this.cacheService = new CacheService({
      maxSize: config.performance?.cacheConfig?.maxSize || 1000,
      maxMemoryMB: 128,
      defaultTTL: config.performance?.cacheConfig?.ttl || 300000,
      maxTTL: config.performance?.cacheConfig?.ttl || 300000,
      evictionPolicy: "lru",
      evictionBatchSize: 10,
      compressionEnabled: false,
      serializationEnabled: true,
      warmupEnabled: false,
      warmupKeys: [],
      cleanupInterval: 300000,
      expiredCleanupBatch: 100,
    });
    this.resourceManager = new ResourceManager({
      memory: {
        maxHeapSize:
          (config.performance?.resourceConfig?.maxMemoryUsage || 512 * 1024 * 1024) / (1024 * 1024),
        gcThreshold:
          (config.performance?.resourceConfig?.gcThreshold || 100 * 1024 * 1024) / (1024 * 1024),
        gcInterval: 60000,
        leakDetectionEnabled: true,
        leakThreshold: 10,
      },
      files: {
        maxOpenFiles: 1000,
        cleanupInterval: 300000,
        tempFileTimeout: 3600000,
        maxTempFiles: 100,
      },
      connections: {
        maxConnections: 100,
        idleTimeout: 300000,
        cleanupInterval: 60000,
      },
      processes: {
        maxProcesses: 10,
        processTimeout: 1800000,
        cleanupInterval: 300000,
      },
      timers: {
        maxTimers: 1000,
        cleanupInterval: 60000,
      },
      streams: {
        maxStreams: 100,
        idleTimeout: 300000,
        cleanupInterval: 60000,
      },
      monitoring: {
        enabled: true,
        interval: 30000,
        alertThresholds: {
          memory: 85,
          files: 80,
          connections: 80,
        },
      },
    });
    this.performanceMetrics = new PerformanceMetrics({
      collection: {
        enabled: true,
        interval: config.performance?.metricsConfig?.metricsInterval || 30000,
        maxSamples: 1000,
        maxAge: 3600000,
      },
      export: {
        enabled: true,
        interval: 300000,
        format: "json",
        destination: "console",
      },
      aggregation: {
        enabled: true,
        interval: 300000,
        retention: 86400000,
      },
      alerts: {
        enabled: true,
        thresholds: {
          "system.memory.heap_used": 512 * 1024 * 1024,
          "http.request.duration": 2000,
          "sessions.active": 50,
        },
        cooldown: 300000,
      },
    });
    this.memoryOptimizer = MemoryOptimizer.getInstance();

    // Initialize memoized functions
    this.memoizedClassifyIntent = memoize(this.classifyIntent.bind(this), {
      maxSize: 100,
      ttl: 300000, // 5 minutes
      keyGenerator: (message: string) => message.toLowerCase().slice(0, 100),
    });

    // Set up error handler notification callback
    this.errorHandler.on(
      "error:notification:sent",
      (error: Error, context: Record<string, unknown>, recipient: string) => {
        // Handle async operation without returning promise
        void (async () => {
          if (recipient !== "user" && recipient !== "admin" && context.phoneNumber) {
            const severity = this.getErrorSeverity(error);
            const severityEmoji = {
              low: "ℹ️",
              medium: "⚠️",
              high: "❌",
              critical: "🚨",
            };

            const userMessage = this.getUserFriendlyErrorMessage(error);
            const formattedMessage = `${severityEmoji[severity]} ${userMessage}`;

            try {
              await this.twilioClient.messages.create({
                body: formattedMessage,
                from: `whatsapp:${this.config.twilio.phoneNumber}`,
                to: `whatsapp:${typeof context.phoneNumber === "string" ? context.phoneNumber : "unknown"}`,
              });
            } catch (twilioError) {
              console.error("[BotEngine] Failed to send error notification:", twilioError);
            }
          }
        })();
      }
    );

    // Initialize StatusMessenger
    this.statusMessenger = createStatusMessenger({
      twilio: {
        client: this.twilioClient,
        phoneNumber: config.twilio.phoneNumber,
      },
      messageQueue: {
        maxConcurrent: 5,
        retryAttempts: 3,
        retryDelay: 1000,
      },
      rateLimit: {
        maxMessagesPerMinute: 10,
        maxMessagesPerHour: 100,
      },
      features: {
        enableProgressTracking: config.features.enableStatusUpdates,
        enableErrorNotifications: config.features.enableStatusUpdates,
        enableWarningNotifications: config.features.enableStatusUpdates,
        enableInfoNotifications: config.features.enableStatusUpdates,
      },
    });

    // Initialize WebsiteManager
    this.websiteManager = new WebsiteManager(
      {
        github: {
          token: config.github.token,
          organization: config.github.organization,
          baseUrl: "https://api.github.com",
        },
        vercel: {
          token: config.vercel.token,
          teamId: config.vercel.teamId,
        },
        workspaceDir: config.workspace.baseDir,
        sessionTimeout: config.workspace.timeout,
        enableGitIntegration: config.features.enableGitIntegration,
        enableDeployment: config.features.enableDeployment,
      },
      this.workspaceManager,
      this.geminiRunner,
      this.statusMessenger
    );

    console.log("[BotEngine] Initialized with configuration:", {
      twilioPhone: config.twilio.phoneNumber,
      serverPort: config.server.port,
      workspaceDir: config.workspace.baseDir,
      features: config.features,
    });
  }

  /**
   * Start the bot engine
   */
  async start(): Promise<void> {
    this.performanceMetrics.startTimer("bot_engine_start", "Bot engine startup");

    try {
      // Start performance monitoring
      if (this.config.performance?.enableOptimizations) {
        this.performanceManager.start();

        // Start memory monitoring
        setInterval(() => {
          this.memoryOptimizer.monitor();
        }, this.config.performance?.resourceConfig?.cleanupInterval || 60000);

        // Start metrics collection
        if (this.config.performance?.enableMetrics) {
          this.performanceMetrics.start();
        }
      }

      // Initialize resource manager
      if (this.config.performance?.enableResourceManagement) {
        this.resourceManager.start();
      }

      // Create HTTP server for webhook handling
      this.server = createServer((req, res) => {
        this.handleHttpRequest(req, res).catch(console.error);
      });

      // Start listening
      await new Promise<void>((resolve, reject) => {
        this.server?.listen(this.config.server.port, this.config.server.host, (error?: Error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      // Initialize StatusMessenger
      this.statusMessenger.initialize();

      this.isRunning = true;

      console.log(`[BotEngine] Started on ${this.config.server.host}:${this.config.server.port}`);
      console.log(`[BotEngine] Webhook endpoint: ${this.config.server.webhookPath}`);

      if (this.config.performance?.enableOptimizations) {
        console.log("[BotEngine] Performance optimizations enabled");
      }

      this.emit("connected");
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to start bot engine");
      console.error("[BotEngine] Failed to start:", botError);
      this.emit("error", botError);
      throw botError;
    } finally {
      if (this.config.performance?.enableMetrics) {
        this.performanceMetrics.endTimer("startup_timer", "startup");
      }
    }
  }

  /**
   * Stop the bot engine
   */
  async stop(): Promise<void> {
    this.performanceMetrics.startTimer("bot_engine_stop", "Bot engine shutdown");

    try {
      this.isRunning = false;

      // Cancel all active operations
      for (const [, controller] of this.activeOperations) {
        controller.abort();
      }
      this.activeOperations.clear();

      // Stop all active sessions
      const activeSessions = await this.sessionManager.getActiveSessions();
      for (const session of activeSessions) {
        try {
          session.fail("Bot engine shutting down");
          await this.sessionManager.updateSession(session);
        } catch (error: unknown) {
          console.error(`[BotEngine] Failed to stop session ${session.id}:`, error);
        }
      }

      // Clean up components
      this.websiteManager.shutdown();
      await this.sessionManager.shutdown();
      this.geminiRunner.cleanupExpiredSessions();
      await this.errorHandler.shutdown();

      // Stop performance optimization components
      if (this.config.performance?.enableOptimizations) {
        this.performanceManager.stop();

        if (this.config.performance?.enableConnectionPooling) {
          this.connectionPool.destroy();
        }

        if (this.config.performance?.enableResourceManagement) {
          await this.resourceManager.stop();
        }

        if (this.config.performance?.enableMetrics) {
          this.performanceMetrics.stop();
        }
      }

      // Stop HTTP server
      if (this.server) {
        await new Promise<void>(resolve => {
          this.server?.close(() => resolve());
        });
      }

      console.log("[BotEngine] Stopped successfully");
      this.emit("disconnected");
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to stop bot engine");
      console.error("[BotEngine] Failed to stop:", botError);
      this.emit("error", botError);
      throw botError;
    } finally {
      if (this.config.performance?.enableMetrics) {
        this.performanceMetrics.endTimer("shutdown_timer", "shutdown");
      }
    }
  }

  /**
   * Handle HTTP requests
   */
  private async handleHttpRequest(req: IncomingMessage, res: ServerResponse): Promise<void> {
    try {
      if (!req.url) {
        this.sendResponse(res, 400, "Invalid request");
        return;
      }

      const parsedUrl = parse(req.url, true);
      const path = parsedUrl.pathname;

      // Handle webhook requests
      if (path === this.config.server.webhookPath && req.method === "POST") {
        await this.handleWebhookRequest(req, res);
        return;
      }

      // Handle health check
      if (path === "/health" && req.method === "GET") {
        this.sendResponse(res, 200, "OK");
        return;
      }

      // Handle status endpoint
      if (path === "/status" && req.method === "GET") {
        const status = await this.getStatus();
        this.sendResponse(res, 200, status);
        return;
      }

      // 404 for unknown paths
      this.sendResponse(res, 404, "Not Found");
    } catch (error: unknown) {
      console.error("[BotEngine] HTTP request error:", error);
      this.sendResponse(res, 500, "Internal Server Error");
    }
  }

  /**
   * Handle webhook requests
   */
  private async handleWebhookRequest(req: IncomingMessage, res: ServerResponse): Promise<void> {
    try {
      // Parse request body
      const body = await this.parseRequestBody(req);

      // Validate Twilio signature if configured
      if (this.config.twilio.authToken) {
        const signature = req.headers["x-twilio-signature"] as string;
        const isValid = twilio.validateRequest(
          this.config.twilio.authToken,
          signature,
          this.config.twilio.webhookUrl,
          body
        );

        if (!isValid) {
          console.warn("[BotEngine] Invalid Twilio signature");
          this.sendResponse(res, 403, "Forbidden");
          return;
        }
      }

      // Parse WhatsApp message
      const whatsAppMessage = this.parseWhatsAppMessage(body);
      if (!whatsAppMessage) {
        console.warn("[BotEngine] Invalid WhatsApp message format");
        this.sendResponse(res, 400, "Invalid message format");
        return;
      }

      // Create webhook event
      const event: MessageReceivedEvent = {
        id: randomUUID(),
        type: "message.received",
        timestamp: new Date(),
        source: "twilio",
        version: "1.0",
        data: {
          messageId: randomUUID(),
          from: this.extractUser(whatsAppMessage),
          message: {
            id: randomUUID(),
            type: "text",
            content: { type: "text", text: whatsAppMessage.Body },
            timestamp: new Date(),
          },
        },
      };

      // Handle webhook event
      const result = await this.handle(event);

      // Send response
      this.sendResponse(res, result.status === "success" ? 200 : 500, result.message || "OK");
    } catch (error: unknown) {
      console.error("[BotEngine] Webhook processing error:", error);
      this.sendResponse(res, 500, "Internal Server Error");
    }
  }

  /**
   * Handle webhook events (implements WebhookHandler interface)
   */
  async handle(event: WebhookEvent): Promise<WebhookResponse> {
    return this.handleWebhookEvent(event);
  }

  /**
   * Handle webhook events
   */
  async handleWebhookEvent(event: WebhookEvent): Promise<WebhookResponse> {
    try {
      console.log(`[BotEngine] Processing webhook event: ${event.type}`);

      this.emit("webhook:received", event);

      // Handle different event types
      if (isMessageReceivedEvent(event)) {
        await this.processWhatsAppMessage(event.data.message, event.data.from);
      } else if (isSessionStartedEvent(event)) {
        this.handleSessionStarted(event);
      } else if (isSessionCompletedEvent(event)) {
        this.handleSessionCompleted(event);
      } else if (isSessionFailedEvent(event)) {
        this.handleSessionFailed(event);
      } else if (isSystemErrorEvent(event)) {
        this.handleSystemError(event);
      } else {
        console.log(`[BotEngine] Unhandled event type: ${event.type}`);
      }

      return { status: "success", message: "Event processed" };
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to process webhook event");
      console.error("[BotEngine] Webhook event error:", botError);

      this.emit("error", botError, { event });

      return {
        status: "error",
        message: botError.message,
        retryAfter: 60,
      };
    }
  }

  /**
   * Process WhatsApp message
   */
  async processWhatsAppMessage(
    message: { content: { type: string; text?: string } },
    user: User
  ): Promise<void> {
    // Start performance timer if enabled
    if (this.config.performance?.enableMetrics) {
      this.performanceMetrics.startTimer("whatsapp.message.processing_time", "message_processing");
    }

    try {
      console.log(
        `[BotEngine] Processing message from ${user.phoneNumber}: ${message.content.text}`
      );

      // Update performance metrics
      this.performanceMetrics.incrementCounter("messages_processed");

      this.emit("message:received", user, {
        From: user.phoneNumber,
        Body: message.content.type === "text" ? message.content.text : "",
        NumMedia: 0,
      });

      // Check for active session (with caching)
      const cacheKey = `active_session_${user.phoneNumber}`;
      let activeSession: WebsiteSession | null = null;

      if (this.config.performance?.enableCaching) {
        const cachedSession = this.cacheService.get(cacheKey);
        if (
          cachedSession &&
          typeof cachedSession === "object" &&
          cachedSession !== null &&
          "id" in cachedSession
        ) {
          activeSession = cachedSession as WebsiteSession;
        }
      }

      if (!activeSession) {
        activeSession = await this.sessionManager.getActiveSession(user.phoneNumber);
        if (activeSession && this.config.performance?.enableCaching) {
          this.cacheService.set(cacheKey, activeSession, 60000); // 1 minute cache
        }
      }

      if (activeSession) {
        console.log(
          `[BotEngine] Found active session ${activeSession.id} for user ${user.phoneNumber}`
        );

        // Add message to session context
        activeSession.addMessage({
          role: "user",
          content: message.content.type === "text" ? message.content.text || "" : "",
        });

        await this.sessionManager.updateSession(activeSession);

        // Update cache
        if (this.config.performance?.enableCaching) {
          this.cacheService.set(cacheKey, activeSession, 60000);
        }

        // Continue with existing session
        await this.continueSession(
          activeSession,
          message.content.type === "text" ? message.content.text || "" : ""
        );
        return;
      }

      // Classify intent for new conversation (with memoization)
      const messageText = message.content.type === "text" ? message.content.text || "" : "";
      const intent = this.config.performance?.enableOptimizations
        ? (this.memoizedClassifyIntent as (text: string) => IntentClassification)(messageText)
        : this.classifyIntent(messageText);

      console.log(
        `[BotEngine] Classified intent: ${intent.intent} (confidence: ${intent.confidence})`
      );

      // Route to appropriate handler
      switch (intent.intent) {
        case "new_site":
          await this.createWebsiteSession(user, messageText);
          break;
        case "edit_site":
          await this.editWebsiteSession(user, messageText);
          break;
        case "import_repo":
          if (intent.repositoryInfo) {
            await this.importRepositorySession(user, intent.repositoryInfo);
          } else {
            await this.sendWhatsAppMessage(
              user.phoneNumber,
              "Please provide a valid GitHub repository URL."
            );
          }
          break;
        default:
          await this.sendWhatsAppMessage(
            user.phoneNumber,
            "I can help you:\n" +
              "• Create a new website\n" +
              "• Edit an existing website\n" +
              "• Import a GitHub repository\n\n" +
              "What would you like to do?"
          );
      }
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to process WhatsApp message");
      console.error("[BotEngine] Message processing error:", botError);

      this.performanceMetrics.incrementCounter("message_processing_errors");
      await this.handleError(botError, undefined, user.phoneNumber);
    } finally {
      if (this.config.performance?.enableMetrics) {
        this.performanceMetrics.endTimer("whatsapp.message.processing_time", "message_processing");
      }
    }
  }

  /**
   * Create website session
   */
  async createWebsiteSession(user: User, prompt: string): Promise<void> {
    try {
      console.log(`[BotEngine] Creating website session for user ${user.phoneNumber}`);

      // Send initial status update
      await this.sendWhatsAppMessage(
        user.phoneNumber,
        "🚀 Creating your website! I'll keep you updated on the progress."
      );

      // Create workspace
      const workspace = await this.workspaceManager.createWorkspace({
        user,
        type: "temporary",
        project: {
          name: this.generateProjectName(prompt),
          description: prompt,
          type: "static",
        },
      });

      // Create session
      const session = await this.sessionManager.createSession({
        user,
        operation: "create",
        workspace,
        context: {
          messages: [
            {
              id: randomUUID(),
              role: "user",
              content: prompt,
              timestamp: new Date(),
            },
          ],
        },
      });

      // Track session
      this.trackUserSession(user.phoneNumber, session.id);
      this.sessionToUser.set(session.id, user);

      // Initialize Gemini runner
      this.geminiRunner.initialize(workspace);

      // Start session
      session.start();
      await this.sessionManager.updateSession(session);

      this.emit("session:started", session.id, user, "create");

      // Send status update
      await this.sendStatusUpdate(session.id, "Workspace created, starting website generation...");

      // Create website
      const result = await this.websiteManager.createWebsite(session);

      if (result.success) {
        session.complete();
        await this.sessionManager.updateSession(session);

        const projectData: ProjectData = {
          projectName: workspace.project?.name || "Website",
          description: prompt,
          url: result.url || "",
          htmlContent: "", // Would be populated by WebsiteManager
          githubRepoUrl: result.metadata?.repositoryUrl || null,
          githubRepoName: result.metadata?.projectName || null,
          lastCommitSha: null,
        };

        this.emit("session:completed", session.id, user, projectData);

        // Send success message
        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `✅ Your website has been created successfully!\n\n` +
            `🌐 URL: ${result.url}\n` +
            `📝 Project: ${projectData.projectName}\n\n` +
            `You can now view your website at the URL above.`
        );
      } else {
        session.fail("Website creation failed");
        await this.sessionManager.updateSession(session);

        this.emit(
          "session:failed",
          session.id,
          user,
          new Error(result.errors?.[0] || "Unknown error")
        );

        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `❌ Failed to create your website. Please try again later.`
        );
      }
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to create website session");
      console.error("[BotEngine] Website creation error:", botError);

      await this.handleError(botError, undefined, user.phoneNumber);
    }
  }

  /**
   * Edit website session
   */
  async editWebsiteSession(user: User, prompt: string): Promise<void> {
    try {
      console.log(`[BotEngine] Creating edit session for user ${user.phoneNumber}`);

      // Send initial status update
      await this.sendWhatsAppMessage(
        user.phoneNumber,
        "✏️ Editing your website! I'll keep you updated on the progress."
      );

      // Get or create workspace
      const workspace = await this.workspaceManager.createWorkspace({
        user,
        type: "temporary",
        project: {
          name: "website-edit",
          description: prompt,
          type: "static",
        },
      });

      // Create session
      const session = await this.sessionManager.createSession({
        user,
        operation: "edit",
        workspace,
        context: {
          messages: [
            {
              id: randomUUID(),
              role: "user",
              content: prompt,
              timestamp: new Date(),
            },
          ],
        },
      });

      // Track session
      this.trackUserSession(user.phoneNumber, session.id);
      this.sessionToUser.set(session.id, user);

      // Initialize Gemini runner
      this.geminiRunner.initialize(workspace);

      // Start session
      session.start();
      await this.sessionManager.updateSession(session);

      this.emit("session:started", session.id, user, "edit");

      // Send status update
      await this.sendStatusUpdate(session.id, "Preparing to edit website...");

      // Edit website
      const result = await this.websiteManager.editWebsite(session);

      if (result.success) {
        session.complete();
        await this.sessionManager.updateSession(session);

        const projectData: ProjectData = {
          projectName: workspace.project?.name || "Website",
          description: prompt,
          url: result.url || "",
          htmlContent: "", // Would be populated by WebsiteManager
          githubRepoUrl: null,
          githubRepoName: null,
          lastCommitSha: null,
        };

        this.emit("session:completed", session.id, user, projectData);

        // Send success message
        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `✅ Your website has been updated successfully!\n\n` +
            `🌐 URL: ${result.url}\n\n` +
            `You can now view your updated website at the URL above.`
        );
      } else {
        session.fail("Website editing failed");
        await this.sessionManager.updateSession(session);

        this.emit(
          "session:failed",
          session.id,
          user,
          new Error(result.errors?.[0] || "Unknown error")
        );

        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `❌ Failed to edit your website. Please try again later.`
        );
      }
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to edit website session");
      console.error("[BotEngine] Website editing error:", botError);

      await this.handleError(botError, undefined, user.phoneNumber);
    }
  }

  /**
   * Import repository session
   */
  async importRepositorySession(user: User, repoInfo: RepositoryInfo): Promise<void> {
    try {
      console.log(`[BotEngine] Creating import session for user ${user.phoneNumber}`);

      // Send initial status update
      await this.sendWhatsAppMessage(
        user.phoneNumber,
        "📦 Importing your repository! I'll keep you updated on the progress."
      );

      // Import repository
      const result = await this.websiteManager.importRepository({
        ...repoInfo,
        fullUrl: repoInfo.fullUrl || undefined,
      });

      if (result.success) {
        const projectData: ProjectData = {
          projectName: result.projectName,
          description: result.description || "",
          url: result.deploymentUrl,
          htmlContent: "", // Would be populated by WebsiteManager
          githubRepoUrl: result.repositoryUrl,
          githubRepoName: result.projectName,
          lastCommitSha: null,
        };

        this.emit("session:completed", randomUUID(), user, projectData);

        // Send success message
        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `✅ Repository imported successfully!\n\n` +
            `📦 Repository: ${result.repositoryUrl}\n` +
            `🌐 URL: ${result.deploymentUrl}\n` +
            `📝 Project: ${result.projectName}\n\n` +
            `You can now view your imported website at the URL above.`
        );
      } else {
        this.emit("session:failed", randomUUID(), user, new Error(result.error || "Unknown error"));

        await this.sendWhatsAppMessage(
          user.phoneNumber,
          `❌ Failed to import repository: ${result.error || "Unknown error"}`
        );
      }
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to import repository session");
      console.error("[BotEngine] Repository import error:", botError);

      await this.handleError(botError, undefined, user.phoneNumber);
    }
  }

  /**
   * Continue existing session
   */
  private async continueSession(session: WebsiteSession, message: string): Promise<void> {
    try {
      console.log(`[BotEngine] Continuing session ${session.id} with message: ${message}`);

      // Send acknowledgment
      await this.sendWhatsAppMessage(
        session.user.phoneNumber,
        "I'm processing your request. Let me continue working on that..."
      );

      // Update session context based on operation
      if (session.operation === "create") {
        await this.websiteManager.createWebsite(session);
      } else if (session.operation === "edit") {
        await this.websiteManager.editWebsite(session);
      }

      // Session completion will be handled by the WebsiteManager callbacks
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to continue session");
      console.error("[BotEngine] Session continuation error:", botError);

      await this.handleError(botError, session.id, session.user.phoneNumber);
    }
  }

  /**
   * Send WhatsApp message with comprehensive error handling
   */
  async sendWhatsAppMessage(phoneNumber: string, message: string): Promise<void> {
    // Start performance timer if enabled
    if (this.config.performance?.enableMetrics) {
      this.performanceMetrics.startTimer("whatsapp.message.processing_time", "message_sending");
    }

    try {
      // Use connection pool if enabled
      let result: unknown;
      if (this.config.performance?.enableConnectionPooling) {
        result = await this.connectionPool.makeTwilioRequest(
          "/2010-04-01/Accounts/" + this.config.twilio.accountSid + "/Messages.json",
          {
            method: "POST",
            body: new URLSearchParams({
              Body: message,
              From: `whatsapp:${this.config.twilio.phoneNumber}`,
              To: `whatsapp:${phoneNumber}`,
            }).toString(),
          }
        );
      } else {
        result = await this.twilioClient.messages.create({
          body: message,
          from: `whatsapp:${this.config.twilio.phoneNumber}`,
          to: `whatsapp:${phoneNumber}`,
        });
      }

      console.log(`[BotEngine] Sent WhatsApp message to ${phoneNumber}: ${result.sid || "pooled"}`);

      // Update performance metrics
      this.performanceMetrics.incrementCounter("messages_sent");
      this.performanceMetrics.recordHistogram("message_length", message.length);

      // Get user for event
      const user = this.getUserByPhoneNumber(phoneNumber);
      if (user) {
        this.emit("message:sent", user, message);
      }
    } catch (error: unknown) {
      const botError = this.createBotError(error, "Failed to send WhatsApp message");
      console.error("[BotEngine] WhatsApp message error:", botError);

      // Update error metrics
      this.performanceMetrics.incrementCounter("message_send_errors");

      // Use comprehensive error handler
      await this.errorHandler.handle(botError, {
        phoneNumber,
        operation: "send_whatsapp_message",
        service: "twilio",
        metadata: {
          messageLength: message.length,
          originalError: error instanceof Error ? error.message : String(error),
        },
      });

      this.emit("error", botError, { phoneNumber, message });
    } finally {
      if (this.config.performance?.enableMetrics) {
        this.performanceMetrics.endTimer("whatsapp.message.processing_time", "message_sending");
      }
    }
  }

  /**
   * Send status update
   */
  async sendStatusUpdate(
    sessionId: string,
    status: string,
    data?: StatusMessageData
  ): Promise<void> {
    try {
      const session = await this.sessionManager.getSession(sessionId);
      if (!session) {
        console.warn(`[BotEngine] Session ${sessionId} not found for status update`);
        return;
      }

      console.log(`[BotEngine] Status update for session ${sessionId}: ${status}`);

      // Add status to session narrative
      session.addNarrativeItem({
        type: "text",
        content: status,
      });

      await this.sessionManager.updateSession(session);

      // Send to user if status updates are enabled
      if (this.config.features.enableStatusUpdates) {
        await this.sendWhatsAppMessage(session.user.phoneNumber, `📊 ${status}`);
      }

      this.emit("status:update", sessionId, status, data);
    } catch (error: unknown) {
      console.error("[BotEngine] Status update error:", error);
    }
  }

  /**
   * Handle error with comprehensive error handling service
   */
  async handleError(error: Error, sessionId?: string, phoneNumber?: string): Promise<void> {
    try {
      const botError = this.createBotError(error, "Bot engine error");

      console.error("[BotEngine] Handling error:", botError);

      // Update session if provided
      if (sessionId) {
        const session = await this.sessionManager.getSession(sessionId);
        if (session) {
          session.fail(botError.message);
          await this.sessionManager.updateSession(session);

          this.emit("session:failed", sessionId, session.user, botError);
        }
      }

      // Use comprehensive error handler
      await this.errorHandler.handle(botError, {
        sessionId,
        phoneNumber,
        operation: "bot_engine_operation",
        service: "bot_engine",
        metadata: {
          originalError: botError.message,
          stack: botError.stack,
        },
      });

      this.emit("error", botError, { sessionId, phoneNumber });
    } catch (handleError: unknown) {
      console.error("[BotEngine] Error handling error:", handleError);
    }
  }

  /**
   * Get supported event types
   */
  getSupportedEvents(): WebhookEventType[] {
    return [
      "message.received",
      "message.sent",
      "session.started",
      "session.completed",
      "session.failed",
      "system.error",
    ] as WebhookEventType[];
  }

  /**
   * Get bot status
   */
  async getStatus(): Promise<Record<string, unknown>> {
    // Start performance timer if enabled
    if (this.config.performance?.enableMetrics) {
      this.performanceMetrics.startTimer("http.request.duration", "status_retrieval");
    }

    try {
      const sessionStats = await this.sessionManager.getSessionStats();
      await this.sessionManager.getActiveSessions();
      const errorStats = this.errorHandler.getStatistics();

      // Get performance stats
      const performanceStats: unknown = this.config.performance?.enableOptimizations
        ? this.performanceManager.getPerformanceSummary()
        : null;

      const cacheStats = this.config.performance?.enableCaching
        ? this.cacheService.getStats()
        : null;

      const connectionPoolStats = this.config.performance?.enableConnectionPooling
        ? this.connectionPool.getStats()
        : null;

      const resourceStats = this.config.performance?.enableResourceManagement
        ? this.resourceManager.getStats()
        : null;

      const metricsData = this.config.performance?.enableMetrics
        ? this.performanceMetrics.getAllMetrics()
        : null;

      const memoryStats = this.config.performance?.enableOptimizations
        ? this.memoryOptimizer.getStats()
        : null;

      const status: Record<string, unknown> = {
        isRunning: this.isRunning,
        uptime: process.uptime(),
        sessions: {
          total: sessionStats.total,
          active: sessionStats.active,
          completed: sessionStats.completed,
          failed: sessionStats.failed,
        },
        activeOperations: this.activeOperations.size,
        errorHandling: {
          totalErrors: errorStats.totalErrors,
          errorsByCategory: errorStats.errorsByCategory,
          errorsBySeverity: errorStats.errorsBySeverity,
          recoverySuccessRate: errorStats.recoverySuccessRate,
          fallbackUsageRate: errorStats.fallbackUsageRate,
          errorRatePerMinute: errorStats.errorRatePerMinute,
          circuitBreakerTrips: errorStats.circuitBreakerTrips,
          recentErrorsCount: errorStats.recentErrors.length,
        },
        features: this.config.features,
        timestamp: new Date().toISOString(),
      };

      // Add performance data if enabled
      if (this.config.performance?.enableOptimizations) {
        status.performance = {
          enabled: true,
          manager: performanceStats,
          cache: cacheStats,
          connectionPool: connectionPoolStats,
          resources: resourceStats,
          metrics: metricsData,
          memory: memoryStats,
        };
      }

      return status;
    } catch (error: unknown) {
      console.error("[BotEngine] Status error:", error);

      // Use error handler for status errors
      await this.errorHandler.handle(
        error instanceof Error ? error : new Error("Status retrieval failed"),
        {
          operation: "get_status",
          service: "bot_engine",
        }
      );

      return {
        isRunning: this.isRunning,
        error: "Failed to get status",
        timestamp: new Date().toISOString(),
      };
    } finally {
      if (this.config.performance?.enableMetrics) {
        this.performanceMetrics.endTimer("http.request.duration", "status_retrieval");
      }
    }
  }

  // Private helper methods

  /**
   * Parse request body
   */
  private async parseRequestBody(req: IncomingMessage): Promise<Record<string, unknown>> {
    return new Promise((resolve, reject) => {
      let body = "";

      req.on("data", (chunk: Buffer) => {
        body += chunk.toString();
      });

      req.on("end", () => {
        try {
          // Parse URL-encoded body (Twilio format)
          let result: Record<string, unknown> = {};

          if (body.startsWith("{")) {
            // JSON format
            result = JSON.parse(body) as Record<string, unknown>;
          } else {
            // URL-encoded format
            const parsed = new URLSearchParams(body);
            for (const [key, value] of parsed) {
              result[key] = value;
            }
          }

          resolve(result);
        } catch (error) {
          reject(error instanceof Error ? error : new Error(String(error)));
        }
      });

      req.on("error", reject);
    });
  }

  /**
   * Parse WhatsApp message from request body
   */
  private parseWhatsAppMessage(body: Record<string, unknown>): WhatsAppMessage | null {
    try {
      if (!body.From || !body.Body) {
        return null;
      }

      return {
        From: typeof body.From === "string" ? body.From.replace("whatsapp:", "") : "",
        Body: typeof body.Body === "string" ? body.Body : "",
        NumMedia: parseInt(typeof body.NumMedia === "string" ? body.NumMedia : "0"),
        MediaUrl0: typeof body.MediaUrl0 === "string" ? body.MediaUrl0 : undefined,
      };
    } catch (error: unknown) {
      console.error("[BotEngine] WhatsApp message parsing error:", error);
      return null;
    }
  }

  /**
   * Extract user from WhatsApp message
   */
  private extractUser(message: WhatsAppMessage): User {
    return {
      phoneNumber: message.From,
      name: undefined,
      lastActiveAt: new Date(),
      isActive: true,
    };
  }

  /**
   * Classify message intent
   */
  private classifyIntent(message: string): IntentClassification {
    // Simple intent classification - in production, this would use AI
    const lowerMessage = message.toLowerCase();

    if (
      lowerMessage.includes("create") ||
      lowerMessage.includes("new") ||
      lowerMessage.includes("build")
    ) {
      return {
        intent: "new_site",
        confidence: 0.8,
        reasoning: "Message contains website creation keywords",
      };
    }

    if (
      lowerMessage.includes("edit") ||
      lowerMessage.includes("update") ||
      lowerMessage.includes("change")
    ) {
      return {
        intent: "edit_site",
        confidence: 0.8,
        reasoning: "Message contains website editing keywords",
      };
    }

    if (
      lowerMessage.includes("import") ||
      lowerMessage.includes("github") ||
      lowerMessage.includes("repository")
    ) {
      const githubMatch = message.match(/github\.com\/([^/]+)\/([^/\s]+)/);
      const repositoryInfo: RepositoryInfo | undefined = githubMatch
        ? {
            type: "url",
            fullUrl: githubMatch[0],
            owner: githubMatch[1],
            repo: githubMatch[2],
          }
        : undefined;

      return {
        intent: "import_repo",
        confidence: 0.9,
        reasoning: "Message contains repository import keywords",
        repositoryInfo,
      };
    }

    return {
      intent: "scaffold_mode",
      confidence: 0.5,
      reasoning: "Could not classify specific intent",
    };
  }

  /**
   * Generate project name from prompt
   */
  private generateProjectName(prompt: string): string {
    const words = prompt
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, "")
      .split(" ")
      .filter(word => word.length > 2)
      .slice(0, 3);

    return words.join("-") || "website";
  }

  /**
   * Track user session
   */
  private trackUserSession(phoneNumber: string, sessionId: string): void {
    const sessions = this.userSessions.get(phoneNumber) || new Set();
    sessions.add(sessionId);
    this.userSessions.set(phoneNumber, sessions);
  }

  /**
   * Get user by phone number
   */
  private getUserByPhoneNumber(phoneNumber: string): User | null {
    for (const [, user] of this.sessionToUser) {
      if (user.phoneNumber === phoneNumber) {
        return user;
      }
    }
    return null;
  }

  /**
   * Send HTTP response
   */
  private sendResponse(res: ServerResponse, statusCode: number, data: unknown): void {
    res.writeHead(statusCode, { "Content-Type": "application/json" });
    res.end(JSON.stringify(typeof data === "string" ? { message: data } : data));
  }

  /**
   * Create bot error
   */
  private createBotError(error: unknown, defaultMessage: string): Error {
    if (error instanceof Error) {
      return error;
    }

    return new Error(defaultMessage);
  }

  /**
   * Handle session started event
   */
  private handleSessionStarted(event: SessionStartedEvent): void {
    console.log(`[BotEngine] Session started: ${event.data.session.id}`);
    // Custom handling for session started events
  }

  /**
   * Handle session completed event
   */
  private handleSessionCompleted(event: SessionCompletedEvent): void {
    console.log(`[BotEngine] Session completed: ${event.data.session.id}`);
    // Custom handling for session completed events
  }

  /**
   * Handle session failed event
   */
  private handleSessionFailed(event: SessionFailedEvent): void {
    console.log(`[BotEngine] Session failed: ${event.data.session.id}`);
    // Custom handling for session failed events
  }

  /**
   * Handle system error event
   */
  private handleSystemError(event: SystemErrorEvent): void {
    console.error(`[BotEngine] System error: ${event.data.error.message}`);
    // Custom handling for system error events
  }

  /**
   * Get error severity
   */
  private getErrorSeverity(error: Error): "low" | "medium" | "high" | "critical" {
    if (error.name === "CriticalError" || error.message.includes("critical")) {
      return "critical";
    }
    if (error.name === "SystemError" || error.message.includes("system")) {
      return "high";
    }
    if (error.name === "ValidationError" || error.message.includes("validation")) {
      return "medium";
    }
    return "low";
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyErrorMessage(error: Error): string {
    // Default error messages for different error types
    const errorTypeMessages: Record<string, string> = {
      SessionError: "There was an issue with your session. Please try again.",
      WorkspaceError: "There was an issue with your workspace. Please try again.",
      GitError: "There was an issue with Git operations. Please try again.",
      CommunicationError: "There was a communication issue. Please try again.",
      AIError: "There was an issue with AI processing. Please try again.",
      DeploymentError: "There was an issue with deployment. Please try again.",
      ValidationError: "There was a validation issue. Please check your input.",
      RateLimitError: "You are sending requests too quickly. Please wait a moment.",
      SystemError: "There was a system issue. Please try again later.",
      ExternalServiceError: "There was an issue with an external service. Please try again later.",
    };

    return errorTypeMessages[error.name] || "An unexpected error occurred. Please try again.";
  }
}

/**
 * Create BotEngine instance
 */
export function createBotEngine(config: BotEngineConfig): BotEngine {
  return new BotEngine(config);
}

/**
 * Default BotEngine configuration
 */
export const DEFAULT_BOT_ENGINE_CONFIG: Partial<BotEngineConfig> = {
  server: {
    host: "localhost",
    port: 3000,
    webhookPath: "/webhook",
  },
  workspace: {
    baseDir: "/tmp/bot-workspaces",
    maxWorkspacesPerUser: 5,
    timeout: 30 * 60 * 1000, // 30 minutes
  },
  features: {
    enableWebsiteCreation: true,
    enableWebsiteEditing: true,
    enableRepositoryImport: true,
    enableDeployment: true,
    enableGitIntegration: true,
    enableStatusUpdates: true,
    enableErrorRecovery: true,
  },
  performance: {
    enableOptimizations: true,
    enableCaching: true,
    enableConnectionPooling: true,
    enableMetrics: true,
    enableResourceManagement: true,
    cacheConfig: {
      maxSize: 1000,
      ttl: 300000, // 5 minutes
      evictionPolicy: "lru",
    },
    connectionPoolConfig: {
      maxConnections: 10,
      idleTimeout: 30000,
      requestTimeout: 5000,
    },
    metricsConfig: {
      enablePrometheus: false,
      metricsInterval: 60000,
    },
    resourceConfig: {
      maxMemoryUsage: 512 * 1024 * 1024, // 512MB
      gcThreshold: 100 * 1024 * 1024, // 100MB
      cleanupInterval: 60000, // 1 minute
    },
  },
};
