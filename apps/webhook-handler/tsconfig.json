{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "baseUrl": "../../", "paths": {"@whatsite-bot/core": ["packages/core/dist"], "@whatsite-bot/core/*": ["packages/core/dist/*"], "@whatsite-bot/workspace-manager": ["packages/workspace-manager/dist"], "@whatsite-bot/workspace-manager/*": ["packages/workspace-manager/dist/*"], "@whatsite-bot/gemini-runner": ["packages/gemini-runner/dist"], "@whatsite-bot/gemini-runner/*": ["packages/gemini-runner/dist/*"]}}, "include": ["api/**/*", "lib/**/*"], "exclude": ["node_modules", "dist"]}