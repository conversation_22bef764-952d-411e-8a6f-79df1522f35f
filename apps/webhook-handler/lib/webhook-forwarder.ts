import type {
  WhatsAppWebhook,
  WhatsAppApiMessage,
  ClassifiedIntent,
  WebhookForwardingResult,
} from '@whatsite-bot/core';

interface ForwardingPayload {
  originalWebhook: WhatsAppWebhook;
  message: WhatsAppApiMessage;
  classifiedIntent: ClassifiedIntent;
  timestamp: string;
}

export class WebhookForwarder {
  private readonly botEngineUrl: string;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 1000; // 1 second

  constructor() {
    this.botEngineUrl = process.env.BOT_ENGINE_URL || 'https://whatsite-bot-engine.fly.dev';
  }

  async forwardToBot(payload: ForwardingPayload): Promise<WebhookForwardingResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(`${this.botEngineUrl}/webhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BOT_ENGINE_TOKEN}`,
            'X-Webhook-Source': 'vercel-handler',
          },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          const result = await response.json();
          return {
            success: true,
            statusCode: response.status,
            response: result,
            attempt,
          };
        } else {
          const errorText = await response.text();
          lastError = new Error(`HTTP ${response.status}: ${errorText}`);
        }
      } catch (error) {
        lastError = error as Error;
        console.error(`Forwarding attempt ${attempt} failed:`, error);
      }

      // Wait before retrying (unless it's the last attempt)
      if (attempt < this.maxRetries) {
        await this.delay(this.retryDelay * attempt);
      }
    }

    return {
      success: false,
      error: lastError?.message || 'Unknown error',
      attempts: this.maxRetries,
      lastError: lastError || undefined,
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.botEngineUrl}/health`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${process.env.BOT_ENGINE_TOKEN}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Bot engine health check failed:', error);
      return false;
    }
  }
}
