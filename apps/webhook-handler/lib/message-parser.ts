import type { WhatsAppWebhook, WhatsAppApiMessage } from '@whatsite-bot/core';

export class MessageParser {
  extractMessages(webhook: WhatsAppWebhook): WhatsAppApiMessage[] {
    const messages: WhatsAppApiMessage[] = [];

    if (!webhook.entry || !Array.isArray(webhook.entry)) {
      return messages;
    }

    for (const entry of webhook.entry) {
      if (!entry.changes || !Array.isArray(entry.changes)) {
        continue;
      }

      for (const change of entry.changes) {
        if (change.field !== 'messages' || !change.value?.messages) {
          continue;
        }

        for (const message of change.value.messages) {
          // Only process incoming messages (not status updates)
          if (message.type && message.from && message.timestamp) {
            messages.push({
              ...message,
              // Ensure we have the contact info
              contact: change.value.contacts?.find(
                (c: { wa_id: string }) => c.wa_id === message.from,
              ),
              // Add metadata
              metadata: {
                entry_id: entry.id,
                business_phone_number_id: change.value.metadata?.phone_number_id || 'unknown',
                webhook_id: webhook.id || 'unknown',
              },
            });
          }
        }
      }
    }

    return messages;
  }

  extractMessageText(message: WhatsAppApiMessage): string {
    switch (message.type) {
      case 'text':
        return message.text?.body || '';
      case 'button':
        return message.button?.text || '';
      case 'interactive':
        if (message.interactive?.type === 'button_reply') {
          return message.interactive.button_reply?.title || '';
        } else if (message.interactive?.type === 'list_reply') {
          return message.interactive.list_reply?.title || '';
        }
        return '';
      default:
        return '';
    }
  }

  isValidMessage(message: WhatsAppApiMessage): boolean {
    return !!(
      message.id &&
      message.from &&
      message.timestamp &&
      message.type &&
      this.extractMessageText(message).trim().length > 0
    );
  }

  getMessageContext(message: WhatsAppApiMessage): {
    phoneNumber: string;
    contactName?: string;
    messageId: string;
    timestamp: string;
    type: string;
  } {
    return {
      phoneNumber: message.from,
      contactName: message.contact?.profile?.name,
      messageId: message.id,
      timestamp: message.timestamp,
      type: message.type,
    };
  }
}
