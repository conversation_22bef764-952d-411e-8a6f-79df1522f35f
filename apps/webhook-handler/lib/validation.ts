import { createHmac } from 'crypto';

import type { VercelRequest } from '@vercel/node';

export class WebhookValidator {
  private readonly webhookSecret: string;

  constructor() {
    this.webhookSecret = process.env.WEBHOOK_SECRET || '';
    if (!this.webhookSecret) {
      console.warn('WEBHOOK_SECRET environment variable not set');
    }
  }

  validateSignature(req: VercelRequest): boolean {
    if (!this.webhookSecret) {
      console.warn('Webhook validation skipped: no secret configured');
      return true; // Allow in development/testing
    }

    const signature = req.headers['x-hub-signature-256'] as string;
    if (!signature) {
      console.error('Missing webhook signature');
      return false;
    }

    try {
      const body = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      const expectedSignature = this.generateSignature(body);

      return this.compareSignatures(signature, expectedSignature);
    } catch (error) {
      console.error('Error validating webhook signature:', error);
      return false;
    }
  }

  private generateSignature(payload: string): string {
    const hmac = createHmac('sha256', this.webhookSecret);
    hmac.update(payload);
    return 'sha256=' + hmac.digest('hex');
  }

  private compareSignatures(signature1: string, signature2: string): boolean {
    if (signature1.length !== signature2.length) {
      return false;
    }

    // Use constant-time comparison to prevent timing attacks
    let result = 0;
    for (let i = 0; i < signature1.length; i++) {
      result |= signature1.charCodeAt(i) ^ signature2.charCodeAt(i);
    }

    return result === 0;
  }

  validateWebhookStructure(webhook: unknown): boolean {
    if (!webhook || typeof webhook !== 'object') {
      return false;
    }
    const webhookObj = webhook as Record<string, unknown>;
    return !!(
      'entry' in webhookObj &&
      Array.isArray(webhookObj.entry) &&
      webhookObj.entry.length > 0
    );
  }

  extractPhoneNumber(webhook: unknown): string | null {
    try {
      if (!webhook || typeof webhook !== 'object') {
        return null;
      }

      const webhookObj = webhook as Record<string, unknown>;
      if (
        !('entry' in webhookObj) ||
        !Array.isArray(webhookObj.entry) ||
        webhookObj.entry.length === 0
      ) {
        return null;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const entry = webhookObj.entry[0];
      if (!entry || typeof entry !== 'object' || !('changes' in entry)) {
        return null;
      }

      const entryObj = entry as Record<string, unknown>;
      const changes = entryObj.changes;
      if (!Array.isArray(changes) || changes.length === 0) {
        return null;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const change = changes[0];
      if (
        !change ||
        typeof change !== 'object' ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        !('value' in change) ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        !change.value ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        typeof change.value !== 'object' ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        !('metadata' in change.value) ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        !change.value.metadata ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        typeof change.value.metadata !== 'object' ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        !('phone_number_id' in change.value.metadata)
      ) {
        return null;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      const phoneNumberId = (change.value.metadata as Record<string, unknown>).phone_number_id;
      return typeof phoneNumberId === 'string' ? phoneNumberId : null;
    } catch (error) {
      console.error('Error extracting phone number:', error);
      return null;
    }
  }
}
