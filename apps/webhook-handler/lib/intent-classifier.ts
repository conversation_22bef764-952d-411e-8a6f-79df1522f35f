import type { WhatsAppApiMessage, ClassifiedIntent, IntentType } from '@whatsite-bot/core';

export class IntentClassifier {
  private readonly websiteKeywords = [
    'website',
    'site',
    'web',
    'page',
    'landing',
    'portfolio',
    'business',
    'company',
    'blog',
    'store',
    'shop',
    'online',
    'domain',
    'hosting',
  ];

  private readonly helpKeywords = [
    'help',
    'how',
    'what',
    'can',
    'support',
    'assist',
    'guide',
    'tutorial',
    'explain',
    'show',
    'demo',
    'example',
    'instructions',
  ];

  private readonly greetingKeywords = [
    'hi',
    'hello',
    'hey',
    'good morning',
    'good afternoon',
    'good evening',
    'greetings',
    'howdy',
    'sup',
    'yo',
  ];

  private readonly statusKeywords = [
    'status',
    'progress',
    'update',
    'done',
    'ready',
    'finished',
    'complete',
    'how long',
    'when',
    'time',
    'eta',
  ];

  classifyMessage(message: WhatsAppApiMessage): ClassifiedIntent {
    const messageText = message.text?.body?.toLowerCase() || '';
    const messageType = message.type;

    // Handle non-text messages
    if (messageType !== 'text') {
      return {
        type: 'other' as IntentType,
        confidence: 1.0,
        reasoning: `Non-text message of type: ${messageType}`,
        suggestedResponse:
          'I can only process text messages. Please send me a text message describing what you need.',
      };
    }

    // Check for greetings
    if (this.containsKeywords(messageText, this.greetingKeywords)) {
      return {
        type: 'greeting' as IntentType,
        confidence: 0.9,
        reasoning: 'Message contains greeting keywords',
        suggestedResponse:
          'Hello! I can help you create a website. Just describe what kind of website you need.',
      };
    }

    // Check for help requests
    if (this.containsKeywords(messageText, this.helpKeywords)) {
      return {
        type: 'help' as IntentType,
        confidence: 0.8,
        reasoning: 'Message contains help-related keywords',
        suggestedResponse:
          "I can help you create websites! Tell me what kind of website you need and I'll build it for you.",
      };
    }

    // Check for status inquiries
    if (this.containsKeywords(messageText, this.statusKeywords)) {
      return {
        type: 'status' as IntentType,
        confidence: 0.8,
        reasoning: 'Message contains status inquiry keywords',
        suggestedResponse: 'Let me check the status of your website...',
      };
    }

    // Check for website creation requests
    if (
      this.containsKeywords(messageText, this.websiteKeywords) ||
      this.isWebsiteCreationRequest(messageText)
    ) {
      return {
        type: 'website_creation' as IntentType,
        confidence: 0.9,
        reasoning:
          'Message contains website-related keywords or describes a website creation request',
        suggestedResponse: "I'll help you create that website! Let me get started...",
      };
    }

    // Default to website creation for longer descriptive messages
    if (messageText.length > 50) {
      return {
        type: 'website_creation' as IntentType,
        confidence: 0.7,
        reasoning: 'Longer message likely describes a website requirement',
        suggestedResponse: 'I understand you want a website. Let me create that for you...',
      };
    }

    // Fallback to other
    return {
      type: 'other' as IntentType,
      confidence: 0.5,
      reasoning: 'Unable to classify message clearly',
      suggestedResponse:
        "I'm not sure what you need. Could you please tell me what kind of website you'd like me to create?",
    };
  }

  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  private isWebsiteCreationRequest(text: string): boolean {
    // Look for patterns that indicate website creation requests
    const patterns = [
      /create.*for/i,
      /build.*for/i,
      /make.*for/i,
      /need.*for/i,
      /want.*for/i,
      /i.*need/i,
      /i.*want/i,
      /can you.*make/i,
      /can you.*build/i,
      /can you.*create/i,
    ];

    return patterns.some(pattern => pattern.test(text));
  }
}
