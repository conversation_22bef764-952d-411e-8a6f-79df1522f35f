import type { VercelRequest, VercelResponse } from '@vercel/node';
import type {
  WhatsAppWebhook,
  ClassifiedIntent,
  WebhookForwardingResult,
} from '@whatsite-bot/core';

import { IntentClassifier } from '../lib/intent-classifier';
import { WebhookForwarder } from '../lib/webhook-forwarder';
import { MessageParser } from '../lib/message-parser';
import { WebhookValidator } from '../lib/validation';

const intentClassifier = new IntentClassifier();
const webhookForwarder = new WebhookForwarder();
const messageParser = new MessageParser();
const webhookValidator = new WebhookValidator();

export default async function handler(req: VercelRequest, res: VercelResponse): Promise<void> {
  try {
    // Handle webhook verification
    if (req.method === 'GET') {
      const mode = req.query['hub.mode'];
      const token = req.query['hub.verify_token'];
      const challenge = req.query['hub.challenge'];

      if (mode === 'subscribe' && token === process.env.WEBHOOK_VERIFY_TOKEN) {
        console.log('Webhook verified');
        res.status(200).send(challenge);
        return;
      }

      res.status(403).send('Forbidden');
      return;
    }

    // Handle webhook events
    if (req.method === 'POST') {
      // Validate webhook signature
      const isValid = webhookValidator.validateSignature(req);
      if (!isValid) {
        console.error('Invalid webhook signature');
        res.status(403).send('Invalid signature');
        return;
      }

      const webhook = req.body as WhatsAppWebhook;

      // Quick response to WhatsApp
      res.status(200).send('OK');

      // Process webhook asynchronously
      await processWebhook(webhook);
    } else {
      res.status(405).send('Method Not Allowed');
    }
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).send('Internal Server Error');
  }
}

async function processWebhook(webhook: WhatsAppWebhook): Promise<void> {
  try {
    // Parse the webhook for messages
    const messages = messageParser.extractMessages(webhook);

    if (messages.length === 0) {
      console.log('No messages found in webhook');
      return;
    }

    // Process each message
    for (const message of messages) {
      try {
        // Classify the message intent
        const classifiedIntent: ClassifiedIntent = intentClassifier.classifyMessage(message);

        // Forward to bot-engine
        const forwardingResult: WebhookForwardingResult = await webhookForwarder.forwardToBot({
          originalWebhook: webhook,
          message,
          classifiedIntent,
          timestamp: new Date().toISOString(),
        });

        if (!forwardingResult.success) {
          console.error('Failed to forward message to bot-engine:', forwardingResult.error);
        }
      } catch (error) {
        console.error('Error processing message:', error);
      }
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
  }
}
