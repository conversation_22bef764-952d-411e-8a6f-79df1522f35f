# Webhook Handler

The webhook-handler is a lightweight Vercel function that receives WhatsApp webhooks, classifies message intents, and forwards them to the bot-engine running on Fly.io.

## Architecture

This follows the two-app architecture:

1. **webhook-handler** (Vercel) - Fast webhook processing and intent classification
2. **bot-engine** (Fly.io) - Long-running website creation and session management

## Features

- **Intent Classification**: Automatically classifies incoming messages as greetings, help requests, website creation, status inquiries, or other
- **Webhook Forwarding**: Forwards classified messages to the bot-engine with retry logic
- **Security**: Validates webhook signatures from WhatsApp
- **Performance**: Lightweight function optimized for quick response times
- **Reliability**: Retry mechanism with exponential backoff

## Components

### `/api/whatsapp.ts`
Main Vercel function that:
- Handles webhook verification
- Validates webhook signatures
- Processes incoming messages
- Forwards to bot-engine

### `/lib/intent-classifier.ts`
Classifies message intents using keyword matching:
- `greeting` - Hello, hi, good morning
- `help` - Help requests and how-to questions
- `website_creation` - Website creation requests
- `status` - Status inquiries
- `other` - Fallback for unclassified messages

### `/lib/webhook-forwarder.ts`
Forwards processed webhooks to bot-engine:
- Retry logic with exponential backoff
- Health checking
- Error handling

### `/lib/message-parser.ts`
Extracts messages from WhatsApp webhook payload:
- Handles different message types
- Extracts contact information
- Validates message structure

### `/lib/validation.ts`
Security validation:
- HMAC signature verification
- Webhook structure validation
- Phone number extraction

## Environment Variables

Required environment variables in Vercel:

```
WEBHOOK_VERIFY_TOKEN=your_verification_token
WEBHOOK_SECRET=your_webhook_secret
BOT_ENGINE_URL=https://your-bot-engine.fly.dev
BOT_ENGINE_TOKEN=your_bot_engine_auth_token
```

## Deployment

### Deploy to Vercel

1. Install Vercel CLI:
```bash
npm i -g vercel
```

2. Deploy from webhook-handler directory:
```bash
cd apps/webhook-handler
vercel --prod
```

3. Set environment variables:
```bash
vercel env add WEBHOOK_VERIFY_TOKEN
vercel env add WEBHOOK_SECRET
vercel env add BOT_ENGINE_URL
vercel env add BOT_ENGINE_TOKEN
```

### Configure WhatsApp Webhook

1. Set webhook URL in WhatsApp Business API:
```
https://your-deployment.vercel.app/api/whatsapp
```

2. Set verify token to match `WEBHOOK_VERIFY_TOKEN`

## Integration with Bot-Engine

The webhook-handler forwards messages to the bot-engine at `/webhook` endpoint with this payload:

```typescript
{
  originalWebhook: WhatsAppWebhook;
  message: WhatsAppMessage;
  classifiedIntent: ClassifiedIntent;
  timestamp: string;
}
```

The bot-engine receives this and:
1. Creates or retrieves user session
2. Processes the message based on intent
3. Manages website creation workflow
4. Sends responses back to WhatsApp

## Monitoring

The webhook-handler includes:
- Health check endpoint for bot-engine
- Error logging and retry mechanisms
- Performance monitoring through Vercel analytics

## Development

Run locally:
```bash
npm run dev
```

Test webhook:
```bash
curl -X POST http://localhost:3000/api/whatsapp \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

## Migration from Monolithic Approach

This replaces the original 1164-line `api/whatsapp.ts` monolithic handler with:
- Separated concerns (intent classification vs. processing)
- Better scalability (Vercel for quick responses, Fly.io for long-running tasks)
- Improved maintainability through modular architecture
- Enhanced reliability through retry mechanisms