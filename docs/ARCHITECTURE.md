# WhatsApp Website Bot - Architecture Documentation

## Overview

This project is a modular WhatsApp bot that generates and deploys websites using AI, with support for iterative editing through follow-up messages and automatic version control via GitHub. The bot intelligently classifies user intent to determine whether to create new sites or edit existing ones, maintaining session state through Supabase and tracking all changes in Git repositories.

## Project Structure

```
whatsite-bot/
├── api/
│   └── whatsapp.js          # Main Vercel serverless function handler
├── lib/
│   ├── config.js            # API clients initialization and environment validation
│   ├── intent-classifier.js # AI-powered intent classification (new/edit)
│   ├── database.js          # Supabase session and project management
│   ├── transcription.js     # Audio transcription using OpenAI Whisper
│   ├── website-generator.js # Website generation and editing using Google Gemini
│   ├── deployment.js        # Direct Vercel deployment (fallback)
│   ├── github.js            # GitHub repository management and version control
│   ├── vercel-git.js        # Git-based Vercel deployments
│   ├── messaging.js         # WhatsApp messaging utilities
│   └── utils.js             # Utility functions and helpers
├── package.json
├── vercel.json
├── README.md
├── GITHUB_SETUP.md          # GitHub integration setup guide
└── ARCHITECTURE.md
```

## Module Breakdown

### 1. [`api/whatsapp.js`](api/whatsapp.js) - Main Handler
**Responsibilities:**
- Request routing and orchestration
- Intent-based workflow management
- Error handling and timeout management
- Coordinating new site creation and editing workflows

**Key Functions:**
- [`handler()`](api/whatsapp.js:32) - Main Vercel serverless function
- [`parseIncomingRequest()`](api/whatsapp.js:93) - Parse WhatsApp webhook data
- [`generateAndDeployWebsite()`](api/whatsapp.js:133) - Orchestrate new website creation
- [`editAndRedeployWebsite()`](api/whatsapp.js:156) - Orchestrate website editing
- [`handleProcessError()`](api/whatsapp.js:184) - Handle generation/deployment errors

### 2. [`lib/config.js`](lib/config.js) - Configuration Management
**Responsibilities:**
- API client initialization
- Environment variable validation (including Supabase)
- Configuration centralization

**Key Functions:**
- [`initializeClients()`](lib/config.js:12) - Initialize Twilio, OpenAI, Google AI clients
- [`validateEnvironment()`](lib/config.js:30) - Validate required environment variables

### 3. [`lib/intent-classifier.js`](lib/intent-classifier.js) - Intent Classification
**Responsibilities:**
- AI-powered message analysis using Gemini 2.0 Flash
- Determining user intent (new site vs edit)
- Context-aware classification based on user history

**Key Functions:**
- [`classifyIntent()`](lib/intent-classifier.js:11) - Main intent classification function
- [`containsEditKeywords()`](lib/intent-classifier.js:85) - Keyword-based fallback detection

### 4. [`lib/database.js`](lib/database.js) - Session Management
**Responsibilities:**
- Supabase database integration
- User project storage and retrieval
- Session state management
- Project history tracking

**Key Functions:**
- [`getLastProject()`](lib/database.js:25) - Retrieve user's most recent project
- [`saveProject()`](lib/database.js:48) - Save new project to database
- [`updateProject()`](lib/database.js:80) - Update existing project
- [`getUserProjects()`](lib/database.js:108) - Get user's project history

### 5. [`lib/transcription.js`](lib/transcription.js) - Audio Processing
**Responsibilities:**
- WhatsApp audio file download
- OpenAI Whisper transcription
- Temporary file management

**Key Functions:**
- [`transcribeAudio()`](lib/transcription.js:9) - Complete audio transcription workflow

### 6. [`lib/website-generator.js`](lib/website-generator.js) - AI Website Generation & Editing
**Responsibilities:**
- Google Gemini AI integration (2.5 Pro for generation)
- HTML generation and editing with context awareness
- Response cleanup and validation
- Fallback HTML generation

**Key Functions:**
- [`generateWebsite()`](lib/website-generator.js:11) - Main website generation/editing
- [`cleanupGeneratedHTML()`](lib/website-generator.js:116) - Clean AI response
- [`validateAndFixHTML()`](lib/website-generator.js:137) - Validate HTML structure
- [`generateFallbackHTML()`](lib/website-generator.js:189) - Create fallback pages

### 7. [`lib/deployment.js`](lib/deployment.js) - Direct Vercel Integration (Fallback)
**Responsibilities:**
- Direct Vercel API integration (fallback when GitHub integration unavailable)
- HTML enhancement with CSS
- Direct deployment and redeployment
- URL extraction

**Key Functions:**
- [`deployToVercel()`](lib/deployment.js:12) - Main deployment function
- [`enhanceHTMLStyles()`](lib/deployment.js:81) - Add CSS enhancements
- [`createDeploymentBody()`](lib/deployment.js:197) - Prepare Vercel API payload
- [`extractDeploymentUrl()`](lib/deployment.js:219) - Extract deployment URL

### 8. [`lib/github.js`](lib/github.js) - GitHub Repository Management
**Responsibilities:**
- GitHub API integration for repository management
- Repository creation and file management
- Commit operations with meaningful messages
- Content retrieval for editing workflows

**Key Functions:**
- [`createRepository()`](lib/github.js:15) - Create new GitHub repository with initial files
- [`updateRepository()`](lib/github.js:45) - Update repository with new content
- [`getRepositoryContent()`](lib/github.js:75) - Fetch current repository content
- [`generateCommitMessage()`](lib/github.js:285) - Generate meaningful commit messages

### 9. [`lib/vercel-git.js`](lib/vercel-git.js) - Git-Based Vercel Deployments
**Responsibilities:**
- Vercel Git integration for automatic deployments
- Repository connection to Vercel projects
- Git-based deployment triggering
- Fallback to direct deployment when needed

**Key Functions:**
- [`connectGitToVercel()`](lib/vercel-git.js:12) - Connect GitHub repo to Vercel project
- [`triggerGitDeployment()`](lib/vercel-git.js:35) - Trigger deployment from Git
- [`fallbackDirectDeployment()`](lib/vercel-git.js:165) - Fallback to direct deployment
- [`validateGitIntegrationConfig()`](lib/vercel-git.js:195) - Validate GitHub integration setup

### 10. [`lib/messaging.js`](lib/messaging.js) - WhatsApp Communication
**Responsibilities:**
- WhatsApp message sending
- Status message management with editing support
- User communication templates

**Key Functions:**
- [`sendWhatsappMessage()`](lib/messaging.js:11) - Send individual messages
- [`sendStatusMessage()`](lib/messaging.js:31) - Send templated status updates

### 11. [`lib/utils.js`](lib/utils.js) - Utility Functions
**Responsibilities:**
- String manipulation (slugify)
- Project name generation
- Timeout management
- Diagnostic logging
- Fallback HTML generation

**Key Functions:**
- [`slugify()`](lib/utils.js:10) - Convert text to URL-friendly format
- [`generateProjectName()`](lib/utils.js:27) - AI-generated project names
- [`createTimeoutPromise()`](lib/utils.js:64) - Timeout promise utility
- [`generateTimeoutFallbackHTML()`](lib/utils.js:78) - Timeout fallback pages

## Data Flow

### New Site Creation Flow (with GitHub Integration)
```
WhatsApp Message → parseIncomingRequest() → transcribeAudio() (if audio)
                                        ↓
                    getLastProject() → classifyIntent() → "new_site"
                                        ↓
generateProjectName() → generateWebsite() → createRepository() → connectGitToVercel()
                                        ↓                    ↓
                                   saveProject()    → sendStatusMessage() → "deployment_complete"
```

### Site Editing Flow (with GitHub Integration)
```
WhatsApp Message → parseIncomingRequest() → transcribeAudio() (if audio)
                                        ↓
                    getLastProject() → classifyIntent() → "edit_site"
                                        ↓
getRepositoryContent() → generateWebsite(with context) → updateRepository() → updateProject()
                                        ↓                           ↓
                                   Auto Git Deploy    → sendStatusMessage() → "update_complete"
```

### Fallback Flow (GitHub Integration Unavailable)
```
Any Step → GitHub Error → fallbackDirectDeployment() → deployToVercel()
                                        ↓
                    Continue with original workflow
```

### Error Handling Flow
```
Any Step → Error → handleProcessError() → Fallback HTML → Deploy Fallback
                                        ↓
                    sendStatusMessage() → "error" or "timeout_complete"
```

## Key Improvements

### 1. **Separation of Concerns**
- Each module has a single, well-defined responsibility
- Easier to test individual components
- Reduced coupling between different functionalities

### 2. **Error Handling**
- Centralized error handling in main handler
- Module-specific error handling where appropriate
- Graceful degradation with fallback content

### 3. **Maintainability**
- Smaller, focused files are easier to understand and modify
- Clear module boundaries make debugging easier
- Consistent function naming and documentation

### 4. **Reusability**
- Modules can be easily reused or replaced
- Functions are more focused and testable
- Configuration is centralized and easily modified

### 5. **Scalability**
- Easy to add new features by creating new modules
- Existing modules can be enhanced without affecting others
- Clear interfaces between modules

## Environment Variables

The following environment variables are required:

```bash
# Twilio (WhatsApp)
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+***********

# OpenAI (voice transcription)
OPENAI_API_KEY=sk-...

# Google AI (website generation & intent classification)
GOOGLE_API_KEY=AIza...

# Vercel (deployment)
VERCEL_API_TOKEN=...
VERCEL_TEAM_ID=... (optional, for team accounts)

# Supabase (session management)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=eyJ...

# GitHub (version control) - Optional but recommended
GITHUB_TOKEN=ghp_... (Personal Access Token with repo permissions)
```

> **Note**: GitHub integration is optional. If not configured, the bot gracefully falls back to direct Vercel deployments.

## Database Schema

The Supabase database uses the following table structure:

```sql
CREATE TABLE user_projects (
    id SERIAL PRIMARY KEY,
    phone_number VARCHAR(30) NOT NULL,  -- Increased from 20 to handle international numbers
    project_name VARCHAR(150) NOT NULL, -- Increased from 100 for longer project names
    description TEXT,
    url VARCHAR(1000),                  -- Increased from 500 for longer Vercel URLs
    html_content TEXT,
    github_repo_url VARCHAR(500),       -- GitHub repository URL
    github_repo_name VARCHAR(150),      -- Full repository name (org/repo)
    last_commit_sha VARCHAR(40),        -- Latest commit SHA for tracking
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_projects_phone ON user_projects(phone_number);
CREATE INDEX idx_user_projects_active ON user_projects(phone_number, is_active);
CREATE INDEX idx_user_projects_github ON user_projects(github_repo_name);
```

## Deployment

The refactored code maintains full compatibility with Vercel's serverless functions:

1. The main handler remains at [`api/whatsapp.js`](api/whatsapp.js)
2. All modules are in the [`lib/`](lib/) directory
3. ES6 imports are used throughout
4. No changes needed to [`vercel.json`](vercel.json) or [`package.json`](package.json)

## Testing Considerations

With the new modular structure, you can now:

- Unit test individual modules in isolation
- Mock dependencies easily
- Test error scenarios more effectively
- Validate configuration separately from business logic

## Key Features

### Intent Classification
- Uses Gemini 2.0 Flash for fast, accurate intent detection
- Analyzes message context and user history
- Fallback logic for edge cases
- Confidence scoring for classification quality

### Session Management
- Persistent storage via Supabase
- Per-user project tracking
- Active project management
- Project history and retrieval

### Version Control Integration
- **Automatic Repository Creation**: Each website gets its own GitHub repository
- **Meaningful Commits**: AI-generated commit messages based on user requests
- **Git-Based Deployments**: Vercel deploys directly from GitHub for better reliability
- **Change Tracking**: Full version history for every website modification
- **Fallback Support**: Gracefully falls back to direct deployment if GitHub is unavailable

### Smart Editing
- **Context-Aware Modification**: Fetches latest code from GitHub before editing
- **Consistent Updates**: Lower temperature for reliable edits
- **Version Tracking**: Every change is committed with descriptive messages
- **Fallback Content**: Uses database content if GitHub is unavailable
- **Design Consistency**: Maintains styling and structure across edits

### Error Resilience
- **Comprehensive Timeout Handling**: 4.5-minute timeout with fallback strategies
- **GitHub Integration Fallback**: Automatic fallback to direct Vercel deployment
- **Graceful Degradation**: Continues operation when external services fail
- **Detailed Error Logging**: Comprehensive logging for debugging and monitoring

## GitHub Integration Benefits

### Professional Website Management
- Each generated website gets its own professional GitHub repository
- Proper version control with meaningful commit history
- README files automatically generated for each project
- Vercel configuration files included for easy deployment

### Enhanced Reliability
- Git-based deployments are more reliable than direct API deployments
- Automatic backup of all generated websites on GitHub
- Easy rollback capabilities through Git history
- Better deployment monitoring through Vercel's Git integration

### Future-Ready Architecture
- Ready for collaboration features (multiple users editing same site)
- Branch-based development for major changes
- Integration with GitHub Actions for advanced workflows
- Support for custom domains through Vercel's Git integration

## Future Enhancements

The modular structure and GitHub integration make it easy to add:

- **Multi-project management**: Allow users to switch between multiple active projects
- **Template system**: Pre-built website templates stored in GitHub
- **Branch-based editing**: Create feature branches for major changes
- **Rollback functionality**: Allow users to revert to previous versions
- **Collaboration**: Multi-user project editing with proper Git workflows
- **Advanced deployment**: Support for custom domains and advanced Vercel features
- **Analytics integration**: Track website performance and user engagement
- **Additional AI providers**: Support for other AI models and providers
- **Enhanced media support**: Image generation and optimization with Git LFS
- **Repository management**: Allow users to access their GitHub repositories directly