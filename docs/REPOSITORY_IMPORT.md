# GitHub Repository Import Feature

This document explains how to use the GitHub repository import functionality in the WhatsApp Website Bot.

## Overview

The repository import feature allows users to import existing GitHub repositories (that were previously created by the bot) into the database system, enabling them to continue editing these websites through WhatsApp messages.

## How It Works

### 1. Intent Detection
The bot automatically detects when a user wants to import a repository by analyzing their message for:
- GitHub URLs (e.g., `https://github.com/username/repo-name`)
- GitHub references (e.g., `github.com/username/repo-name`)
- Repository patterns (e.g., `username/repo-name`)

### 2. Repository Validation
When a repository is detected, the bot performs several validation checks:
- **Access Validation**: Verifies the repository exists and is accessible
- **Structure Validation**: Ensures the repository contains an `index.html` file
- **Bot Compatibility**: Confirms the repository was created by the bot

### 3. Import Process
If validation passes, the bot:
- Imports repository metadata and content
- Checks for existing imports to prevent duplicates
- Sets up Vercel deployment (if not already deployed)
- Saves the project to the database for future editing

## Usage Examples

### Import via GitHub URL
```
User: "I want to work on https://github.com/myusername/my-website"
Bot: "📁 GitHub repository detected: myusername/my-website
      🔍 Validating repository access...
      ✅ Repository access confirmed!
      📋 Checking repository structure...
      ✅ Repository structure is valid!
      📥 Importing repository data...
      ✅ Repository "my-website" imported successfully!
      
      📁 GitHub: https://github.com/myusername/my-website
      
      You can now edit it with follow-up messages!"
```

### Import via Repository Name
```
User: "Import myusername/my-portfolio"
Bot: [Same import process as above]
```

### Edit After Import
```
User: "Add a contact form to the website"
Bot: "🔧 I'll edit your "my-website" website: Add a contact form to the website
      🔧 Editing "my-website"...
      ✅ Website updated successfully!
      🌐 Updated site: https://my-website.vercel.app"
```

## Error Handling

The bot provides specific error messages for different scenarios:

### Repository Not Found
```
❌ Repository not found: username/repo-name

Please check the repository name or URL and try again.
```

### Access Denied
```
❌ Access denied to repository: username/repo-name

Please check repository permissions.
```

### Invalid Structure
```
❌ Repository "repo-name" doesn't contain an index.html file.

This bot works with HTML websites created by the bot.
```

### Already Imported
```
ℹ️ Repository "my-website" is already imported and active.

You can edit it with follow-up messages!
```

## Technical Implementation

### Files Modified
- **`lib/intent-classifier.js`**: Enhanced to detect GitHub URLs and repository references
- **`lib/github.js`**: Added repository validation and import functions
- **`lib/database.js`**: Added import-specific database operations
- **`lib/messaging.js`**: Added import-specific status messages
- **`api/whatsapp.js`**: Integrated import workflow into main handler

### Key Functions

#### Intent Classification
- `extractRepositoryInfo()`: Parses GitHub URLs and repository names
- Enhanced `classifyIntent()`: Returns `import_repo` intent with repository information

#### Repository Operations
- `validateRepositoryAccess()`: Checks repository existence and permissions
- `checkRepositoryStructure()`: Verifies repository has required files
- `getRepositoryMetadata()`: Retrieves repository information
- `importRepositoryData()`: Orchestrates complete import process

#### Database Operations
- `findProjectByRepo()`: Checks if repository is already imported
- `importProject()`: Creates database records from imported repository data

### Workflow Integration
The import functionality integrates seamlessly with existing bot workflows:
1. **Import**: User provides GitHub repository → Bot imports and sets up
2. **Edit**: User requests changes → Bot edits using existing edit workflow
3. **Deploy**: Changes are committed to GitHub and deployed via Vercel

## Benefits

### For Users
- **Continuity**: Continue working on previously created websites
- **No Setup Required**: Just provide the GitHub URL or repository name
- **Seamless Integration**: Imported repositories work exactly like new projects
- **Version Control**: Full GitHub integration with commit history

### For Developers
- **Robust Validation**: Multiple validation layers prevent errors
- **Error Resilience**: Comprehensive error handling and user feedback
- **Duplicate Prevention**: Automatic detection of already-imported repositories
- **Consistent Architecture**: Uses existing patterns and functions

## Configuration Requirements

The import feature requires the same environment variables as the main bot:
- `GITHUB_TOKEN`: For GitHub API access
- `VERCEL_API_TOKEN`: For deployment
- `SUPABASE_URL` and `SUPABASE_ANON_KEY`: For database operations

## Limitations

1. **Bot-Created Repositories Only**: Only works with repositories that contain `index.html` files
2. **Public/Accessible Repositories**: Repository must be accessible with the configured GitHub token
3. **Single HTML File**: Currently supports single-page websites with `index.html`
4. **GitHub Integration Required**: Requires valid GitHub token for repository operations

## Future Enhancements

Potential improvements for future versions:
- Support for multi-page websites
- Import from other Git providers (GitLab, Bitbucket)
- Batch import of multiple repositories
- Repository template detection and conversion
- Advanced structure validation for different website types