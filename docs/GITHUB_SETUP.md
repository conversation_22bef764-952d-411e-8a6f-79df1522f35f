# GitHub Integration Setup Guide

This guide will help you set up GitHub integration for your WhatsApp Website Bot, enabling automatic version control for all generated websites.

## Overview

With GitHub integration enabled, your bot will:
- Create individual GitHub repositories for each generated website
- Commit all website code to version control
- Connect repositories to Vercel for Git-based deployments
- Track changes with meaningful commit messages
- Enable proper version history for all generated sites

## Prerequisites

1. **GitHub Account**: You need a GitHub account with repository creation permissions
2. **Vercel Account**: Connected to your GitHub account for Git-based deployments

## Step 1: Create GitHub Personal Access Token

1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Set the following scopes:
   - `repo` (Full control of private repositories)
4. Copy the generated token (starts with `ghp_`)

## Step 2: Configure Environment Variables

Add this new environment variable to your `.env.local` file:

```bash
# GitHub Integration
GITHUB_TOKEN=ghp_your_personal_access_token_here
```

### Complete Environment Variables List

```bash
# Existing variables
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+***********
OPENAI_API_KEY=sk-...
GOOGLE_API_KEY=AIza...
VERCEL_API_TOKEN=...
VERCEL_TEAM_ID=... # Optional
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=eyJ...

# New GitHub variable
GITHUB_TOKEN=ghp_...
```

## Step 3: Update Database Schema

Run this SQL in your Supabase dashboard to add GitHub-related fields:

```sql
-- Add GitHub-related columns to existing table
ALTER TABLE user_projects 
ADD COLUMN github_repo_url VARCHAR(500),
ADD COLUMN github_repo_name VARCHAR(150),
ADD COLUMN last_commit_sha VARCHAR(40);

-- Add index for GitHub repo lookups
CREATE INDEX idx_user_projects_github ON user_projects(github_repo_name);

-- Update existing field sizes if needed (optional)
ALTER TABLE user_projects ALTER COLUMN phone_number TYPE VARCHAR(30);
ALTER TABLE user_projects ALTER COLUMN project_name TYPE VARCHAR(150);
ALTER TABLE user_projects ALTER COLUMN url TYPE VARCHAR(1000);
```

## Step 4: Deploy Updated Code

1. Deploy your updated bot to Vercel:
   ```bash
   vercel --prod
   ```

2. Verify the deployment includes all new environment variables

## Step 5: Test the Integration

1. Send a message to your WhatsApp bot: "Create a portfolio website"
2. Monitor the logs for GitHub repository creation
3. Check your personal GitHub account for the new repository
4. Verify the Vercel deployment is connected to the GitHub repo

## How It Works

### New Website Creation Flow

1. **User sends message** → Bot generates website
2. **GitHub repository created** → Individual repo for the website
3. **Initial commit** → HTML, README, and Vercel config committed
4. **Vercel connection** → Repository linked to Vercel for Git deployments
5. **Database updated** → GitHub repo info saved for future edits

### Website Editing Flow

1. **User sends edit request** → Bot fetches latest code from GitHub
2. **AI generates updates** → Using current code as context
3. **Commit changes** → New commit with descriptive message
4. **Automatic deployment** → Vercel deploys from Git automatically

## Repository Structure

Each generated website repository contains:

```
user-portfolio-site-abc123/
├── index.html              # Generated website
├── README.md              # Auto-generated project info
├── .gitignore             # Basic ignore file
└── vercel.json            # Vercel configuration
```

## Fallback Behavior

If GitHub integration fails for any reason, the bot will:
1. Log the error
2. Fall back to direct Vercel deployment (original behavior)
3. Continue to function normally
4. Retry GitHub integration on the next request

## Benefits

- **Version Control**: Full Git history for every website
- **Professional Appearance**: Generated sites have proper repositories
- **Better Deployments**: Vercel's Git integration provides superior deployment management
- **Collaboration Ready**: Future potential for sharing/forking websites
- **Automatic Backup**: All generated sites are backed up on GitHub
- **Change Tracking**: Every edit is tracked with meaningful commit messages

## Troubleshooting

### Common Issues

1. **"GITHUB_TOKEN environment variable is required"**
   - Ensure your GitHub token is properly set in environment variables
   - Verify the token has correct permissions

2. **"Repository creation failed"**
   - Verify your token has `repo` permissions
   - Ensure you haven't hit GitHub's repository limits
   - Check if repository name conflicts exist

3. **"Vercel connection failed"**
   - Verify your Vercel token has correct permissions
   - Check if the GitHub repository is accessible to Vercel

4. **Fallback to direct deployment**
   - This is normal behavior when GitHub integration isn't available
   - Check logs for specific GitHub integration errors

### Monitoring

Monitor your bot's performance by checking:
- Vercel function logs for GitHub API calls
- Your personal GitHub account for new repositories
- Vercel dashboard for Git-connected projects

## Security Considerations

- Store all tokens securely in environment variables
- Regularly rotate your GitHub personal access token
- Monitor repository creation for unusual activity
- Consider using a dedicated GitHub account for generated sites if you create many

## Cost Implications

- **GitHub**: Free for public repositories (recommended for generated sites)
- **Vercel**: Git-based deployments are included in all plans
- **API Limits**: GitHub has rate limits for API calls (5000/hour for authenticated requests)

## Future Enhancements

With this foundation, you can easily add:
- Branch-based editing for major changes
- Rollback functionality using Git history
- User access to their repository URLs
- Custom domain setup through Vercel
- Collaboration features for multi-user editing