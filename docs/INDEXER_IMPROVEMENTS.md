# Indexer Workflow Improvements

## Problem
The GitHub Actions indexer workflow had multiple critical failure points:

1. **Build failures preventing indexing**: Workflow would fail to run indexing when there were build issues
2. **Node.js version incompatibility**: `@google/genai@1.9.0` requires Node.js >=20, but workflow used v18
3. **Missing indexing script**: User repositories didn't have the required indexing script
4. **Dependency conflicts**: LlamaIndex wrapper conflicted with direct Google GenAI usage
5. **Poor error handling**: Single failures would crash the entire indexing process

## Analysis
After examining the codebase and identifying root causes:

1. **Current build script** (`package.json`): Just creates an empty `public/index.html` placeholder
2. **Build output value**: Not valuable for Gemini's editing capabilities - it's just a placeholder
3. **Node.js compatibility**: `@google/genai@1.9.0` requires Node.js >=20 for proper functionality
4. **Embedding generation**: LlamaIndex wrapper was causing dependency conflicts
5. **Error propagation**: Individual failures were stopping the entire indexing process

## Solution
Modified the indexer workflow to:

### 1. Enhanced GitHub Actions Workflow (`.github/workflows/indexer.yml`)
- Added `npm ci` to install project dependencies (with `continue-on-error: true`)
- Added optional build step that won't block indexing if it fails
- Added `if: always()` to ensure indexing runs regardless of previous step failures
- Added npm caching for better performance

### 2. Improved Indexing Script (`scripts/indexRepo.mjs`)
- **Better error handling**: More detailed error logging with stack traces
- **Resilient processing**: Continue indexing even if individual files fail
- **Improved database operations**: Smaller batch sizes and better error recovery
- **Progress tracking**: Better logging of processing progress
- **Failure recovery**: Update project status appropriately on failure

### 3. Generated Script Fixes (`lib/github.ts`)
- **Node.js version upgrade**: Updated generated workflow to use Node.js 20 instead of 18
- **Fixed dependency conflicts**: Removed LlamaIndex wrapper, use Google GenAI directly
- **Corrected embedding generation**: Use `text-embedding-004` model with proper API calls
- **Updated embedding dimensions**: Changed from 1536 to 768 dimensions for text-embedding-004
- **Enhanced error recovery**: Better fallback mechanisms and status updates

## Key Changes

### Workflow Changes
```yaml
# Before: No build step, basic error handling
- name: Run repository indexer
  run: node scripts/indexRepo.mjs

# After: Optional build, guaranteed indexing
- name: Build project (optional)
  run: npm run build
  continue-on-error: true
  
- name: Run repository indexer
  run: node scripts/indexRepo.mjs
  if: always()  # Always run indexing
```

### Enhanced Code Chunking and Embedding
```javascript
// Using LlamaIndex CodeASTNodeParser for better code-aware chunking
const { CodeASTNodeParser } = require('@llamaindex/node');
const { GeminiEmbedding } = require('@llamaindex/google');

this.parser = new CodeASTNodeParser({ maxLines: 120 });
this.embedModel = new GeminiEmbedding({
  model: 'gemini-embedding-exp-03-07',
});

// AST-aware chunking for code files
chunkContent(content, filePath) {
  if (this.isCodeFile(filePath)) {
    return this.parser.splitText(content); // Function/class-level chunks
  }
  // Fallback to paragraph-based chunking for non-code files
}

// Proper embedding generation with 1536 dimensions
async generateEmbedding(text) {
  try {
    const embeddings = await this.embedModel.getTextEmbeddingsBatch([text]);
    const embedding = embeddings[0];
    return embedding ? embedding.slice(0, this.vectorDim) : new Array(this.vectorDim).fill(0);
  } catch (error) {
    return new Array(1536).fill(0); // Correct dimensions for gemini-embedding-exp-03-07
  }
}
```

### Script Improvements
- **File processing**: Continue if individual files fail to process
- **Database operations**: Use smaller batches (25 instead of 50) with retry logic
- **Error recovery**: Clean up project status on failure
- **Progress reporting**: Better visibility into processing status

## Root Cause Analysis

### Issue 1: Node.js Version Incompatibility
- **Problem**: `@google/genai@1.9.0` requires Node.js >=20, workflow used v18
- **Solution**: Updated both main workflow and generated workflow to use Node.js 20
- **Impact**: Eliminates installation failures in user repositories

### Issue 2: Inadequate Code Chunking
- **Problem**: Simple text-based chunking didn't respect code structure (functions, classes)
- **Solution**: Use LlamaIndex `CodeASTNodeParser` for AST-aware chunking with proper embedding wrapper
- **Impact**: Better code understanding and more meaningful chunks for vector retrieval

### Issue 3: Missing Error Handling
- **Problem**: Single failures would crash entire indexing process
- **Solution**: Added comprehensive try-catch blocks and graceful degradation
- **Impact**: Partial indexing success instead of complete failure

### Issue 4: Inconsistent Embedding Dimensions
- **Problem**: Fallback vectors didn't match the expected 1536 dimensions for gemini-embedding-exp-03-07
- **Solution**: Ensure all embedding vectors (including fallbacks) use consistent 1536 dimensions
- **Impact**: Consistent embedding dimensions prevent database errors and improve retrieval accuracy

## Benefits

1. **Guaranteed indexing**: Indexing will always run, regardless of build failures
2. **Better reliability**: More resilient to individual file or batch failures
3. **Improved debugging**: Better error messages and progress tracking
4. **Faster execution**: Added npm caching and optimized batch sizes
5. **Graceful degradation**: Partial success is better than complete failure
6. **Dependency compatibility**: Fixed Node.js and package version conflicts
7. **AST-aware code chunking**: Better code understanding with function/class-level chunks
8. **Consistent embedding dimensions**: Proper 1536-dimensional vectors for all embeddings

## Build Output Decision

The build step is kept as optional because:
- Current build output is just a placeholder file
- Not valuable for Gemini's editing capabilities
- Real value comes from indexing the source code, not build artifacts
- Keeping it allows for future expansion if build output becomes valuable

## Testing

The changes ensure that:
- Indexing runs even if `npm ci` fails (Node.js 20 compatibility)
- Indexing runs even if `npm run build` fails (optional build step)
- Individual file processing failures don't stop the entire process
- Database batch failures don't prevent other batches from succeeding
- Project status is properly updated on both success and failure
- Embedding generation works with LlamaIndex wrapper for gemini-embedding-exp-03-07
- Fallback vectors use correct dimensions (1536 for gemini-embedding-exp-03-07)
- AST-aware chunking produces meaningful code segments
- Generated workflows in user repositories use compatible Node.js version

## Recommended Testing Scenarios

1. **Test with build failures**: Verify indexing continues when `npm run build` fails
2. **Test with dependency issues**: Ensure Node.js 20 resolves `@google/genai` installation
3. **Test embedding generation**: Verify direct Google GenAI usage works correctly
4. **Test error recovery**: Confirm partial failures don't prevent successful indexing
5. **Test generated workflows**: Validate that user repositories get working indexing scripts
6. **Test database operations**: Ensure smaller batch sizes improve reliability