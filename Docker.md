# Docker Setup for WhatsApp Bot Engine

This document provides comprehensive instructions for setting up and running the WhatsApp Bot Engine using Docker.

## Prerequisites

- Docker (version 20.10+)
- Docker Compose (version 2.0+)
- Git

## Quick Start

### Development Environment

1. **Clone the repository and navigate to the project directory:**
   ```bash
   git clone <repository-url>
   cd whatsite-bot
   ```

2. **Create environment file:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development environment:**
   ```bash
   # Using main docker-compose for full development setup
   docker-compose up --build

   # Or using the simplified development setup
   docker-compose -f docker-compose.dev.yml up --build
   ```

4. **Access the application:**
   - Bot Engine: http://localhost:3000
   - Health Check: http://localhost:3000/health
   - Redis: localhost:6379
   - PostgreSQL: localhost:5432
   - Adminer (DB UI): http://localhost:8080

### Production Environment

1. **Build production image:**
   ```bash
   docker build -f apps/bot-engine/Dockerfile -t whatsite-bot-engine .
   ```

2. **Run production container:**
   ```bash
   docker run -p 3000:3000 \
     -e NODE_ENV=production \
     -e TWILIO_ACCOUNT_SID=your_sid \
     -e TWILIO_AUTH_TOKEN=your_token \
     -e GITHUB_TOKEN=your_token \
     whatsite-bot-engine
   ```

## Docker Configuration

### Multi-Stage Dockerfile

The Dockerfile uses a multi-stage build process:

1. **Dependencies Stage**: Installs system dependencies and Node.js packages
2. **Builder Stage**: Builds the TypeScript application
3. **Production Stage**: Creates optimized runtime image

### Key Features

- **Alpine Linux**: Lightweight base image
- **Multi-stage build**: Optimized for production
- **Non-root user**: Security best practices
- **Health checks**: Built-in health monitoring
- **Volume mounts**: Persistent data storage

### Directory Structure

```
/app/
├── apps/bot-engine/          # Main application
├── packages/                 # Shared packages
├── workspaces/              # Git workspaces (volume)
├── sessions/                # Session data (volume)
└── node_modules/            # Dependencies
```

## Environment Variables

### Required Variables

```bash
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_phone_number

# GitHub Configuration
GITHUB_TOKEN=your_github_token

# AI Configuration
GEMINI_API_KEY=your_gemini_key
OPENAI_API_KEY=your_openai_key
```

### Optional Variables

```bash
# Application
NODE_ENV=production
PORT=3000
WORKSPACE_BASE_DIR=/app/workspaces
SESSION_STORAGE_DIR=/app/sessions

# Database
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379

# Security
JWT_SECRET=your_jwt_secret
WEBHOOK_SECRET=your_webhook_secret
```

## Docker Compose Services

### Development Services

- **bot-engine**: Main application with hot reload
- **redis**: Session and cache storage
- **postgres**: Development database
- **adminer**: Database administration UI

### Production Services

- **bot-engine**: Production-optimized application
- **redis**: Persistent cache and session storage

## Available Scripts

### Root Package Scripts

```bash
# Docker operations
npm run docker:build       # Build production image
npm run docker:run         # Run production container
npm run docker:dev         # Start development environment
npm run docker:stop        # Stop all containers
npm run docker:clean       # Clean up containers and volumes
npm run docker:logs        # View application logs
```

### Bot Engine Scripts

```bash
# Local Docker operations
npm run docker:build       # Build container
npm run docker:run         # Run container
npm run docker:dev         # Development with hot reload
npm run docker:logs        # View logs
```

## Data Persistence

### Volumes

- **bot-workspaces**: Git workspace data
- **bot-sessions**: Session and state data
- **redis-data**: Redis persistence
- **postgres-data**: Database data

### Backup

```bash
# Backup volumes
docker run --rm -v bot-workspaces:/data -v $(pwd):/backup alpine tar czf /backup/workspaces-backup.tar.gz /data

# Restore volumes
docker run --rm -v bot-workspaces:/data -v $(pwd):/backup alpine tar xzf /backup/workspaces-backup.tar.gz -C /
```

## Health Checks

### Application Health

- **Endpoint**: `GET /health`
- **Response**: Status, uptime, memory usage
- **Docker**: Built-in health check every 30s

### Service Health

- **Redis**: `redis-cli ping`
- **PostgreSQL**: `pg_isready`
- **Application**: HTTP health check

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change port mapping in docker-compose.yml
2. **Memory issues**: Increase Docker memory limit
3. **Permission errors**: Check file permissions in volumes
4. **Build failures**: Clear Docker cache with `docker system prune`

### Debugging

```bash
# View container logs
docker-compose logs -f bot-engine

# Execute commands in container
docker-compose exec bot-engine sh

# Check container status
docker-compose ps

# Inspect container
docker inspect <container-id>
```

### Performance Optimization

1. **Use .dockerignore**: Exclude unnecessary files
2. **Layer caching**: Order Dockerfile instructions efficiently
3. **Multi-stage builds**: Reduce final image size
4. **Volume mounts**: Use for persistent data only

## Security Considerations

### Production Security

- **Non-root user**: Application runs as `botengine` user
- **Minimal base image**: Alpine Linux for security
- **Environment variables**: Never hardcode secrets
- **Health checks**: Monitor application health
- **Resource limits**: Set memory and CPU limits

### Development Security

- **Local environment**: Use separate development secrets
- **Volume permissions**: Ensure proper file permissions
- **Network isolation**: Use Docker networks
- **Regular updates**: Keep base images updated

## Deployment

### Fly.io Deployment

The application includes `fly.toml` for Fly.io deployment:

```bash
# Deploy to Fly.io
cd apps/bot-engine
fly deploy
```

### Custom Deployment

1. **Build image**: `docker build -t your-registry/bot-engine .`
2. **Push image**: `docker push your-registry/bot-engine`
3. **Deploy**: Use your container orchestration platform

## Monitoring

### Metrics

- **Health endpoint**: `/health`
- **Ready endpoint**: `/ready`
- **Metrics endpoint**: `/metrics`

### Logging

- **Structured logging**: JSON format in production
- **Log levels**: Configurable via environment
- **Container logs**: Available via `docker logs`

## Development Workflow

### Local Development

```bash
# Start development environment
docker-compose up --build

# Make changes to source code
# Changes are automatically reloaded

# Run tests
docker-compose exec bot-engine npm test

# Check types
docker-compose exec bot-engine npm run typecheck
```

### Building for Production

```bash
# Build production image
docker build -f apps/bot-engine/Dockerfile -t whatsite-bot-engine .

# Test production image
docker run -p 3000:3000 --env-file .env whatsite-bot-engine

# Deploy to registry
docker tag whatsite-bot-engine your-registry/whatsite-bot-engine
docker push your-registry/whatsite-bot-engine
```

## Contributing

When contributing to the Docker setup:

1. **Test changes**: Ensure both development and production builds work
2. **Update documentation**: Keep this README up to date
3. **Follow conventions**: Use consistent naming and structure
4. **Security review**: Consider security implications of changes

## Support

For issues with the Docker setup:

1. Check the troubleshooting section
2. Review container logs
3. Verify environment configuration
4. Check Docker and system resources