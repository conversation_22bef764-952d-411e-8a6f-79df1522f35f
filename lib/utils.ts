/**
 * Utility functions
 */
import { GoogleGenAI } from "@google/genai";

/**
 * Converts a string into a URL-friendly slug.
 * @param {string} text - The text to convert.
 * @returns {string} - The slugified text.
 */
export function slugify(text: string): string {
    if (!text) return '';
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-') // Replace spaces with -
        .replace(/[^\w\-\.]+/g, '-') // Replace non-word chars (except dots) with -
        .replace(/\.+/g, '-') // Replace dots with -
        .replace(/\-\-+/g, '-') // Replace multiple - with single -
        .replace(/^-+|-+$/g, '') // Remove leading and trailing dashes
        .substring(0, 50); // Truncate to 50 chars
}

/**
 * Generates a creative and URL-friendly project name using Gemini.
 * @param {string} prompt - The user's prompt for the website.
 * @param {GoogleGenAI} genAI - The initialized Google AI client.
 * @returns {Promise<string>} - A unique, URL-friendly project name.
 */
export async function generateProjectName(prompt: string, genAI: GoogleGenAI): Promise<string> {
    console.log("=== PROJECT NAME GENERATION DIAGNOSTICS ===");
    console.log(`Generating project name for prompt: "${prompt ? prompt.substring(0, 50) : 'undefined'}..."`);
    
    // Handle empty or invalid prompts
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
        console.log("Invalid prompt provided, using fallback");
        return `whatsite-${Date.now()}`;
    }
    
    const fullPrompt = `Based on the following user request, generate a short, creative, and unique project name. The name must be a single, URL-friendly string (e.g., "my-awesome-site"). Do not include any explanation, just return the name. Request: "${prompt}"`;
    
    try {
        if (!genAI) {
            console.log("No AI client provided, using fallback");
            return `whatsite-${Date.now()}`;
        }
        
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: fullPrompt
        });
        const text = result.text?.trim() || '';
        
        console.log(`Generated project name: "${text}"`);
        
        // Check if the name is valid
        if (!text || text.length < 3) {
            console.log("Generated name is too short or empty, using fallback");
            return `whatsite-${Date.now()}`;
        }
        
        // Fallback to slugify if the AI returns an empty or invalid name
        const slugified = slugify(text);
        console.log(`Slugified project name: "${slugified}"`);
        return slugified || `whatsite-${Date.now()}`;
    } catch (error) {
        console.error("Error generating project name:", error);
        return `whatsite-${Date.now()}`;
    }
}

/**
 * Creates a timeout promise that rejects after specified milliseconds
 * @param {number} ms - Milliseconds to wait before rejecting
 * @param {string} message - Error message for timeout
 * @returns {Promise<never>} - Promise that rejects after timeout
 */
export function createTimeoutPromise(ms: number, message: string = 'Operation timed out'): Promise<never> {
    return new Promise((_, reject) => {
        setTimeout(() => {
            reject(new Error(`${message} after ${ms/1000} seconds`));
        }, ms);
    });
}

/**
 * Generates a fallback HTML page for timeout scenarios
 * @param {string} prompt - Original user prompt
 * @param {string} projectName - Project name
 * @returns {string} - Fallback HTML content
 */
export function generateTimeoutFallbackHTML(prompt: string, projectName: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${projectName}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; }
        p { margin-bottom: 20px; }
        .note { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${projectName}</h1>
        <div class="note">
            <p>We're working on creating your website based on: "${prompt}"</p>
            <p>The process is taking longer than expected. Please check back soon!</p>
        </div>
        <p>This is a temporary page. Your full website will be available soon!</p>
    </div>
</body>
</html>`;
}

/**
 * Logs diagnostic information about the main process
 * @param {string} projectName - Project name
 * @param {string} prompt - User prompt
 * @param {Record<string, boolean>} envVars - Environment variables status
 */
export function logMainProcessDiagnostics(projectName: string, prompt: string, envVars: Record<string, boolean>): void {
    console.log("=== MAIN PROCESS DIAGNOSTICS ===");
    console.log(`Starting website generation for project: ${projectName}`);
    console.log(`User prompt: "${prompt}"`);
    console.log("Environment variables check:", envVars);
}

/**
 * Validates website code and logs warnings
 * @param {string} websiteCode - Generated website code
 */
export function validateWebsiteCode(websiteCode: string): void {
    console.log(`Generated website code length: ${websiteCode.length} characters`);
    if (websiteCode.length < 500) {
        console.log("WARNING: Generated website code is suspiciously short!");
    }
}