/**
 * Audio transcription functionality using OpenAI Whisper
 */
import fs from "fs";
import OpenAI from "openai";

/**
 * Transcribes an audio file from a URL using OpenAI Whisper.
 * @param {string} mediaUrl - The URL of the audio file.
 * @param {OpenAI} openai - The initialized OpenAI client.
 * @returns {Promise<string>} - The transcribed text.
 */
export async function transcribeAudio(mediaUrl: string, openai: OpenAI): Promise<string> {
    console.log("Starting audio transcription process...");
    
    // Use a supported format extension for Whisper API (.ogg is in the list of supported formats)
    const tempPath = `/tmp/${Date.now()}.ogg`;
    
    try {
        console.log(`Downloading audio from ${mediaUrl} to ${tempPath}`);
        
        // Download the media file directly using fetch with authentication
        const response = await fetch(mediaUrl, {
            headers: {
                Authorization: `Basic ${Buffer.from(
                    `${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`
                ).toString('base64')}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`Failed to download media: ${response.status} ${response.statusText}`);
        }
        
        // Get the content type to verify we're getting audio
        const contentType = response.headers.get('content-type');
        console.log(`Media content type: ${contentType}`);
        
        // Save the media content to a file
        const buffer = await response.arrayBuffer();
        fs.writeFileSync(tempPath, Buffer.from(buffer));
        
        // Log file stats to verify download
        const stats = fs.statSync(tempPath);
        console.log(`Downloaded file size: ${stats.size} bytes`);
        
        if (stats.size < 1000) {
            throw new Error("Downloaded file is too small to be valid audio");
        }
        
        console.log("Audio download complete.");
        console.log("Sending audio to OpenAI Whisper API for transcription...");
        
        const transcription = await openai.audio.transcriptions.create({
            file: fs.createReadStream(tempPath),
            model: "whisper-1",
        });
        
        console.log("Transcription received from OpenAI.");
        fs.unlinkSync(tempPath); // Clean up the temporary file
        console.log("Transcription complete, temporary file deleted.");
        return transcription.text;
    } catch (error) {
        console.error("Transcription error details:", error);
        
        // Clean up temp file if it exists
        try {
            if (fs.existsSync(tempPath)) {
                fs.unlinkSync(tempPath);
                console.log("Cleaned up temporary file after error.");
            }
        } catch (cleanupError) {
            console.error("Error cleaning up temporary file:", cleanupError);
        }
        
        throw error;
    }
}