/**
 * AI-powered commit message generator using Gemini 2.0 Flash
 */
import { GoogleGenAI } from "@google/genai";

interface CommitGenerationParams {
  userMessage: string;
  oldContent?: string | null;
  newContent: string;
  isEdit: boolean;
  projectName: string;
}

interface ContentChanges {
  type: 'update' | 'expansion' | 'reduction' | 'modification';
  summary: string;
  details: string[];
}

/**
 * Generates intelligent commit messages using Gemini 2.0 Flash
 * @param {CommitGenerationParams} params - Parameters for commit message generation
 * @param {GoogleGenAI} genAI - Google AI client
 * @returns {Promise<string>} - Generated commit message
 */
export async function generateAICommitMessage(params: CommitGenerationParams, genAI: GoogleGenAI): Promise<string> {
    const { userMessage, oldContent, newContent, isEdit, projectName } = params;
    
    console.log(`=== GENERATING AI COMMIT MESSAGE ===`);
    console.log(`Project: ${projectName}`);
    console.log(`Is Edit: ${isEdit}`);
    console.log(`User Message: ${userMessage}`);
    console.log(`Old Content Length: ${oldContent ? oldContent.length : 0} characters`);
    console.log(`New Content Length: ${newContent.length} characters`);
    
    try {
        // Use Gemini 2.0 Flash for commit message generation
        const generationConfig = {
            temperature: 0.3, // Lower temperature for more consistent, professional commit messages
            maxOutputTokens: 100, // Keep commit messages concise
        };
        
        let prompt: string;
        
        if (isEdit && oldContent) {
            // For edits, analyze the differences
            const changes = analyzeContentChanges(oldContent, newContent);
            
            prompt = `You are a professional developer creating a Git commit message. Analyze the changes made to a website and create a concise, descriptive commit message.

PROJECT: ${projectName}
USER REQUEST: "${userMessage}"

CHANGES DETECTED:
${changes.summary}

DETAILED CHANGES:
- Content length changed from ${oldContent.length} to ${newContent.length} characters
- Change type: ${changes.type}
- Key modifications: ${changes.details.join(', ')}

Create a commit message following conventional commit format:
- Use prefixes like: feat:, fix:, style:, refactor:, docs:, chore:
- Keep it under 72 characters
- Be specific about what changed
- Use present tense, imperative mood

Examples:
- "feat: add contact form with validation"
- "style: update color scheme to blue theme"
- "fix: correct navigation menu alignment"
- "refactor: improve responsive layout"

Generate ONE commit message only:`;
        } else {
            // For new creations
            prompt = `You are a professional developer creating a Git commit message for a new website creation.

PROJECT: ${projectName}
USER REQUEST: "${userMessage}"

WEBSITE DETAILS:
- Content length: ${newContent.length} characters
- Type: New website creation
- Purpose: ${extractWebsitePurpose(userMessage, newContent)}

Create a commit message following conventional commit format:
- Use "feat:" prefix for new features/websites
- Keep it under 72 characters
- Be specific about what was created
- Use present tense, imperative mood

Examples:
- "feat: create portfolio website with dark theme"
- "feat: build restaurant landing page"
- "feat: add business website with contact form"

Generate ONE commit message only:`;
        }
        
        console.log(`Sending prompt to Gemini 2.0 Flash...`);
        
        if (!genAI) {
            throw new Error('No AI client provided');
        }
        
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: prompt,
            config: generationConfig
        });
        
        // Extract text from the actual response structure
        let commitMessage = '';
        if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
            commitMessage = result.candidates[0].content.parts[0].text.trim();
        } else if (result.text) {
            commitMessage = result.text.trim();
        } else {
            throw new Error('No text content found in AI response');
        }
        
        // Clean up the response - remove quotes, extra text, etc.
        commitMessage = cleanCommitMessage(commitMessage);
        
        // Validate and fallback if needed
        if (!commitMessage || commitMessage.length > 72 || !isValidCommitMessage(commitMessage)) {
            console.log(`Generated commit message invalid, using fallback`);
            commitMessage = generateFallbackCommitMessage(userMessage, isEdit);
        }
        
        console.log(`Generated commit message: "${commitMessage}"`);
        return commitMessage;
        
    } catch (error) {
        console.error('Error generating AI commit message:', error);
        console.log('Falling back to basic commit message generation');
        
        // Fallback to basic generation
        return generateFallbackCommitMessage(userMessage, isEdit);
    }
}

/**
 * Analyzes changes between old and new content
 * @param {string} oldContent - Previous content
 * @param {string} newContent - New content
 * @returns {ContentChanges} - Analysis of changes
 */
function analyzeContentChanges(oldContent: string, newContent: string): ContentChanges {
    const changes: ContentChanges = {
        type: 'update',
        summary: '',
        details: []
    };
    
    // Basic content analysis
    const oldLength = oldContent.length;
    const newLength = newContent.length;
    const lengthDiff = newLength - oldLength;
    
    if (lengthDiff > 500) {
        changes.type = 'expansion';
        changes.summary = 'Significant content addition';
        changes.details.push('major content expansion');
    } else if (lengthDiff < -500) {
        changes.type = 'reduction';
        changes.summary = 'Content reduction/cleanup';
        changes.details.push('content reduction');
    } else {
        changes.type = 'modification';
        changes.summary = 'Content modifications';
        changes.details.push('content updates');
    }
    
    // Analyze specific changes
    const oldLower = oldContent.toLowerCase();
    const newLower = newContent.toLowerCase();
    
    // Check for style changes
    if (hasStyleChanges(oldContent, newContent)) {
        changes.details.push('styling updates');
    }
    
    // Check for structural changes
    if (hasStructuralChanges(oldContent, newContent)) {
        changes.details.push('layout changes');
    }
    
    // Check for content additions
    if (hasNewSections(oldContent, newContent)) {
        changes.details.push('new sections');
    }
    
    return changes;
}

/**
 * Checks if there are style-related changes
 * @param {string} oldContent - Previous content
 * @param {string} newContent - New content
 * @returns {boolean} - Whether style changes detected
 */
function hasStyleChanges(oldContent: string, newContent: string): boolean {
    const styleKeywords = ['color:', 'background:', 'font-', 'margin:', 'padding:', 'border:', 'width:', 'height:'];
    
    for (const keyword of styleKeywords) {
        const oldCount = (oldContent.match(new RegExp(keyword, 'gi')) || []).length;
        const newCount = (newContent.match(new RegExp(keyword, 'gi')) || []).length;
        
        if (Math.abs(oldCount - newCount) > 2) {
            return true;
        }
    }
    
    return false;
}

/**
 * Checks if there are structural changes
 * @param {string} oldContent - Previous content
 * @param {string} newContent - New content
 * @returns {boolean} - Whether structural changes detected
 */
function hasStructuralChanges(oldContent: string, newContent: string): boolean {
    const structuralTags = ['<div', '<section', '<header', '<footer', '<nav', '<main', '<article'];
    
    for (const tag of structuralTags) {
        const oldCount = (oldContent.match(new RegExp(tag, 'gi')) || []).length;
        const newCount = (newContent.match(new RegExp(tag, 'gi')) || []).length;
        
        if (oldCount !== newCount) {
            return true;
        }
    }
    
    return false;
}

/**
 * Checks if new sections were added
 * @param {string} oldContent - Previous content
 * @param {string} newContent - New content
 * @returns {boolean} - Whether new sections detected
 */
function hasNewSections(oldContent: string, newContent: string): boolean {
    const sectionKeywords = ['<section', '<div class=', '<div id=', '<header', '<footer'];
    
    for (const keyword of sectionKeywords) {
        const oldCount = (oldContent.match(new RegExp(keyword, 'gi')) || []).length;
        const newCount = (newContent.match(new RegExp(keyword, 'gi')) || []).length;
        
        if (newCount > oldCount) {
            return true;
        }
    }
    
    return false;
}

/**
 * Extracts the purpose of the website from user message and content
 * @param {string} userMessage - User's original message
 * @param {string} content - Website content
 * @returns {string} - Website purpose
 */
function extractWebsitePurpose(userMessage: string, content: string): string {
    // Extract key terms from user message
    const message = userMessage.toLowerCase();
    
    if (message.includes('portfolio')) return 'portfolio website';
    if (message.includes('business') || message.includes('company')) return 'business website';
    if (message.includes('restaurant') || message.includes('food')) return 'restaurant website';
    if (message.includes('blog')) return 'blog website';
    if (message.includes('shop') || message.includes('store') || message.includes('ecommerce')) return 'e-commerce website';
    if (message.includes('landing')) return 'landing page';
    if (message.includes('personal')) return 'personal website';
    
    // Analyze content for clues
    const contentLower = content.toLowerCase();
    if (contentLower.includes('about me') || contentLower.includes('my work')) return 'portfolio website';
    if (contentLower.includes('contact us') || contentLower.includes('our services')) return 'business website';
    if (contentLower.includes('menu') || contentLower.includes('reservation')) return 'restaurant website';
    
    return 'website';
}

/**
 * Cleans up the AI-generated commit message
 * @param {string} message - Raw commit message from AI
 * @returns {string} - Cleaned commit message
 */
export function cleanCommitMessage(message: string): string {
    if (!message || message === null || message === undefined) return '';
    
    // Remove quotes
    message = message.replace(/^["']|["']$/g, '');
    
    // Remove extra whitespace
    message = message.trim();
    
    // Take only the first line if multiple lines
    message = message.split('\n')[0];
    
    // Ensure it doesn't end with a period
    message = message.replace(/\.$/, '');
    
    // Truncate if too long
    if (message.length > 72) {
        message = message.substring(0, 69) + '...';
    }
    
    return message;
}

/**
 * Validates if a commit message follows good practices
 * @param {string} message - Commit message to validate
 * @returns {boolean} - Whether the message is valid
 */
export function isValidCommitMessage(message: string): boolean {
    // Check basic requirements
    if (!message || message.length < 10 || message.length > 72) {
        return false;
    }
    
    // Check for conventional commit format
    const conventionalPrefixes = ['feat:', 'fix:', 'docs:', 'style:', 'refactor:', 'test:', 'chore:', 'perf:', 'ci:', 'build:'];
    const hasValidPrefix = conventionalPrefixes.some(prefix => message.toLowerCase().startsWith(prefix));
    
    if (!hasValidPrefix) {
        return false;
    }
    
    // Check that it's not just the prefix
    const colonIndex = message.indexOf(':');
    if (colonIndex === -1 || message.substring(colonIndex + 1).trim().length < 5) {
        return false;
    }
    
    return true;
}

/**
 * Generates a fallback commit message when AI generation fails
 * @param {string} userMessage - Original user message
 * @param {boolean} isEdit - Whether this is an edit
 * @returns {string} - Fallback commit message
 */
export function generateFallbackCommitMessage(userMessage: string, isEdit: boolean): string {
    if (isEdit) {
        // For edits, create more descriptive commit messages
        const editKeywords: Record<string, string> = {
            'color': 'style',
            'background': 'style',
            'font': 'style',
            'size': 'style',
            'add': 'feat',
            'remove': 'refactor',
            'delete': 'refactor',
            'change': 'refactor',
            'modify': 'refactor',
            'fix': 'fix',
            'header': 'style',
            'footer': 'style',
            'navigation': 'feat',
            'menu': 'feat'
        };
        
        let category = 'refactor';
        for (const [keyword, cat] of Object.entries(editKeywords)) {
            if (userMessage.toLowerCase().includes(keyword)) {
                category = cat;
                break;
            }
        }
        
        // Truncate message if too long, but keep it descriptive
        const maxLength = 60;
        let message = userMessage.length > maxLength
            ? userMessage.substring(0, maxLength) + '...'
            : userMessage;
        
        return `${category}: ${message}`;
    } else {
        // For initial creation
        const truncatedMessage = userMessage.length > 40 
            ? userMessage.substring(0, 40) + '...' 
            : userMessage;
        return `feat: create website - ${truncatedMessage}`;
    }
}

/**
 * Enhanced commit message generator that replaces the basic one
 * @param {string} userMessage - Original user message
 * @param {boolean} isEdit - Whether this is an edit or new creation
 * @param {string | null} oldContent - Previous content (for edits)
 * @param {string} newContent - New content
 * @param {string} projectName - Project name
 * @param {GoogleGenAI} genAI - Google AI client
 * @returns {Promise<string>} - Generated commit message
 */
export async function generateEnhancedCommitMessage(
    userMessage: string, 
    isEdit: boolean, 
    oldContent: string | null, 
    newContent: string, 
    projectName: string, 
    genAI: GoogleGenAI
): Promise<string> {
    return await generateAICommitMessage({
        userMessage,
        oldContent,
        newContent,
        isEdit,
        projectName
    }, genAI);
}