/**
 * Configuration and API client initialization
 */
import { GoogleGenAI } from "@google/genai";
import OpenA<PERSON> from "openai";
import twilio from "twilio";

// Global configuration for embedding models and vector dimensions
// This ensures consistency across all vector operations
export const EMBEDDING_CONFIG = {
  // Use the same model across all components
  MODEL: 'models/text-embedding-004',
  
  // Vector dimensions for text-embedding-004 model
  DIMENSIONS: 768,
  
  // Batch sizes for processing
  BATCH_SIZE: 25,
  EMBEDDING_BATCH_SIZE: 50,
  
  // Similarity thresholds
  DEFAULT_SIMILARITY_THRESHOLD: 0.7,
  FALLBACK_SIMILARITY_THRESHOLD: 0.6,
  
  // Search result limits
  MAX_RESULTS: 10,
  DEFAULT_MAX_RESULTS: 10
} as const;

// Validate that dimensions match expected model output
export function validateEmbeddingDimensions(embedding: number[], context: string = 'unknown'): void {
  if (embedding.length !== EMBEDDING_CONFIG.DIMENSIONS) {
    console.error(`🔍 DEBUG: Embedding dimension mismatch in ${context}`);
    console.error(`Expected: ${EMBEDDING_CONFIG.DIMENSIONS}, Got: ${embedding.length}`);
    throw new Error(`Embedding dimension mismatch: expected ${EMBEDDING_CONFIG.DIMENSIONS}, got ${embedding.length} in ${context}`);
  }
}

// Ensure embedding has correct dimensions, pad or truncate if necessary
export function normalizeEmbeddingDimensions(embedding: number[], context: string = 'unknown'): number[] {
  if (embedding.length === EMBEDDING_CONFIG.DIMENSIONS) {
    return embedding;
  }
  
  console.warn(`🔧 DEBUG: Normalizing embedding dimensions in ${context}: ${embedding.length} -> ${EMBEDDING_CONFIG.DIMENSIONS}`);
  
  if (embedding.length > EMBEDDING_CONFIG.DIMENSIONS) {
    // Truncate if too long
    return embedding.slice(0, EMBEDDING_CONFIG.DIMENSIONS);
  } else {
    // Pad with zeros if too short
    const padded = [...embedding];
    while (padded.length < EMBEDDING_CONFIG.DIMENSIONS) {
      padded.push(0);
    }
    return padded;
  }
}

/**
 * Initialize all API clients with environment variables
 * @returns {Object} Object containing all initialized clients
 */
export function initializeClients() {
    const twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    const genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    const vercelToken = process.env.VERCEL_API_TOKEN;

    return {
        twilioClient,
        openai,
        genAI,
        vercelToken
    };
}

/**
 * Validate that all required environment variables are present
 * @returns {Object} Object with validation results
 */
export function validateEnvironment() {
    const required = [
        'TWILIO_ACCOUNT_SID',
        'TWILIO_AUTH_TOKEN',
        'TWILIO_PHONE_NUMBER',
        'OPENAI_API_KEY',
        'GOOGLE_API_KEY',
        'VERCEL_API_TOKEN',
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY'
    ];

    const missing = required.filter(key => !process.env[key]);
    const envVars = {
        "GOOGLE_API_KEY": !!process.env.GOOGLE_API_KEY,
        "VERCEL_API_TOKEN": !!process.env.VERCEL_API_TOKEN,
        "VERCEL_TEAM_ID": !!process.env.VERCEL_TEAM_ID,
        "SUPABASE_URL": !!process.env.SUPABASE_URL,
        "SUPABASE_ANON_KEY": !!process.env.SUPABASE_ANON_KEY
    };

    return {
        isValid: missing.length === 0,
        missing,
        envVars
    };
}