import { GoogleGenAI } from '@google/genai';
import { createClient } from '@supabase/supabase-js';
import { UserProject } from '../types/index.js';

interface ScaffoldRequest {
  projectType: 'nextjs' | 'astro' | 'remix' | 'vite-react' | 'vite-vue' | 'express' | 'custom';
  description: string;
  features?: string[];
  styling?: 'tailwind' | 'css' | 'styled-components' | 'emotion';
  typescript?: boolean;
}

interface ScaffoldResult {
  success: boolean;
  files: ScaffoldFile[];
  projectStructure: ProjectStructure;
  setupInstructions: string[];
  error?: string;
}

interface ScaffoldFile {
  path: string;
  content: string;
  type: 'code' | 'config' | 'documentation' | 'asset';
  description: string;
}

interface ProjectStructure {
  name: string;
  type: string;
  framework: string;
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
  features: string[];
}

export class ScaffoldManager {
  private genAI: GoogleGenAI;
  private supabase: any;

  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY! });
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    );
  }

  /**
   * Generate a complete multi-file project scaffold
   */
  async generateScaffold(request: ScaffoldRequest): Promise<ScaffoldResult> {
    try {
      console.log(`🏗️  Scaffolding ${request.projectType} project: ${request.description}`);

      // Get project template and structure
      const template = this.getProjectTemplate(request.projectType);
      
      // Generate project structure using AI
      const projectStructure = await this.generateProjectStructure(request, template);
      
      // Generate all project files
      const files = await this.generateProjectFiles(request, projectStructure, template);
      
      // Generate setup instructions
      const setupInstructions = this.generateSetupInstructions(projectStructure);

      console.log(`✅ Generated scaffold with ${files.length} files`);

      return {
        success: true,
        files,
        projectStructure,
        setupInstructions
      };

    } catch (error) {
      console.error('Scaffold generation failed:', error);
      return {
        success: false,
        files: [],
        projectStructure: {} as ProjectStructure,
        setupInstructions: [],
        error: error.message
      };
    }
  }

  /**
   * Get base template for project type
   */
  private getProjectTemplate(projectType: string): any {
    const templates = {
      nextjs: {
        framework: 'Next.js',
        baseFiles: [
          'package.json',
          'next.config.js',
          'tailwind.config.js',
          'tsconfig.json',
          'app/layout.tsx',
          'app/page.tsx',
          'app/globals.css',
          'components/ui/button.tsx',
          'lib/utils.ts',
          'README.md'
        ],
        dependencies: [
          'next',
          'react',
          'react-dom',
          'tailwindcss',
          'autoprefixer',
          'postcss'
        ],
        devDependencies: [
          '@types/node',
          '@types/react',
          '@types/react-dom',
          'typescript',
          'eslint',
          'eslint-config-next'
        ],
        scripts: {
          'dev': 'next dev',
          'build': 'next build',
          'start': 'next start',
          'lint': 'next lint'
        }
      },
      astro: {
        framework: 'Astro',
        baseFiles: [
          'package.json',
          'astro.config.mjs',
          'tsconfig.json',
          'src/layouts/Layout.astro',
          'src/pages/index.astro',
          'src/components/Card.astro',
          'src/styles/global.css',
          'public/favicon.svg',
          'README.md'
        ],
        dependencies: [
          'astro',
          '@astrojs/tailwind',
          'tailwindcss'
        ],
        devDependencies: [
          '@types/node',
          'typescript'
        ],
        scripts: {
          'dev': 'astro dev',
          'start': 'astro dev',
          'build': 'astro build',
          'preview': 'astro preview'
        }
      },
      remix: {
        framework: 'Remix',
        baseFiles: [
          'package.json',
          'remix.config.js',
          'tsconfig.json',
          'app/root.tsx',
          'app/routes/_index.tsx',
          'app/styles/global.css',
          'app/components/ui/button.tsx',
          'README.md'
        ],
        dependencies: [
          '@remix-run/node',
          '@remix-run/react',
          '@remix-run/serve',
          'react',
          'react-dom',
          'tailwindcss'
        ],
        devDependencies: [
          '@remix-run/dev',
          '@types/react',
          '@types/react-dom',
          'typescript'
        ],
        scripts: {
          'build': 'remix build',
          'dev': 'remix dev',
          'start': 'remix-serve build'
        }
      },
      'vite-react': {
        framework: 'Vite + React',
        baseFiles: [
          'package.json',
          'vite.config.ts',
          'tsconfig.json',
          'index.html',
          'src/main.tsx',
          'src/App.tsx',
          'src/index.css',
          'src/components/ui/Button.tsx',
          'README.md'
        ],
        dependencies: [
          'react',
          'react-dom',
          'tailwindcss',
          'autoprefixer',
          'postcss'
        ],
        devDependencies: [
          '@types/react',
          '@types/react-dom',
          '@vitejs/plugin-react',
          'typescript',
          'vite'
        ],
        scripts: {
          'dev': 'vite',
          'build': 'tsc && vite build',
          'preview': 'vite preview'
        }
      }
    };

    return templates[projectType] || templates.nextjs;
  }

  /**
   * Generate project structure using AI
   */
  private async generateProjectStructure(
    request: ScaffoldRequest,
    template: any
  ): Promise<ProjectStructure> {
    const prompt = `Generate a project structure for a ${request.projectType} application.

Description: ${request.description}
Features: ${request.features?.join(', ') || 'standard features'}
Styling: ${request.styling || 'tailwind'}
TypeScript: ${request.typescript !== false}

Base template: ${JSON.stringify(template, null, 2)}

Generate a complete project structure with:
1. A descriptive project name based on the description (kebab-case, no spaces)
2. Updated dependencies based on requested features
3. Additional files needed for the specific requirements
4. Proper scripts configuration
5. Feature-specific additions

IMPORTANT: Generate a meaningful project name from the description. Examples:
- "Create a blog website" → "modern-blog-site"
- "E-commerce store for shoes" → "shoe-store-app"
- "Portfolio website" → "developer-portfolio"

Return JSON in this format:
{
  "name": "descriptive-project-name",
  "type": "${request.projectType}",
  "framework": "${template.framework}",
  "dependencies": ["dep1", "dep2"],
  "devDependencies": ["dev1", "dev2"],
  "scripts": {"script": "command"},
  "features": ["feature1", "feature2"]
}`;

    try {
      const result = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: prompt,
        config: {
          temperature: 0.3,
          maxOutputTokens: 1000
        }
      });

      let response = '';
      if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
        response = result.candidates[0].content.parts[0].text;
      } else if (result.text) {
        response = result.text;
      } else {
        throw new Error('No text content found in AI response');
      }

      const cleanResponse = this.cleanJsonResponse(response);
      console.log('Cleaned JSON response:', cleanResponse);
      
      const structure = JSON.parse(cleanResponse);

      // Merge with template defaults
      return {
        ...template,
        ...structure,
        dependencies: [...new Set([...template.dependencies, ...(structure.dependencies || [])])],
        devDependencies: [...new Set([...template.devDependencies, ...(structure.devDependencies || [])])],
        scripts: { ...template.scripts, ...(structure.scripts || {}) }
      };

    } catch (error) {
      console.warn('Failed to generate AI structure, using template:', error);
      
      // Generate a fallback name from the description
      const fallbackName = this.generateFallbackProjectName(request.description);
      
      return {
        name: fallbackName,
        type: request.projectType,
        framework: template.framework,
        dependencies: template.dependencies,
        devDependencies: template.devDependencies,
        scripts: template.scripts,
        features: request.features || []
      };
    }
  }

  /**
   * Generate all project files
   */
  private async generateProjectFiles(
    request: ScaffoldRequest,
    structure: ProjectStructure,
    template: any
  ): Promise<ScaffoldFile[]> {
    console.log(`📁 Generating ${template.baseFiles.length} project files...`);

    const files: ScaffoldFile[] = [];

    // Generate each file
    for (const filePath of template.baseFiles) {
      try {
        console.log(`  Generating ${filePath}...`);
        
        const fileContent = await this.generateFileContent(
          filePath,
          request,
          structure,
          template
        );

        files.push({
          path: filePath,
          content: fileContent,
          type: this.getFileType(filePath),
          description: this.getFileDescription(filePath)
        });

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        console.warn(`Failed to generate ${filePath}:`, error);
        
        // Add fallback content
        files.push({
          path: filePath,
          content: this.getFallbackContent(filePath, structure),
          type: this.getFileType(filePath),
          description: `Fallback content for ${filePath}`
        });
      }
    }

    return files;
  }

  /**
   * Generate content for a specific file
   */
  private async generateFileContent(
    filePath: string,
    request: ScaffoldRequest,
    structure: ProjectStructure,
    template: any
  ): Promise<string> {
    
    // Special handling for package.json
    if (filePath === 'package.json') {
      return JSON.stringify({
        name: structure.name,
        version: '0.1.0',
        private: true,
        scripts: structure.scripts,
        dependencies: structure.dependencies.reduce((acc, dep) => {
          acc[dep] = 'latest';
          return acc;
        }, {} as Record<string, string>),
        devDependencies: structure.devDependencies.reduce((acc, dep) => {
          acc[dep] = 'latest';
          return acc;
        }, {} as Record<string, string>)
      }, null, 2);
    }

    // Use AI to generate file content
    const prompt = this.buildFilePrompt(filePath, request, structure, template);

    const result = await this.genAI.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: prompt,
      config: {
        temperature: 0.4,
        maxOutputTokens: 2000
      }
    });

    let response = '';
    if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
      response = result.candidates[0].content.parts[0].text;
    } else if (result.text) {
      response = result.text;
    } else {
      throw new Error('No text content found in AI response');
    }

    return this.cleanCodeResponse(response);
  }

  /**
   * Build prompt for file generation
   */
  private buildFilePrompt(
    filePath: string,
    request: ScaffoldRequest,
    structure: ProjectStructure,
    template: any
  ): string {
    return `Generate content for ${filePath} in a ${structure.framework} project.

Project Description: ${request.description}
Project Type: ${request.projectType}
Features: ${request.features?.join(', ') || 'standard'}
Styling: ${request.styling || 'tailwind'}
TypeScript: ${request.typescript !== false}

Requirements:
- Follow ${structure.framework} best practices
- Include proper TypeScript types if applicable
- Use ${request.styling || 'tailwind'} for styling
- Make it production-ready and well-structured
- Include helpful comments

Generate ONLY the file content, no explanations or markdown formatting.`;
  }

  /**
   * Clean JSON response from AI
   */
  private cleanJsonResponse(response: string): string {
    let cleaned = response.trim();
    
    // Remove code block markers
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.replace(/```json\n?/, '').replace(/\n?```$/, '');
    } else if (cleaned.startsWith('```')) {
      cleaned = cleaned.replace(/```\n?/, '').replace(/\n?```$/, '');
    }
    
    // Find JSON object boundaries
    const jsonStart = cleaned.indexOf('{');
    let jsonEnd = -1;
    
    if (jsonStart !== -1) {
      // Find the matching closing brace
      let braceCount = 0;
      for (let i = jsonStart; i < cleaned.length; i++) {
        if (cleaned[i] === '{') {
          braceCount++;
        } else if (cleaned[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            jsonEnd = i;
            break;
          }
        }
      }
    }
    
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }
    
    return cleaned.trim();
  }

  /**
   * Clean code response from AI
   */
  private cleanCodeResponse(response: string): string {
    let cleaned = response.trim();
    
    // Remove code block markers
    if (cleaned.startsWith('```')) {
      const firstNewline = cleaned.indexOf('\n');
      const lastBackticks = cleaned.lastIndexOf('```');
      if (firstNewline > 0 && lastBackticks > firstNewline) {
        cleaned = cleaned.substring(firstNewline + 1, lastBackticks);
      }
    }
    
    return cleaned.trim();
  }

  /**
   * Get file type for categorization
   */
  private getFileType(filePath: string): 'code' | 'config' | 'documentation' | 'asset' {
    if (filePath.includes('README') || filePath.endsWith('.md')) {
      return 'documentation';
    }
    if (filePath.includes('config') || filePath.endsWith('.json') || filePath.endsWith('.js') && filePath.includes('config')) {
      return 'config';
    }
    if (filePath.endsWith('.svg') || filePath.endsWith('.png') || filePath.endsWith('.ico')) {
      return 'asset';
    }
    return 'code';
  }

  /**
   * Get file description
   */
  private getFileDescription(filePath: string): string {
    const descriptions: Record<string, string> = {
      'package.json': 'Project dependencies and scripts',
      'README.md': 'Project documentation and setup instructions',
      'tsconfig.json': 'TypeScript configuration',
      'tailwind.config.js': 'Tailwind CSS configuration',
      'next.config.js': 'Next.js configuration',
      'vite.config.ts': 'Vite build configuration',
      'app/layout.tsx': 'Root layout component',
      'app/page.tsx': 'Home page component',
      'src/App.tsx': 'Main application component',
      'src/main.tsx': 'Application entry point'
    };

    return descriptions[filePath] || `${filePath} file`;
  }

  /**
   * Get fallback content for failed generations
   */
  private getFallbackContent(filePath: string, structure: ProjectStructure): string {
    if (filePath === 'README.md') {
      return `# ${structure.name}\n\nA ${structure.framework} application.\n\n## Getting Started\n\n\`\`\`bash\nnpm install\nnpm run dev\n\`\`\``;
    }
    
    if (filePath.endsWith('.json')) {
      return '{}';
    }
    
    return `// ${filePath}\n// Generated by WhatsApp Bot\n\nexport default function Component() {\n  return <div>Hello World</div>;\n}`;
  }

  /**
   * Generate setup instructions
   */
  private generateSetupInstructions(structure: ProjectStructure): string[] {
    const instructions = [
      '1. Navigate to your project directory',
      '2. Install dependencies: npm install',
      `3. Start development server: ${structure.scripts.dev || 'npm run dev'}`,
      '4. Open your browser and navigate to the development URL'
    ];

    if (structure.features.includes('database')) {
      instructions.splice(2, 0, '2.5. Set up your database connection in .env.local');
    }

    if (structure.features.includes('auth')) {
      instructions.splice(2, 0, '2.5. Configure authentication providers in your environment');
    }

    return instructions;
  }

  /**
   * Check if scaffold mode is supported for a project type
   */
  isProjectTypeSupported(projectType: string): boolean {
    const supportedTypes = ['nextjs', 'astro', 'remix', 'vite-react', 'vite-vue', 'express'];
    return supportedTypes.includes(projectType);
  }

  /**
   * Get available project types
   */
  getAvailableProjectTypes(): Array<{type: string, name: string, description: string}> {
    return [
      { type: 'nextjs', name: 'Next.js', description: 'React framework with SSR and routing' },
      { type: 'astro', name: 'Astro', description: 'Modern static site generator' },
      { type: 'remix', name: 'Remix', description: 'Full-stack React framework' },
      { type: 'vite-react', name: 'Vite + React', description: 'Fast React development with Vite' },
      { type: 'vite-vue', name: 'Vite + Vue', description: 'Fast Vue development with Vite' },
      { type: 'express', name: 'Express.js', description: 'Node.js web application framework' }
    ];
  }

  /**
   * Generate a fallback project name from description
   */
  private generateFallbackProjectName(description: string): string {
    // Extract key words and create a project name
    const words = description
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .split(/\s+/)
      .filter(word => word.length > 2) // Filter out short words
      .slice(0, 3); // Take first 3 meaningful words

    if (words.length === 0) {
      return 'scaffold-project';
    }

    const projectName = words.join('-');
    
    // Add timestamp suffix to ensure uniqueness
    const timestamp = Date.now().toString().slice(-4);
    
    return `${projectName}-${timestamp}`;
  }
}

export default ScaffoldManager;