/**
 * Vercel Git integration functionality
 */

interface VercelProject {
    id: string;
    name: string;
    accountId: string;
    link?: {
        type: string;
        repo: string;
        repoId?: string;
    };
}

interface VercelDeployment {
    id: string;
    url: string;
    readyState: string;
    createdAt: string;
}

interface GitIntegrationResult {
    projectId: string;
    projectName: string;
    deploymentUrl: string;
    dashboardUrl: string;
    isExisting: boolean;
}

interface DeploymentResult {
    deploymentId: string;
    url: string;
    state: string;
}

interface ProjectDeploymentInfo {
    deploymentId: string;
    url: string;
    state: string;
    createdAt: string;
}

interface GitIntegrationConfig {
    isValid: boolean;
    missingVars: string[];
    message: string;
}

/**
 * Connects a GitHub repository to Vercel for automatic deployments
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} projectName - Vercel project name
 * @returns {Promise<GitIntegrationResult>} - Vercel project information
 */
export async function connectGitToVercel(repoName: string, projectName: string, projectType?: string): Promise<GitIntegrationResult> {
    console.log(`=== VERCEL GIT INTEGRATION ===`);
    console.log(`Connecting repository: ${repoName}`);
    console.log(`Vercel project name: ${projectName}`);
    console.log(`Project type: ${projectType || 'static'}`);
    
    const vercelToken = process.env.VERCEL_API_TOKEN;
    
    if (!vercelToken) {
        throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
    
    try {
        // First, check if project already exists
        const existingProject = await getVercelProject(projectName, vercelToken);
        
        if (existingProject) {
            console.log(`Vercel project "${projectName}" already exists, linking to it`);
            console.log(`Existing project ID: ${existingProject.id}`);
            
            // Check if it's already connected to the correct repository
            if (existingProject.link && existingProject.link.repo === repoName) {
                console.log(`Project is already connected to repository: ${repoName}`);
                
                // Get latest deployment
                const latestDeployment = await getLatestDeployment(existingProject.id, vercelToken);
                
                return {
                    projectId: existingProject.id,
                    projectName: existingProject.name,
                    deploymentUrl: latestDeployment ? `https://${latestDeployment.url}` : `https://${existingProject.name}.vercel.app`,
                    dashboardUrl: `https://vercel.com/${existingProject.accountId || 'dashboard'}/${existingProject.name}`,
                    isExisting: true
                };
            } else {
                console.log(`Project exists but not connected to ${repoName}, updating connection...`);
                // Update the project to connect to the repository
                await updateVercelProjectRepo(existingProject.id, repoName, vercelToken);
                
                // Trigger deployment from the connected repo
                const deploymentData = await triggerGitDeployment(existingProject.id);
                console.log(`Deployment triggered for existing project: ${deploymentData.url}`);
                
                return {
                    projectId: existingProject.id,
                    projectName: existingProject.name,
                    deploymentUrl: `https://${deploymentData.url}`,
                    dashboardUrl: `https://vercel.com/${existingProject.accountId || 'dashboard'}/${existingProject.name}`,
                    isExisting: true
                };
            }
        }
        
        // Create new Vercel project connected to GitHub repo
        const projectData = await createVercelProject(repoName, projectName, vercelToken, projectType);
        console.log(`Vercel project created: ${projectData.name}`);
        
        // Trigger initial deployment
        const deploymentData = await triggerGitDeployment(projectData.id);
        console.log(`Initial deployment triggered: ${deploymentData.url}`);
        
        return {
            projectId: projectData.id,
            projectName: projectData.name,
            deploymentUrl: `https://${deploymentData.url}`,
            dashboardUrl: `https://vercel.com/${projectData.accountId}/${projectData.name}`,
            isExisting: false
        };
        
    } catch (error) {
        console.error('Error connecting Git to Vercel:', error);
        throw error;
    }
}

/**
 * Triggers a new deployment from the connected Git repository
 * @param {string} projectId - Vercel project ID
 * @returns {Promise<DeploymentResult>} - Deployment information
 */
export async function triggerGitDeployment(projectId: string): Promise<DeploymentResult> {
    console.log(`Triggering Git deployment for project: ${projectId}`);
    
    const vercelToken = process.env.VERCEL_API_TOKEN;
    
    if (!vercelToken) {
        throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
    
    try {
        const deploymentData = await createGitDeployment(projectId, vercelToken);
        
        return {
            deploymentId: deploymentData.id,
            url: deploymentData.url,
            state: deploymentData.readyState
        };
        
    } catch (error) {
        console.error('Error triggering Git deployment:', error);
        throw error;
    }
}

/**
 * Gets the deployment status and URL for a Vercel project
 * @param {string} projectId - Vercel project ID
 * @returns {Promise<ProjectDeploymentInfo>} - Current deployment information
 */
export async function getProjectDeployment(projectId: string): Promise<ProjectDeploymentInfo> {
    console.log(`Getting deployment info for project: ${projectId}`);
    
    const vercelToken = process.env.VERCEL_API_TOKEN;
    
    if (!vercelToken) {
        throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
    
    try {
        const deployments = await getProjectDeployments(projectId, vercelToken);
        
        if (deployments.length === 0) {
            throw new Error('No deployments found for project');
        }
        
        // Get the latest deployment
        const latestDeployment = deployments[0];
        
        return {
            deploymentId: latestDeployment.id,
            url: `https://${latestDeployment.url}`,
            state: latestDeployment.readyState,
            createdAt: latestDeployment.createdAt
        };
        
    } catch (error) {
        console.error('Error getting project deployment:', error);
        throw error;
    }
}

/**
 * Creates a new Vercel project connected to a GitHub repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} projectName - Desired project name
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelProject>} - Project data
 */
async function createVercelProject(repoName: string, projectName: string, token: string, projectType?: string): Promise<VercelProject> {
    const [owner, repo] = repoName.split('/');
    
    // Map project types to Vercel frameworks
    const frameworkMapping: Record<string, string | null> = {
        'nextjs': 'nextjs',
        'astro': 'astro',
        'remix': 'remix',
        'vite-react': 'vite',
        'vite-vue': 'vite',
        'express': null // Node.js projects don't have a specific framework in Vercel
    };
    
    const framework = projectType ? frameworkMapping[projectType] || null : null;
    
    console.log(`Creating Vercel project with framework: ${framework || 'static'}`);
    
    const projectData: any = {
        name: projectName,
        gitRepository: {
            type: 'github',
            repo: repoName
        },
        framework: framework,
        buildCommand: null,
        outputDirectory: null,
        installCommand: null,
        devCommand: null,
        environmentVariables: [],
        publicSource: true
    };
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        projectData.teamId = process.env.VERCEL_TEAM_ID;
    }
    
    const response = await fetch('https://api.vercel.com/v9/projects', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(projectData)
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Vercel project creation failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    return await response.json();
}

/**
 * Creates a deployment from the connected Git repository
 * @param {string} projectId - Vercel project ID
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelDeployment>} - Deployment data
 */
async function createGitDeployment(projectId: string, token: string): Promise<VercelDeployment> {
    // Get project info to extract repository details
    const project = await getVercelProjectById(projectId, token);
    
    if (!project || !project.link || !project.link.repo) {
        throw new Error('Project is not connected to a Git repository');
    }
    
    // Get the repository ID from GitHub API if not available
    let repoId = project.link.repoId;
    if (!repoId) {
        console.log('Repository ID not found in project, fetching from GitHub...');
        repoId = await getGitHubRepoId(project.link.repo);
    }
    
    // For Git-connected projects, Vercel automatically deploys on push
    // We just need to trigger a deployment using the project's Git source
    const deploymentData: any = {
        name: project.name,
        projectSettings: {
            framework: null
        },
        gitSource: {
            type: 'github',
            repo: project.link.repo,
            repoId: repoId,
            ref: 'main'
        },
        target: 'production'
    };
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        deploymentData.teamId = process.env.VERCEL_TEAM_ID;
    }
    
    console.log('Creating Git deployment with data:', JSON.stringify(deploymentData, null, 2));
    
    const response = await fetch('https://api.vercel.com/v13/deployments', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(deploymentData)
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        console.error('Vercel deployment error:', errorData);
        throw new Error(`Vercel deployment failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    return await response.json();
}

/**
 * Gets deployments for a Vercel project
 * @param {string} projectId - Vercel project ID
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelDeployment[]>} - Array of deployments
 */
async function getProjectDeployments(projectId: string, token: string): Promise<VercelDeployment[]> {
    let url = `https://api.vercel.com/v6/deployments?projectId=${projectId}&limit=1`;
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        url += `&teamId=${process.env.VERCEL_TEAM_ID}`;
    }
    
    const response = await fetch(url, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to get deployments: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    const data = await response.json();
    return data.deployments || [];
}

/**
 * Gets an existing Vercel project by name
 * @param {string} projectName - Project name to search for
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelProject|null>} - Project data or null if not found
 */
async function getVercelProject(projectName: string, token: string): Promise<VercelProject | null> {
    let url = `https://api.vercel.com/v9/projects/${projectName}`;
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        url += `?teamId=${process.env.VERCEL_TEAM_ID}`;
    }
    
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.status === 404) {
            return null; // Project doesn't exist
        }
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to get Vercel project: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }
        
        return await response.json();
    } catch (error) {
        if (error.message.includes('404')) {
            return null;
        }
        throw error;
    }
}

/**
 * Gets a Vercel project by ID
 * @param {string} projectId - Project ID to search for
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelProject|null>} - Project data or null if not found
 */
async function getVercelProjectById(projectId: string, token: string): Promise<VercelProject | null> {
    let url = `https://api.vercel.com/v9/projects/${projectId}`;
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        url += `?teamId=${process.env.VERCEL_TEAM_ID}`;
    }
    
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.status === 404) {
            return null; // Project doesn't exist
        }
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to get Vercel project by ID: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }
        
        return await response.json();
    } catch (error) {
        if (error.message.includes('404')) {
            return null;
        }
        throw error;
    }
}

/**
 * Gets the latest deployment for a Vercel project
 * @param {string} projectId - Vercel project ID
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelDeployment|null>} - Latest deployment or null
 */
async function getLatestDeployment(projectId: string, token: string): Promise<VercelDeployment | null> {
    let url = `https://api.vercel.com/v6/deployments?projectId=${projectId}&limit=1`;
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        url += `&teamId=${process.env.VERCEL_TEAM_ID}`;
    }
    
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            console.error(`Failed to get latest deployment: ${response.status}`);
            return null;
        }
        
        const data = await response.json();
        return data.deployments && data.deployments.length > 0 ? data.deployments[0] : null;
    } catch (error) {
        console.error('Error getting latest deployment:', error);
        return null;
    }
}

/**
 * Updates a Vercel project to connect to a GitHub repository
 * @param {string} projectId - Vercel project ID
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} token - Vercel API token
 * @returns {Promise<VercelProject>} - Updated project data
 */
async function updateVercelProjectRepo(projectId: string, repoName: string, token: string): Promise<VercelProject> {
    const updateData = {
        link: {
            type: 'github',
            repo: repoName
        }
    };
    
    let url = `https://api.vercel.com/v9/projects/${projectId}`;
    
    // Add team ID if specified
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        url += `?teamId=${process.env.VERCEL_TEAM_ID}`;
    }
    
    const response = await fetch(url, {
        method: 'PATCH',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to update Vercel project: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }
    
    return await response.json();
}

/**
 * Gets the GitHub repository ID from the GitHub API
 * @param {string} repoName - Full repository name (owner/repo)
 * @returns {Promise<string>} - Repository ID
 */
async function getGitHubRepoId(repoName: string): Promise<string> {
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        const response = await fetch(`https://api.github.com/repos/${repoName}`, {
            headers: {
                'Authorization': `token ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'WhatsApp-Bot'
            }
        });
        
        if (!response.ok) {
            throw new Error(`Failed to get repository info: ${response.status}`);
        }
        
        const repoData = await response.json();
        return repoData.id.toString();
        
    } catch (error) {
        console.error('Error getting GitHub repository ID:', error);
        throw error;
    }
}

export async function fallbackDirectDeployment(htmlContent: string, projectName: string): Promise<string> {
    console.log(`=== FALLBACK DIRECT DEPLOYMENT ===`);
    console.log(`Using direct Vercel deployment for: ${projectName}`);
    
    // Import the original deployment function
    const { deployToVercel } = await import('./deployment.js');
    
    const vercelToken = process.env.VERCEL_API_TOKEN;
    
    if (!vercelToken) {
        throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
    
    try {
        const deploymentUrl = await deployToVercel(htmlContent, vercelToken, projectName);
        console.log(`Fallback deployment successful: ${deploymentUrl}`);
        return deploymentUrl;
        
    } catch (error) {
        console.error('Fallback deployment also failed:', error);
        throw error;
    }
}

/**
 * Validates that required environment variables are set for Git integration
 * @returns {GitIntegrationConfig} - Validation result
 */
export function validateGitIntegrationConfig(): GitIntegrationConfig {
    const required = ['VERCEL_API_TOKEN', 'GITHUB_TOKEN'];
    const missing: string[] = [];
    
    for (const envVar of required) {
        if (!process.env[envVar] || !process.env[envVar]!.trim()) {
            missing.push(envVar);
        }
    }
    
    return {
        isValid: missing.length === 0,
        missingVars: missing,
        message: missing.length > 0
            ? `Missing required environment variables: ${missing.join(', ')}`
            : 'All required environment variables are set'
    };
}