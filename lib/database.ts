/**
 * Database operations using Supabase
 */
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { UserProject, ProjectData } from '../types/index.js';

let supabase: SupabaseClient | null = null;

/**
 * Get or create Supabase client instance
 * @returns {SupabaseClient} Initialized Supabase client
 */
function getSupabaseClient(): SupabaseClient {
  if (!supabase) {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase environment variables');
    }
    
    supabase = createClient(supabaseUrl, supabaseKey);
  }
  return supabase;
}

/**
 * Normalize phone number by removing WhatsApp prefix
 * @param {string} phoneNumber - Raw phone number from WhatsApp
 * @returns {string} Normalized phone number
 */
function normalizePhoneNumber(phoneNumber: string): string {
  return phoneNumber.replace(/^whatsapp:/, '');
}

/**
 * Get the most recent project for a phone number
 * @param {string} phoneNumber - User's phone number
 * @returns {Promise<UserProject | null>} Most recent project or null
 */
export async function getLastProject(phoneNumber: string): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Looking up projects for normalized phone: ${normalizedPhone}`);
    
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching last project:', error);
      return null;
    }
    
    console.log(`Last project for ${normalizedPhone}:`, data ? data.project_name : 'none');
    return data;
    
  } catch (error) {
    console.error('Error in getLastProject:', error);
    return null;
  }
}

/**
 * Save a new project to the database
 * @param {string} phoneNumber - User's phone number
 * @param {ProjectData} projectData - Project data to save
 * @returns {Promise<UserProject | null>} Saved project or null on error
 */
export async function saveProject(phoneNumber: string, projectData: ProjectData): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Saving project for normalized phone: ${normalizedPhone}`);
    
    const projectRecord = {
      phone_number: normalizedPhone,
      project_name: projectData.projectName,
      description: projectData.description,
      url: projectData.url,
      html_content: projectData.htmlContent,
      github_repo_url: projectData.githubRepoUrl || null,
      github_repo_name: projectData.githubRepoName || null,
      last_commit_sha: projectData.lastCommitSha || null,
      is_active: true,
      created_at: new Date().toISOString()
    };
    
    // Mark all previous projects as inactive
    await supabase
      .from('user_projects')
      .update({ is_active: false })
      .eq('phone_number', normalizedPhone);
    
    // Insert new project
    const { data, error } = await supabase
      .from('user_projects')
      .insert([projectRecord])
      .select()
      .single();
    
    if (error) {
      console.error('Error saving project:', error);
      return null;
    }
    
    console.log(`Successfully saved project ${projectData.projectName} for ${normalizedPhone}`);
    return data;
    
  } catch (error) {
    console.error('Error in saveProject:', error);
    return null;
  }
}

/**
 * Update an existing project in the database
 * @param {string} phoneNumber - User's phone number
 * @param {ProjectData} projectData - Updated project data
 * @returns {Promise<UserProject | null>} Updated project or null on error
 */
export async function updateProject(phoneNumber: string, projectData: ProjectData): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Updating project for normalized phone: ${normalizedPhone}`);
    
    const updateData = {
      description: projectData.description,
      url: projectData.url,
      html_content: projectData.htmlContent,
      github_repo_url: projectData.githubRepoUrl || null,
      github_repo_name: projectData.githubRepoName || null,
      last_commit_sha: projectData.lastCommitSha || null,
      updated_at: new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('user_projects')
      .update(updateData)
      .eq('phone_number', normalizedPhone)
      .eq('project_name', projectData.projectName)
      .eq('is_active', true)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating project:', error);
      return null;
    }
    
    console.log(`Successfully updated project ${projectData.projectName} for ${normalizedPhone}`);
    return data;
    
  } catch (error) {
    console.error('Error in updateProject:', error);
    return null;
  }
}

/**
 * Get all projects for a phone number
 * @param {string} phoneNumber - User's phone number
 * @returns {Promise<UserProject[]>} Array of user projects
 */
export async function getAllProjects(phoneNumber: string): Promise<UserProject[]> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Getting all projects for normalized phone: ${normalizedPhone}`);
    
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching all projects:', error);
      return [];
    }
    
    console.log(`Found ${data?.length || 0} projects for ${normalizedPhone}`);
    return data || [];
    
  } catch (error) {
    console.error('Error in getAllProjects:', error);
    return [];
  }
}

/**
 * Find a project by GitHub repository name
 * @param {string} phoneNumber - User's phone number
 * @param {string} repoName - GitHub repository name (e.g., 'user/repo')
 * @returns {Promise<UserProject | null>} Found project or null
 */
export async function findProjectByRepo(phoneNumber: string, repoName: string): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Looking for project with repo name: ${repoName} for phone: ${normalizedPhone}`);
    
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .eq('github_repo_name', repoName)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error finding project by repo:', error);
      return null;
    }
    
    console.log(`Found project by repo:`, data ? data.project_name : 'none');
    return data;
    
  } catch (error) {
    console.error('Error in findProjectByRepo:', error);
    return null;
  }
}

/**
 * Find a project by GitHub repository URL
 * @param {string} phoneNumber - User's phone number
 * @param {string} repoUrl - GitHub repository URL (e.g., 'https://github.com/user/repo')
 * @returns {Promise<UserProject | null>} Found project or null
 */
export async function findProjectByRepoUrl(phoneNumber: string, repoUrl: string): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Looking for project with repo URL: ${repoUrl} for phone: ${normalizedPhone}`);
    
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .eq('github_repo_url', repoUrl)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error finding project by repo URL:', error);
      return null;
    }
    
    console.log(`Found project by repo URL:`, data ? data.project_name : 'none');
    return data;
    
  } catch (error) {
    console.error('Error in findProjectByRepoUrl:', error);
    return null;
  }
}

/**
 * Import a project from GitHub repository data
 * @param {string} phoneNumber - User's phone number
 * @param {ProjectData} projectData - Project data to import
 * @returns {Promise<UserProject | null>} Imported project or null on error
 */
export async function importProject(phoneNumber: string, projectData: ProjectData): Promise<UserProject | null> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Importing project for normalized phone: ${normalizedPhone}`);
    
    // Check if project with same repo already exists
    if (projectData.githubRepoName) {
      const existingProject = await findProjectByRepo(normalizedPhone, projectData.githubRepoName);
      if (existingProject) {
        console.log(`Project with repo ${projectData.githubRepoName} already exists, updating instead`);
        return await updateProject(normalizedPhone, projectData);
      }
    }
    
    const projectRecord = {
      phone_number: normalizedPhone,
      project_name: projectData.projectName,
      description: projectData.description,
      url: projectData.url,
      html_content: projectData.htmlContent,
      github_repo_url: projectData.githubRepoUrl || null,
      github_repo_name: projectData.githubRepoName || null,
      last_commit_sha: projectData.lastCommitSha || null,
      is_active: true,
      created_at: new Date().toISOString()
    };
    
    // Mark all previous projects as inactive
    await supabase
      .from('user_projects')
      .update({ is_active: false })
      .eq('phone_number', normalizedPhone);
    
    // Insert imported project
    const { data, error } = await supabase
      .from('user_projects')
      .insert([projectRecord])
      .select()
      .single();
    
    if (error) {
      console.error('Error importing project:', error);
      return null;
    }
    
    console.log(`Successfully imported project ${projectData.projectName} for ${normalizedPhone}`);
    return data;
    
  } catch (error) {
    console.error('Error in importProject:', error);
    return null;
  }
}

/**
 * Get user project history with optional limit
 * @param {string} phoneNumber - User's phone number
 * @param {number} limit - Maximum number of projects to return (default: 10)
 * @returns {Promise<UserProject[]>} Array of user projects
 */
export async function getUserProjects(phoneNumber: string, limit: number = 10): Promise<UserProject[]> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`Getting user projects for normalized phone: ${normalizedPhone}, limit: ${limit}`);
    
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      console.error('Error fetching user projects:', error);
      return [];
    }
    
    console.log(`Found ${data?.length || 0} projects for ${normalizedPhone}`);
    return data || [];
    
  } catch (error) {
    console.error('Error in getUserProjects:', error);
    return [];
  }
}

/**
 * Initialize database tables (placeholder for setup operations)
 * @returns {Promise<boolean>} True if initialization successful
 */
export async function initializeTables(): Promise<boolean> {
  try {
    console.log('Database tables initialization check');
    // In a real implementation, this might create tables or run migrations
    // For now, we just return true to indicate the database is ready
    return true;
  } catch (error) {
    console.error('Error in initializeTables:', error);
    return false;
  }
}

/**
 * Log a bot run to track usage and performance
 * @param {string} phoneNumber - User's phone number
 * @param {string} intent - Classified intent
 * @param {string} userMessage - Original user message
 * @param {string} status - Run status (success, error, timeout)
 * @param {string} deploymentUrl - Final deployment URL (if successful)
 * @param {number} processingTimeMs - Processing time in milliseconds
 * @returns {Promise<boolean>} True if logged successfully
 */
export async function logBotRun(
  phoneNumber: string,
  intent: string,
  userMessage: string,
  status: string,
  deploymentUrl?: string,
  processingTimeMs?: number
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    
    console.log(`=== LOGGING BOT RUN ===`);
    console.log(`User: ${normalizedPhone}`);
    console.log(`Intent: ${intent}`);
    console.log(`Status: ${status}`);
    console.log(`User message length: ${userMessage?.length || 0} chars`);
    console.log(`Deployment URL: ${deploymentUrl || 'none'}`);
    console.log(`Processing time: ${processingTimeMs || 0}ms`);
    
    // Get the latest project for this user to link the bot run
    console.log(`🔍 Looking up active project for user: ${normalizedPhone}`);
    const { data: projects, error: projectError } = await supabase
      .from('user_projects')
      .select('id, project_name, github_repo_name')
      .eq('phone_number', normalizedPhone)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1);

    if (projectError) {
      console.error('⚠️  Error getting project for bot run:', projectError);
      console.error('Project error details:', {
        code: projectError.code,
        message: projectError.message,
        details: projectError.details
      });
    }

    const projectId = projects && projects.length > 0 ? projects[0].id : null;
    
    if (projectId && projects && projects.length > 0) {
      console.log(`✅ Found active project: ${projects[0].project_name} (ID: ${projectId})`);
      if (projects[0].github_repo_name) {
        console.log(`📂 GitHub repo: ${projects[0].github_repo_name}`);
      }
    } else {
      console.log(`ℹ️  No active project found for user`);
    }
    
    const runRecord = {
      project_id: projectId,
      wa_user: normalizedPhone,
      is_scaffold: intent === 'scaffold_mode',
      status: status,
      created_at: new Date().toISOString(),
      completed_at: status === 'success' || status === 'error' ? new Date().toISOString() : null
    };
    
    console.log(`💾 Inserting bot run record:`, {
      project_id: runRecord.project_id,
      wa_user: runRecord.wa_user,
      is_scaffold: runRecord.is_scaffold,
      status: runRecord.status,
      has_completed_at: !!runRecord.completed_at
    });
    
    const { data, error } = await supabase
      .from('bot_runs')
      .insert([runRecord])
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error logging bot run:', error);
      console.error('Bot run error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      return false;
    }
    
    console.log(`✅ Successfully logged bot run with ID: ${data.id}`);
    console.log(`📊 Logged data:`, {
      id: data.id,
      project_id: data.project_id,
      wa_user: data.wa_user,
      is_scaffold: data.is_scaffold,
      status: data.status
    });
    return true;
    
  } catch (error) {
    console.error('❌ Error in logBotRun:', error);
    console.error('Error stack:', error.stack);
    return false;
  }
}