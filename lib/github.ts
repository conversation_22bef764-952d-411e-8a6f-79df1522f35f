/**
 * GitHub integration functionality for generated websites
 */
import { generateEnhancedCommitMessage } from './ai-commit-generator.js';
import { GoogleGenAI } from "@google/genai";
import { EMBEDDING_CONFIG, validateEmbeddingDimensions, normalizeEmbeddingDimensions } from './config.js';
import { GitHubRepositoryData, GitHubFileData, GitHubCommitData, GitHubValidationResult, GitHubImportData } from '../types/index.js';

interface CreateRepositoryOptions {
  genAI?: GoogleGenAI;
  userMessage?: string;
  scaffoldFiles?: Array<{
    path: string;
    content: string;
    type: 'code' | 'config' | 'documentation' | 'asset';
    description: string;
  }>;
}

interface UpdateRepositoryOptions {
  userMessage?: string;
  oldContent?: string;
  projectName?: string;
  genAI?: GoogleGenAI;
}

interface GitHubApiError {
  message: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

/**
 * Creates a new GitHub repository for a generated website
 * @param {string} projectName - URL-friendly project name
 * @param {string} description - Project description
 * @param {string} htmlContent - Website HTML content
 * @param {CreateRepositoryOptions} options - Additional options for AI commit generation
 * @returns {Promise<GitHubRepositoryData>} - Repository information
 */
export async function createRepository(
  projectName: string, 
  description: string, 
  htmlContent: string, 
  options: CreateRepositoryOptions = {}
): Promise<GitHubRepositoryData> {
    console.log(`=== GITHUB REPOSITORY CREATION ===`);
    console.log(`Creating repository: ${projectName}`);
    console.log(`Description: ${description}`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    const githubUsername = process.env.GITHUB_USERNAME;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        // Always generate AI-powered repository description if genAI is available
        let repositoryDescription = description;
        if (options.genAI) {
            try {
                console.log(`=== GENERATING AI REPOSITORY DESCRIPTION ===`);
                console.log(`User request: "${options.userMessage || description}"`);
                repositoryDescription = await generateRepositoryDescription(
                    options.userMessage || description,
                    htmlContent,
                    projectName,
                    options.genAI
                );
                console.log(`Generated repository description: "${repositoryDescription}"`);
                console.log(`Description length: ${repositoryDescription.length} characters`);
            } catch (aiError) {
                console.error('Failed to generate AI repository description:', aiError);
                console.log('Falling back to truncated user request');
                // Fallback to truncated user request
                if (description.length > 350) {
                    repositoryDescription = description.substring(0, 347) + '...';
                } else {
                    repositoryDescription = description;
                }
            }
        } else {
            // If no AI available, ensure description fits GitHub limits
            if (description.length > 350) {
                repositoryDescription = description.substring(0, 347) + '...';
            }
        }
        
        // Create repository (will use authenticated user if no username specified)
        const repoData = await createGitHubRepo(projectName, repositoryDescription, githubToken, githubUsername);
        console.log(`Repository created: ${repoData.html_url}`);
        
        // Configure GitHub Actions permissions
        await configureGitHubActionsPermissions(repoData.full_name, githubToken);
        
        // Create initial files
        await createInitialFiles(repoData.full_name, htmlContent, description, githubToken, projectName, options);
        
        return {
            repoUrl: repoData.html_url,
            repoName: repoData.full_name,
            cloneUrl: repoData.clone_url,
            defaultBranch: repoData.default_branch || 'main'
        };
        
    } catch (error) {
        console.error('Error creating GitHub repository:', error);
        throw error;
    }
}

/**
 * Updates an existing GitHub repository with new content
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} htmlContent - Updated HTML content
 * @param {string} commitMessage - Commit message (or user message for AI generation)
 * @param {UpdateRepositoryOptions} options - Additional options for AI commit generation
 * @returns {Promise<GitHubCommitData>} - Commit information
 */
export async function updateRepository(
  repoName: string, 
  htmlContent: string, 
  commitMessage: string, 
  options: UpdateRepositoryOptions = {}
): Promise<{ commitSha: string; commitUrl: string }> {
    console.log(`=== GITHUB REPOSITORY UPDATE ===`);
    console.log(`Updating repository: ${repoName}`);
    console.log(`Commit message: ${commitMessage}`);
    console.log(`HTML content length: ${htmlContent.length} characters`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        // Find the main content file to update
        console.log(`Finding main content file to update...`);
        let targetFile = 'index.html'; // default fallback
        let currentFile: any;
        
        // Try to find the main file by attempting to get content without specifying a file
        try {
            const content = await getRepositoryContent(repoName);
            // If we got content, we need to determine which file was actually retrieved
            // For now, we'll assume it's the main file and try common patterns
            const possibleFiles = [
                'app/page.tsx',      // Next.js App Router
                'pages/index.tsx',   // Next.js Pages Router
                'pages/index.js',    // Next.js Pages Router (JS)
                'src/pages/index.astro', // Astro
                'src/App.tsx',       // Vite React
                'src/App.jsx',       // Vite React (JSX)
                'index.html'         // Static HTML
            ];
            
            // Try to find which file exists
            for (const possibleFile of possibleFiles) {
                try {
                    currentFile = await getFileContent(repoName, possibleFile, githubToken);
                    targetFile = possibleFile;
                    console.log(`Found main content file: ${targetFile}`);
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            if (!currentFile) {
                throw new Error('No main content file found to update');
            }
            
        } catch (error) {
            console.error('Failed to find main content file:', error);
            throw new Error('Cannot update repository: no main content file found');
        }
        
        console.log(`Current file SHA for ${targetFile}: ${currentFile.sha}`);
        
        // Generate AI-powered commit message if options are provided
        let finalCommitMessage = commitMessage;
        if (options.genAI && options.userMessage && options.projectName) {
            try {
                console.log(`Generating AI-powered commit message...`);
                const oldContent = options.oldContent || Buffer.from(currentFile.content, 'base64').toString('utf-8');
                
                finalCommitMessage = await generateEnhancedCommitMessage(
                    options.userMessage,
                    true, // isEdit
                    oldContent,
                    htmlContent,
                    options.projectName,
                    options.genAI
                );
                
                console.log(`AI-generated commit message: "${finalCommitMessage}"`);
            } catch (aiError) {
                console.error('Failed to generate AI commit message:', aiError);
                console.log(`Falling back to provided commit message: "${commitMessage}"`);
                // Keep the original commit message as fallback
            }
        }
        
        // Update the file
        console.log(`Updating ${targetFile} with new content...`);
        const commitData = await updateFile(
            repoName,
            targetFile,
            htmlContent,
            finalCommitMessage,
            currentFile.sha,
            githubToken
        );
        
        console.log(`Repository updated successfully!`);
        console.log(`Commit SHA: ${commitData.commit.sha}`);
        console.log(`Commit URL: ${commitData.commit.html_url}`);
        
        return {
            commitSha: commitData.commit.sha,
            commitUrl: commitData.commit.html_url
        };
        
    } catch (error) {
        console.error('Error updating GitHub repository:', error);
        console.error('Error details:', {
            name: (error as Error).name,
            message: (error as Error).message,
            stack: (error as Error).stack
        });
        throw error;
    }
}

/**
 * Applies multiple file changes to a GitHub repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {Array} changes - Array of file changes from enhanced editing
 * @param {string} commitMessage - Commit message
 * @param {UpdateRepositoryOptions} options - Additional options for AI commit generation
 * @returns {Promise<{commitSha: string; commitUrl: string}>} - Commit information
 */
export async function applyMultiFileChanges(
    repoName: string,
    changes: Array<{
        filePath: string;
        action: 'create' | 'modify' | 'delete';
        content?: string;
        diff?: string;
        reasoning: string;
    }>,
    commitMessage: string,
    options: UpdateRepositoryOptions = {}
): Promise<{ commitSha: string; commitUrl: string }> {
    console.log(`=== APPLYING MULTI-FILE CHANGES ===`);
    console.log(`Repository: ${repoName}`);
    console.log(`Changes: ${changes.length}`);
    console.log(`Commit message: ${commitMessage}`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        // Generate AI-powered commit message if options are provided
        let finalCommitMessage = commitMessage;
        if (options.genAI && options.userMessage && options.projectName) {
            try {
                console.log(`Generating AI-powered commit message for multi-file changes...`);
                
                // Create a summary of changes for AI context
                const changesSummary = changes.map(change =>
                    `${change.action}: ${change.filePath} - ${change.reasoning}`
                ).join('\n');
                
                finalCommitMessage = await generateEnhancedCommitMessage(
                    options.userMessage,
                    true, // isEdit
                    changesSummary, // oldContent (using changes summary as context)
                    `Applied ${changes.length} enhanced changes:\n${changesSummary}`,
                    options.projectName,
                    options.genAI
                );
                
                console.log(`AI-generated commit message: "${finalCommitMessage}"`);
            } catch (aiError) {
                console.error('Failed to generate AI commit message:', aiError);
                console.log(`Falling back to provided commit message: "${commitMessage}"`);
            }
        }
        
        // Apply each change sequentially
        let lastCommitSha: string | null = null;
        let lastCommitUrl: string | null = null;
        
        for (const change of changes) {
            console.log(`Applying ${change.action} to ${change.filePath}...`);
            
            try {
                if (change.action === 'create' || change.action === 'modify') {
                    if (!change.content) {
                        console.warn(`Skipping ${change.action} for ${change.filePath}: no content provided`);
                        continue;
                    }
                    
                    let currentFile: any = null;
                    
                    // For modify actions, get current file SHA
                    if (change.action === 'modify') {
                        try {
                            currentFile = await getFileContent(repoName, change.filePath, githubToken);
                        } catch (error) {
                            console.warn(`File ${change.filePath} not found for modify, treating as create`);
                        }
                    }
                    
                    const changeCommitMessage = `${finalCommitMessage} - ${change.action} ${change.filePath}`;
                    
                    if (currentFile) {
                        // Update existing file
                        const commitData = await updateFile(
                            repoName,
                            change.filePath,
                            change.content,
                            changeCommitMessage,
                            currentFile.sha,
                            githubToken
                        );
                        lastCommitSha = commitData.commit.sha;
                        lastCommitUrl = commitData.commit.html_url;
                    } else {
                        // Create new file
                        const commitData = await createFile(
                            repoName,
                            change.filePath,
                            change.content,
                            changeCommitMessage,
                            githubToken
                        );
                        lastCommitSha = commitData.commit.sha;
                        lastCommitUrl = commitData.commit.html_url;
                    }
                    
                } else if (change.action === 'delete') {
                    // Delete file
                    try {
                        const currentFile = await getFileContent(repoName, change.filePath, githubToken);
                        const commitData = await deleteFile(
                            repoName,
                            change.filePath,
                            `${finalCommitMessage} - delete ${change.filePath}`,
                            currentFile.sha,
                            githubToken
                        );
                        lastCommitSha = commitData.commit.sha;
                        lastCommitUrl = commitData.commit.html_url;
                    } catch (error) {
                        console.warn(`Failed to delete ${change.filePath}: ${error.message}`);
                    }
                }
                
                // Add small delay to avoid GitHub API rate limiting
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.error(`Failed to apply ${change.action} to ${change.filePath}:`, error);
                // Continue with other changes even if one fails
            }
        }
        
        if (!lastCommitSha || !lastCommitUrl) {
            throw new Error('No changes were successfully applied');
        }
        
        console.log(`✅ Multi-file changes applied successfully!`);
        console.log(`Final commit SHA: ${lastCommitSha}`);
        console.log(`Final commit URL: ${lastCommitUrl}`);
        
        return {
            commitSha: lastCommitSha,
            commitUrl: lastCommitUrl
        };
        
    } catch (error) {
        console.error('Error applying multi-file changes:', error);
        throw error;
    }
}

/**
 * Gets the current content of a repository file
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} filePath - Path to the file
 * @returns {Promise<string>} - File content
 */
export async function getRepositoryContent(repoName: string, filePath?: string): Promise<string> {
    console.log(`Getting content from ${repoName}${filePath ? `/${filePath}` : ' (auto-detecting main file)'}`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        // If no specific file path provided, try to find the main content file
        if (!filePath) {
            // Try different common entry points in order of preference
            const possibleFiles = [
                'app/page.tsx',      // Next.js App Router
                'pages/index.tsx',   // Next.js Pages Router
                'pages/index.js',    // Next.js Pages Router (JS)
                'src/pages/index.astro', // Astro
                'src/App.tsx',       // Vite React
                'src/App.jsx',       // Vite React (JSX)
                'index.html',        // Static HTML
                'README.md'          // Fallback to README
            ];
            
            for (const possibleFile of possibleFiles) {
                try {
                    console.log(`Trying to get content from ${repoName}/${possibleFile}`);
                    const fileData = await getFileContent(repoName, possibleFile, githubToken);
                    const content = Buffer.from(fileData.content, 'base64').toString('utf-8');
                    console.log(`Successfully retrieved content from ${possibleFile} (${content.length} characters)`);
                    return content;
                } catch (error) {
                    console.log(`File ${possibleFile} not found, trying next...`);
                    continue;
                }
            }
            
            // If no files found, return a placeholder
            console.log(`No main content file found in ${repoName}, returning placeholder`);
            return `<!-- Repository content from ${repoName} -->
<html><head><title>Repository Content</title></head>
<body><h1>Repository: ${repoName}</h1>
<p>This repository has been imported and is ready for vector-based editing.</p>
</body></html>`;
        }
        
        // If specific file path provided, get that file
        const fileData = await getFileContent(repoName, filePath, githubToken);
        const content = Buffer.from(fileData.content, 'base64').toString('utf-8');
        return content;
        
    } catch (error) {
        console.error('Error getting repository content:', error);
        throw error;
    }
}

/**
 * Validates if a repository exists and is accessible
 * @param {string} repoName - Full repository name (org/repo)
 * @returns {Promise<GitHubValidationResult>} - Validation result
 */
export async function validateRepositoryAccess(repoName: string): Promise<GitHubValidationResult> {
    console.log(`=== VALIDATING REPOSITORY ACCESS ===`);
    console.log(`Checking repository: ${repoName}`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        const response = await fetch(`https://api.github.com/repos/${repoName}`, {
            headers: {
                'Authorization': `Bearer ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        if (!response.ok) {
            if (response.status === 404) {
                return {
                    isValid: false,
                    error: 'Repository not found',
                    message: `Repository "${repoName}" does not exist or is not accessible`
                };
            } else if (response.status === 403) {
                return {
                    isValid: false,
                    error: 'Access denied',
                    message: `Access denied to repository "${repoName}". Check permissions.`
                };
            } else {
                const errorData = await response.json() as GitHubApiError;
                return {
                    isValid: false,
                    error: 'API error',
                    message: `GitHub API error: ${response.status} - ${errorData.message}`
                };
            }
        }
        
        const repoData = await response.json();
        console.log(`Repository found: ${repoData.full_name}`);
        console.log(`Repository description: ${repoData.description || 'No description'}`);
        console.log(`Repository URL: ${repoData.html_url}`);
        
        return {
            isValid: true,
            repoData: {
                fullName: repoData.full_name,
                description: repoData.description,
                htmlUrl: repoData.html_url,
                cloneUrl: repoData.clone_url,
                defaultBranch: repoData.default_branch || 'main',
                createdAt: repoData.created_at,
                updatedAt: repoData.updated_at
            }
        };
        
    } catch (error) {
        console.error('Error validating repository access:', error);
        return {
            isValid: false,
            error: 'Network error',
            message: `Failed to check repository: ${(error as Error).message}`
        };
    }
}


/**
 * Gets repository metadata for import
 * @param {string} repoName - Full repository name (org/repo)
 * @returns {Promise<any>} - Repository metadata
 */
export async function getRepositoryMetadata(repoName: string): Promise<any> {
    console.log(`=== GETTING REPOSITORY METADATA ===`);
    console.log(`Getting metadata for: ${repoName}`);
    
    const githubToken = process.env.GITHUB_TOKEN;
    
    if (!githubToken) {
        throw new Error('GITHUB_TOKEN environment variable is required');
    }
    
    try {
        // Get repository info
        const repoResponse = await fetch(`https://api.github.com/repos/${repoName}`, {
            headers: {
                'Authorization': `Bearer ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        if (!repoResponse.ok) {
            throw new Error(`Failed to get repository metadata: ${repoResponse.status}`);
        }
        
        const repoData = await repoResponse.json();
        
        // Get latest commit info
        const commitsResponse = await fetch(`https://api.github.com/repos/${repoName}/commits?per_page=1`, {
            headers: {
                'Authorization': `Bearer ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        let latestCommitSha: string | null = null;
        if (commitsResponse.ok) {
            const commits = await commitsResponse.json();
            if (commits.length > 0) {
                latestCommitSha = commits[0].sha;
                console.log(`Latest commit SHA: ${latestCommitSha}`);
            }
        }
        
        const metadata = {
            fullName: repoData.full_name,
            name: repoData.name,
            description: repoData.description || `Imported repository: ${repoData.name}`,
            htmlUrl: repoData.html_url,
            cloneUrl: repoData.clone_url,
            defaultBranch: repoData.default_branch || 'main',
            createdAt: repoData.created_at,
            updatedAt: repoData.updated_at,
            latestCommitSha
        };
        
        console.log(`Repository metadata:`, {
            name: metadata.name,
            description: metadata.description,
            latestCommit: metadata.latestCommitSha
        });
        
        return metadata;
        
    } catch (error) {
        console.error('Error getting repository metadata:', error);
        throw error;
    }
}

/**
 * Imports repository data for database storage
 * @param {string} repoName - Full repository name (org/repo)
 * @returns {Promise<GitHubImportData>} - Import data ready for database
 */
export async function importRepositoryData(repoName: string): Promise<GitHubImportData> {
    console.log(`=== IMPORTING REPOSITORY DATA ===`);
    console.log(`Importing data from: ${repoName}`);
    
    try {
        // Validate repository access
        const validation = await validateRepositoryAccess(repoName);
        if (!validation.isValid) {
            throw new Error(validation.message);
        }
        
        console.log(`✅ Repository access validated, proceeding with import`);
        
        // Get repository metadata
        const metadata = await getRepositoryMetadata(repoName);
        
        // Get main content file using auto-detection
        console.log(`Getting main content file for repository: ${repoName}`);
        const htmlContent = await getRepositoryContent(repoName);
        console.log(`Retrieved content (${htmlContent.length} characters) for import`);
        
        // Generate project name from repository name
        const projectName = metadata.name.toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
        
        const importData: GitHubImportData = {
            projectName,
            description: metadata.description,
            htmlContent,
            githubRepoUrl: metadata.htmlUrl,
            githubRepoName: metadata.fullName,
            lastCommitSha: metadata.latestCommitSha,
            url: null // Will be set during deployment
        };
        
        console.log(`Import data prepared:`, {
            projectName: importData.projectName,
            description: importData.description,
            htmlContentLength: importData.htmlContent.length,
            githubRepoName: importData.githubRepoName
        });
        
        return importData;
        
    } catch (error) {
        console.error('Error importing repository data:', error);
        throw error;
    }
}

/**
 * Creates a GitHub repository via API
 * @param {string} name - Repository name
 * @param {string} description - Repository description
 * @param {string} token - GitHub token
 * @param {string} username - GitHub username (optional, uses authenticated user if not provided)
 * @returns {Promise<any>} - Repository data
 */
async function createGitHubRepo(name: string, description: string, token: string, username?: string): Promise<any> {
    console.log(`=== DEBUG: GitHub Repository Creation ===`);
    console.log(`Repository name: "${name}"`);
    console.log(`Description: "${description}"`);
    console.log(`Description length: ${description ? description.length : 0} characters`);
    console.log(`Token present: ${!!token}`);
    console.log(`Token length: ${token ? token.length : 0}`);
    console.log(`Username: ${username || 'not provided'}`);
    
    // DIAGNOSTIC: Check description length before API call
    if (description && description.length > 350) {
        console.log(`⚠️  DESCRIPTION TOO LONG: ${description.length} characters (max: 350)`);
        console.log(`Original description: "${description}"`);
        
        // Truncate description to fit GitHub's limit
        const truncatedDescription = description.substring(0, 347) + '...';
        console.log(`Truncated description: "${truncatedDescription}"`);
        console.log(`New length: ${truncatedDescription.length} characters`);
        description = truncatedDescription;
    }
    
    // Determine API endpoint - use user repos if no specific username, or org repos if username provided
    let apiUrl: string;
    if (username) {
        // Check if this is an organization by trying to get org info first
        try {
            const orgResponse = await fetch(`https://api.github.com/orgs/${username}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/vnd.github.v3+json'
                }
            });
            
            if (orgResponse.ok) {
                // It's an organization
                apiUrl = `https://api.github.com/orgs/${username}/repos`;
            } else {
                // It's a user account, but we can't create repos for other users
                // Fall back to authenticated user
                apiUrl = 'https://api.github.com/user/repos';
                console.log(`Cannot create repository for user ${username}, using authenticated user instead`);
            }
        } catch (error) {
            // Fall back to authenticated user
            apiUrl = 'https://api.github.com/user/repos';
            console.log(`Error checking ${username}, using authenticated user instead`);
        }
    } else {
        // Use authenticated user's account
        apiUrl = 'https://api.github.com/user/repos';
    }
    
    console.log(`Using API URL: ${apiUrl}`);
    
    const requestBody = {
        name: name,
        description: description,
        private: true,
        auto_init: false,  // Don't auto-initialize to avoid conflicts
        // Enable GitHub Actions permissions
        has_issues: true,
        has_projects: true,
        has_wiki: true,
        // Allow GitHub Actions to have read/write permissions and create PRs
        allow_squash_merge: true,
        allow_merge_commit: true,
        allow_rebase_merge: true,
        delete_branch_on_merge: true
    };
    
    console.log(`Request body:`, JSON.stringify(requestBody, null, 2));
    
    const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    
    console.log(`Response status: ${response.status}`);
    console.log(`Response status text: ${response.statusText}`);
    
    if (!response.ok) {
        const errorData = await response.json() as GitHubApiError;
        console.log(`GitHub API error response:`, JSON.stringify(errorData, null, 2));
        
        // Add specific handling for common 422 errors
        if (response.status === 422) {
            console.log(`=== DIAGNOSING 422 ERROR ===`);
            
            if (errorData.errors) {
                const errors = errorData.errors.map(err => `${err.field}: ${err.message}`).join(', ');
                console.log(`Validation errors: ${errors}`);
                
                // Check for specific error types
                const descriptionError = errorData.errors.find(err => err.field === 'description');
                const nameError = errorData.errors.find(err => err.field === 'name');
                
                if (descriptionError) {
                    console.log(`🔍 DESCRIPTION ERROR DETECTED: ${descriptionError.message}`);
                    console.log(`Current description length: ${description ? description.length : 0}`);
                }
                
                if (nameError) {
                    console.log(`🔍 NAME ERROR DETECTED: ${nameError.message}`);
                    console.log(`Current name: "${name}"`);
                }
            }
            
            // Check if it's a repository name conflict
            if (errorData.message && (errorData.message.includes('already exists') || errorData.message.includes('name already exists'))) {
                console.log(`🔍 REPOSITORY NAME CONFLICT DETECTED for: ${name}`);
                console.log(`Attempting to resolve with timestamp suffix...`);
                
                // Try with a timestamp suffix
                const timestampSuffix = Date.now().toString().slice(-6);
                const newName = `${name}-${timestampSuffix}`;
                console.log(`Retrying with name: ${newName}`);
                
                const retryRequestBody = { ...requestBody, name: newName };
                console.log(`Retry request body:`, JSON.stringify(retryRequestBody, null, 2));
                
                const retryResponse = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/vnd.github.v3+json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(retryRequestBody)
                });
                
                console.log(`Retry response status: ${retryResponse.status}`);
                
                if (retryResponse.ok) {
                    console.log(`✅ Repository created successfully with name: ${newName}`);
                    return await retryResponse.json();
                } else {
                    const retryErrorData = await retryResponse.json();
                    console.log(`❌ Retry also failed:`, JSON.stringify(retryErrorData, null, 2));
                }
            }
        }
        
        // Add diagnostic logging for other common errors
        if (response.status === 401) {
            console.log(`🔍 AUTHENTICATION ERROR: Token might be invalid or expired`);
            console.log(`Token starts with: ${token ? token.substring(0, 8) + '...' : 'none'}`);
        }
        
        if (response.status === 403) {
            console.log(`🔍 PERMISSION ERROR: Token might lack repository creation permissions`);
            console.log(`Required scopes: repo, public_repo`);
        }
        
        if (response.status === 429) {
            console.log(`🔍 RATE LIMIT ERROR: Too many requests to GitHub API`);
        }
        
        throw new Error(`GitHub API error: ${response.status} - ${errorData.message || 'Repository creation failed.'}`);
    }
    
    const result = await response.json();
    console.log(`Repository created successfully: ${result.full_name}`);
    return result;
}

/**
 * Configures GitHub Actions permissions for a repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} token - GitHub token
 */
async function configureGitHubActionsPermissions(repoName: string, token: string): Promise<void> {
    console.log(`=== CONFIGURING GITHUB ACTIONS PERMISSIONS ===`);
    console.log(`Repository: ${repoName}`);
    
    try {
        // Configure Actions permissions to allow read/write access
        console.log(`🔧 Setting Actions permissions to read/write...`);
        const actionsPermissionsResponse = await fetch(`https://api.github.com/repos/${repoName}/actions/permissions`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                enabled: true,
                allowed_actions: 'all'
            })
        });
        
        if (!actionsPermissionsResponse.ok) {
            const errorData = await actionsPermissionsResponse.json().catch(() => ({}));
            console.warn(`⚠️  Failed to set Actions permissions: ${actionsPermissionsResponse.status}`);
            console.warn(`Error details:`, errorData);
        } else {
            console.log(`✅ Actions permissions configured successfully`);
        }
        
        // Configure default workflow permissions to read/write
        console.log(`🔧 Setting default workflow permissions to read/write...`);
        const workflowPermissionsResponse = await fetch(`https://api.github.com/repos/${repoName}/actions/permissions/workflow`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                default_workflow_permissions: 'write',
                can_approve_pull_request_reviews: true
            })
        });
        
        if (!workflowPermissionsResponse.ok) {
            const errorData = await workflowPermissionsResponse.json().catch(() => ({}));
            console.warn(`⚠️  Failed to set workflow permissions: ${workflowPermissionsResponse.status}`);
            console.warn(`Error details:`, errorData);
        } else {
            console.log(`✅ Workflow permissions configured successfully`);
        }
        
        console.log(`✅ GitHub Actions permissions configuration completed`);
        
    } catch (error) {
        console.error(`❌ Error configuring GitHub Actions permissions:`, error);
        console.warn(`⚠️  Repository will still function, but Actions may have limited permissions`);
        // Don't throw error as this is not critical for repository creation
    }
}

/**
 * Creates initial files in the repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} htmlContent - Website HTML content
 * @param {string} description - Project description
 * @param {string} token - GitHub token
 * @param {string} projectName - Project name for AI commit generation
 * @param {CreateRepositoryOptions} options - Options for AI commit generation
 */
async function createInitialFiles(
  repoName: string,
  htmlContent: string,
  description: string,
  token: string,
  projectName: string,
  options: CreateRepositoryOptions = {}
): Promise<void> {
    // Generate AI-powered commit message for initial creation
    let initialCommitMessage = 'Initial website creation';
    if (options.genAI && options.userMessage && projectName) {
        try {
            console.log(`Generating AI-powered initial commit message...`);
            initialCommitMessage = await generateEnhancedCommitMessage(
                options.userMessage,
                false, // isEdit = false for new creation
                null, // oldContent
                htmlContent,
                projectName,
                options.genAI
            );
            console.log(`AI-generated initial commit message: "${initialCommitMessage}"`);
        } catch (aiError) {
            console.error('Failed to generate AI initial commit message:', aiError);
            console.log(`Using fallback commit message: "${initialCommitMessage}"`);
        }
    }
    
    // If scaffold files are provided, create them instead of default files
    if (options.scaffoldFiles && options.scaffoldFiles.length > 0) {
        console.log(`=== CREATING SCAFFOLD FILES ===`);
        console.log(`Total scaffold files to create: ${options.scaffoldFiles.length}`);
        console.log(`Repository: ${repoName}`);
        
        let successCount = 0;
        let failureCount = 0;
        
        for (const scaffoldFile of options.scaffoldFiles) {
            try {
                console.log(`📁 Creating scaffold file: ${scaffoldFile.path} (${scaffoldFile.content.length} chars)`);
                
                await createFile(
                    repoName,
                    scaffoldFile.path,
                    scaffoldFile.content,
                    `Add ${scaffoldFile.path} - ${scaffoldFile.description}`,
                    token
                );
                
                successCount++;
                console.log(`✅ Successfully created scaffold file: ${scaffoldFile.path}`);
                
                // Add small delay to avoid GitHub API rate limiting
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                failureCount++;
                console.error(`❌ Failed to create scaffold file ${scaffoldFile.path}:`, error);
                console.error(`Error details:`, {
                    message: error.message,
                    status: error.status || 'unknown'
                });
                // Continue with other files even if one fails
            }
        }
        
        console.log(`=== SCAFFOLD FILE CREATION SUMMARY ===`);
        console.log(`✅ Successfully created: ${successCount} files`);
        console.log(`❌ Failed to create: ${failureCount} files`);
        console.log(`📊 Success rate: ${Math.round((successCount / options.scaffoldFiles.length) * 100)}%`);
        
        // Check if README.md was already created in scaffold files
        const hasReadme = options.scaffoldFiles.some(file => file.path === 'README.md');
        if (!hasReadme) {
            console.log(`📝 Creating README.md for scaffold project...`);
            const readmeContent = generateScaffoldReadmeContent(description, options.scaffoldFiles);
            await createFile(
                repoName,
                'README.md',
                readmeContent,
                'Add project README',
                token
            );
        } else {
            console.log(`✅ README.md already exists in scaffold files, skipping creation`);
        }
        
    } else {
        // Create standard website files
        // Create index.html
        await createFile(
            repoName,
            'index.html',
            htmlContent,
            initialCommitMessage,
            token
        );
        
        // Create README.md
        const readmeContent = generateReadmeContent(description);
        await createFile(
            repoName,
            'README.md',
            readmeContent,
            'Add project README',
            token
        );
        
        // Create vercel.json for deployment configuration
        const vercelConfig = generateVercelConfig();
        await createFile(
            repoName,
            'vercel.json',
            vercelConfig,
            'Add Vercel deployment configuration',
            token
        );
    }
    
    // Create .gitignore (common for both scaffold and standard projects)
    const gitignoreContent = generateGitignoreContent();
    await createFile(
        repoName,
        '.gitignore',
        gitignoreContent,
        'Add .gitignore file',
        token
    );
    
    // Create GitHub Actions workflow and indexing script in a single atomic commit
    console.log(`📋 Creating GitHub Actions workflow and indexing script in single commit...`);
    try {
        const workflowContent = generateIndexerWorkflowContent();
        const indexingScriptContent = generateIndexingScriptContent();
        
        // Create both files atomically using batch commit to prevent workflow running without script
        await createMultipleFiles(
            repoName,
            [
                {
                    path: '.github/workflows/indexer.yml',
                    content: workflowContent
                },
                {
                    path: 'scripts/indexRepo.mjs',
                    content: indexingScriptContent
                }
            ],
            'Add repository indexing workflow and script',
            token
        );
        
        console.log(`✅ GitHub Actions workflow and indexing script created in single commit`);
    } catch (error) {
        if (error.message && error.message.includes('already exists')) {
            console.log(`ℹ️  Repository indexing files already exist, skipping creation`);
        } else {
            console.error(`❌ Failed to create repository indexing files:`, error.message);
            throw error;
        }
    }
    
    // Set up repository secrets for the indexing workflow
    console.log(`🔐 Setting up repository secrets for indexing workflow...`);
    await setupRepositorySecrets(repoName, token);
    console.log(`✅ Repository secrets configured`);
}

/**
 * Creates a file in the repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} filePath - Path for the new file
 * @param {string} content - File content
 * @param {string} message - Commit message
 * @param {string} token - GitHub token
 */
async function createFile(repoName: string, filePath: string, content: string, message: string, token: string): Promise<any> {
    console.log(`Creating file: ${filePath} in ${repoName}`);
    
    const response = await fetch(`https://api.github.com/repos/${repoName}/contents/${filePath}`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            content: Buffer.from(content).toString('base64')
        })
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        console.error(`GitHub API error for ${filePath}:`, {
            status: response.status,
            statusText: response.statusText,
            error: errorData
        });
        throw new Error(`Failed to create file ${filePath}: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }
    
    const result = await response.json();
    console.log(`Successfully created file: ${filePath}`);
    return result;
}

/**
 * Creates multiple files in a single commit using GitHub's Git Data API
 * @param {string} repoName - Full repository name (org/repo)
 * @param {Array} files - Array of files to create
 * @param {string} message - Commit message
 * @param {string} token - GitHub token
 */
async function createMultipleFiles(
    repoName: string,
    files: Array<{path: string; content: string}>,
    message: string,
    token: string
): Promise<any> {
    console.log(`Creating ${files.length} files in single commit: ${repoName}`);
    
    try {
        // Get the current HEAD commit
        const headResponse = await fetch(`https://api.github.com/repos/${repoName}/git/refs/heads/main`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        if (!headResponse.ok) {
            // Try 'master' branch if 'main' doesn't exist
            const masterResponse = await fetch(`https://api.github.com/repos/${repoName}/git/refs/heads/master`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/vnd.github.v3+json'
                }
            });
            
            if (!masterResponse.ok) {
                throw new Error(`Failed to get HEAD reference: ${headResponse.status}`);
            }
            
            const masterData = await masterResponse.json();
            var headSha = masterData.object.sha;
            var branchName = 'master';
        } else {
            const headData = await headResponse.json();
            var headSha = headData.object.sha;
            var branchName = 'main';
        }
        
        console.log(`Current HEAD SHA: ${headSha} on branch: ${branchName}`);
        
        // Get the current tree
        const treeResponse = await fetch(`https://api.github.com/repos/${repoName}/git/commits/${headSha}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        if (!treeResponse.ok) {
            throw new Error(`Failed to get current commit: ${treeResponse.status}`);
        }
        
        const commitData = await treeResponse.json();
        const baseTreeSha = commitData.tree.sha;
        
        // Create blobs for each file
        const treeItems: Array<{
            path: string;
            mode: string;
            type: string;
            sha: string;
        }> = [];
        for (const file of files) {
            console.log(`Creating blob for: ${file.path}`);
            
            const blobResponse = await fetch(`https://api.github.com/repos/${repoName}/git/blobs`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/vnd.github.v3+json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: Buffer.from(file.content).toString('base64'),
                    encoding: 'base64'
                })
            });
            
            if (!blobResponse.ok) {
                const errorData = await blobResponse.json();
                throw new Error(`Failed to create blob for ${file.path}: ${blobResponse.status} - ${errorData.message}`);
            }
            
            const blobData = await blobResponse.json();
            
            treeItems.push({
                path: file.path,
                mode: '100644',
                type: 'blob',
                sha: blobData.sha
            });
        }
        
        // Create new tree
        console.log(`Creating new tree with ${treeItems.length} items`);
        const newTreeResponse = await fetch(`https://api.github.com/repos/${repoName}/git/trees`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                base_tree: baseTreeSha,
                tree: treeItems
            })
        });
        
        if (!newTreeResponse.ok) {
            const errorData = await newTreeResponse.json();
            throw new Error(`Failed to create tree: ${newTreeResponse.status} - ${errorData.message}`);
        }
        
        const newTreeData = await newTreeResponse.json();
        
        // Create new commit
        console.log(`Creating commit: ${message}`);
        const commitResponse = await fetch(`https://api.github.com/repos/${repoName}/git/commits`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                tree: newTreeData.sha,
                parents: [headSha]
            })
        });
        
        if (!commitResponse.ok) {
            const errorData = await commitResponse.json();
            throw new Error(`Failed to create commit: ${commitResponse.status} - ${errorData.message}`);
        }
        
        const newCommitData = await commitResponse.json();
        
        // Update the branch reference
        console.log(`Updating ${branchName} branch to new commit: ${newCommitData.sha}`);
        const updateRefResponse = await fetch(`https://api.github.com/repos/${repoName}/git/refs/heads/${branchName}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sha: newCommitData.sha
            })
        });
        
        if (!updateRefResponse.ok) {
            const errorData = await updateRefResponse.json();
            throw new Error(`Failed to update branch: ${updateRefResponse.status} - ${errorData.message}`);
        }
        
        console.log(`✅ Successfully created ${files.length} files in single commit`);
        console.log(`Commit SHA: ${newCommitData.sha}`);
        
        return {
            commit: newCommitData,
            files: files.map(f => f.path)
        };
        
    } catch (error) {
        console.error(`Failed to create multiple files:`, error);
        throw error;
    }
}

/**
 * Gets file content from repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} filePath - Path to the file
 * @param {string} token - GitHub token
 * @returns {Promise<GitHubFileData>} - File data including content and SHA
 */
async function getFileContent(repoName: string, filePath: string, token: string): Promise<GitHubFileData> {
    const response = await fetch(`https://api.github.com/repos/${repoName}/contents/${filePath}`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json'
        }
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to get file ${filePath}: ${errorData.message}`);
    }
    
    return await response.json();
}

/**
 * Generates README content for the website project
 * @param {string} description - Project description
 * @returns {string} - README markdown content
 */
function generateReadmeContent(description: string): string {
    return `# Generated Website

${description}

## About This Project

This website was generated using an AI-powered WhatsApp bot that creates custom websites based on user prompts.

## Features

- 🤖 AI-generated content and design
- 📱 Mobile-responsive layout
- ⚡ Fast loading and optimized
- 🚀 Deployed on Vercel

## Deployment

This site is automatically deployed to Vercel when changes are pushed to the main branch.

## Generated by

WhatsApp Website Bot - Creating beautiful websites from simple text prompts.

---

*This README was automatically generated.*
`;
}

/**
 * Generates README content for scaffold projects
 * @param {string} description - Project description
 * @param {Array} scaffoldFiles - Array of scaffold files
 * @returns {string} - README markdown content
 */
function generateScaffoldReadmeContent(description: string, scaffoldFiles: Array<{path: string; content: string; type: string; description: string}>): string {
    const fileList = scaffoldFiles.map(file => `- \`${file.path}\` - ${file.description}`).join('\n');
    
    return `# ${description}

This project was scaffolded using an AI-powered WhatsApp bot.

## Project Structure
${fileList}

## Getting Started

1. Clone this repository:
   \`\`\`bash
   git clone <repository-url>
   cd <repository-name>
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Start the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

4. Open your browser and navigate to the local development URL (usually \`http://localhost:3000\`)

## Deployment
This project is automatically deployed to Vercel when changes are pushed to the main branch.

## Features
- Modern project structure
- Development server with hot reload
- Production-ready build system
- Automatic deployment pipeline

---
*Generated with ❤️ by WhatsApp Bot*`;
}

/**
 * Generates Vercel configuration for the website
 * @returns {string} - Vercel JSON configuration
 */
function generateVercelConfig(): string {
    return JSON.stringify({
        "version": 2,
        "builds": [
            {
                "src": "index.html",
                "use": "@vercel/static"
            }
        ],
        "routes": [
            {
                "src": "/(.*)",
                "dest": "/index.html"
            }
        ]
    }, null, 2);
}

/**
 * Generates .gitignore content for the website project
 * @returns {string} - .gitignore content
 */
function generateGitignoreContent(): string {
    return `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
`;
}

/**
 * Generates GitHub Actions workflow content for repository indexing
 * @returns {string} - GitHub Actions workflow YAML content
 */
function generateIndexerWorkflowContent(): string {
    return `name: Repository Indexer
on:
  push:
    branches: [main, master]
  workflow_dispatch:

jobs:
  index-repository:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: \${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          # Don't use cache since we may not have package-lock.json
          
      - name: Check for existing package.json
        run: |
          if [ -f "package.json" ]; then
            echo "📋 Found existing package.json"
            cat package.json
            echo "🔧 Installing project dependencies..."
            npm ci || npm install
          else
            echo "⚠️  No package.json found, skipping project dependencies"
          fi
        continue-on-error: true
        
      - name: Build project (optional)
        run: |
          if [ -f "package.json" ] && npm run build --if-present; then
            echo "✅ Project built successfully"
          else
            echo "⚠️  No build script found or build failed, continuing..."
          fi
        continue-on-error: true
        
      - name: Create package.json for indexing dependencies
        run: |
          cat > package.json << 'EOF'
          {
            "name": "repo-indexer",
            "version": "1.0.0",
            "type": "commonjs",
            "description": "Repository indexing script for vector search",
            "main": "scripts/indexRepo.mjs",
            "dependencies": {
              "@google/genai": "^1.9.0",
              "@llamaindex/google": "^0.3.13",
              "@supabase/supabase-js": "^2.50.5",
              "llamaindex": "^0.11.17",
              "pg": "^8.16.3"
            },
            "engines": {
              "node": ">=18.0.0"
            }
          }
          EOF
          echo "📋 Created package.json for indexing:"
          cat package.json
          
      - name: Install indexing dependencies
        run: |
          echo "📦 Installing indexing dependencies..."
          npm install
          echo "✅ Dependencies installed successfully"
          echo "📋 Installed packages:"
          npm list --depth=0
          
      - name: Run repository indexer
        run: |
          echo "🚀 Starting repository indexer..."
          echo "📂 Current directory: \$(pwd)"
          echo "📋 Directory contents:"
          ls -la
          echo "📜 Checking indexer script:"
          if [ -f "scripts/indexRepo.mjs" ]; then
            echo "✅ Indexer script found"
            echo "📏 Script size: \$(wc -l < scripts/indexRepo.mjs) lines"
          else
            echo "❌ Indexer script not found!"
            exit 1
          fi
          echo "🔧 Running indexer..."
          node scripts/indexRepo.mjs
        env:
          GOOGLE_API_KEY: \${{ secrets.GOOGLE_API_KEY }}
          SUPABASE_URL: \${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: \${{ secrets.SUPABASE_ANON_KEY }}
          GITHUB_TOKEN: \${{ secrets.GITHUB_TOKEN }}
          GITHUB_REPOSITORY: \${{ github.repository }}
        if: always()

      - name: Check if repo map was created
        run: |
          if [ -f "ai/repo-map.json" ]; then
            echo "✅ Repository map created successfully"
            echo "📁 File size: \$(stat -c%s ai/repo-map.json) bytes"
            echo "📊 File preview (first 10 lines):"
            head -10 ai/repo-map.json
          else
            echo "❌ Repository map file not found"
            exit 1
          fi

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Commit and push repo map
        run: |
          git add ai/repo-map.json
          if git diff --staged --quiet; then
            echo "📝 No changes to commit"
          else
            git commit -m "Update repository map [skip ci]"
            git push
            echo "✅ Repository map committed and pushed"
          fi
`;
}

/**
 * Generates the repository indexing script content
 * @returns {string} - JavaScript indexing script content
 */
function generateIndexingScriptContent(): string {
    return `/******************************************************************************************
 * scripts/indexRepo.mjs
 *
 * Modern, LlamaIndex-based Repository Indexer
 * - Uses llamaindex for document parsing and chunking.
 * - Embeds each chunk with Google Gemini.
 * - Upserts into Supabase \\\`repo_chunks\\\`.
 *
 * Prerequisites (installed in the Action or locally):
 *   npm i @google/genai @supabase/supabase-js llamaindex @llamaindex/google pg
 ******************************************************************************************/

console.log('🔍 Starting module imports...');

import { GoogleGenAI } from '@google/genai';
import { createClient } from '@supabase/supabase-js';
import { Document, Settings, SimpleNodeParser } from 'llamaindex';
import { GeminiEmbedding } from '@llamaindex/google';
import { promises as fs } from 'fs';
import { relative, join, extname } from 'path';

console.log('✅ All modules imported successfully');

class RepositoryIndexer {
  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    this.embedModel = new GeminiEmbedding({
      model: EMBEDDING_CONFIG.MODEL, // Use centralized model configuration
    });
    this.supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);
    
    // Configure LlamaIndex settings
    Settings.embedModel = this.embedModel;
    Settings.chunkSize = 1024;
    Settings.chunkOverlap = 20;
    
    this.nodeParser = new SimpleNodeParser({
        chunkSize: Settings.chunkSize,
        chunkOverlap: Settings.chunkOverlap,
    });

    this.repoId = process.env.GITHUB_REPOSITORY; // owner/repo format
    this.projectId = null; // Will be resolved from user_projects table
    this.vectorDim = EMBEDDING_CONFIG.DIMENSIONS; // Use centralized dimension configuration
    this.batchSize = EMBEDDING_CONFIG.BATCH_SIZE; // Use centralized batch size
    this.skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', '.vercel', 'coverage'];
  }

  async run() {
    console.log(\`🚀 Starting indexing for repository: \${this.repoId}\`);
    console.log(\`🔧 Using embedding model: \${this.embedModel.model || 'models/text-embedding-004'}\`);
    console.log(\`🔧 Vector dimensions: \${this.vectorDim}\`);
    
    try {
      console.log('📋 Step 1: Resolving project ID...');
      await this.resolveProjectId();
      
      console.log('📋 Step 2: Building repository map...');
      // Determine the correct root path based on current working directory
      const rootPath = process.cwd().endsWith('/scripts') ? '..' : '.';
      console.log(\`📂 Using root path: \${rootPath}\`);
      const repoMap = await this.buildRepoMap(rootPath);
      
      console.log('📋 Step 3: Chunking and embedding content...');
      const chunks = await this.chunkAndEmbed(repoMap);
      
      console.log('📋 Step 4: Upserting chunks to database...');
      await this.upsertChunks(chunks);
      
      console.log('📋 Step 5: Committing repository map...');
      await this.commitRepoMap(repoMap);
      
      console.log(\`✅ Indexing completed successfully: \${chunks.length} chunks processed\`);
      
    } catch (error) {
      console.error('❌ Indexing failed:', error);
      console.error('❌ Error message:', error.message);
      console.error('❌ Stack trace:', error.stack);
      
      try {
        if (this.projectId) {
          await this.supabase
            .from('user_projects')
            .update({ repo_indexed_at: null, repo_map_path: null })
            .eq('id', this.projectId);
          console.log('⚠️  Updated project status to indicate indexing failure');
        }
      } catch (updateError) {
        console.warn('Failed to update project status:', updateError.message);
      }
      
      process.exit(1);
    }
  }

  async resolveProjectId() {
    console.log(\`🔍 Resolving project ID for repository: \${this.repoId}\`);
    
    const { data: project, error } = await this.supabase
      .from('user_projects')
      .select('id')
      .eq('github_repo_name', this.repoId)
      .single();
    
    if (error) {
      throw new Error(\`Failed to find project for repository \${this.repoId}: \${error.message}\`);
    }
    
    this.projectId = project.id;
    console.log(\`✅ Resolved project ID: \${this.projectId}\`);
  }

  async buildRepoMap(rootPath = '.') {
    console.log('📁 Building repository map...');
    
    const repoMap = {
      structure: {},
      files: [],
      summary: '',
      lastIndexed: new Date().toISOString()
    };

    const files = await this.walkDirectory(rootPath);
    
    for (const filePath of files) {
      if (this.shouldIndexFile(filePath)) {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = relative(rootPath, filePath);
        
        console.log(\`  Processing: \${relativePath}\`);
        
        repoMap.files.push({
          path: relativePath,
          type: this.getFileType(filePath),
          size: content.length,
          summary: await this.generateFileSummary(content, relativePath)
        });
      }
    }

    repoMap.summary = await this.generateRepoSummary(repoMap.files);
    console.log(\`📊 Repository map built: \${repoMap.files.length} files\`);
    
    return repoMap;
  }

  async chunkAndEmbed(repoMap) {
    console.log('🔍 Chunking and embedding content with modern llamaindex...');

    // 1. Create Document objects from all files
    const documents = [];
    for (const file of repoMap.files) {
        try {
            const content = await fs.readFile(file.path, 'utf-8');
            documents.push(new Document({ text: content, id_: file.path }));
        } catch (error) {
            console.warn(\`⚠️  Failed to read file \${file.path}:\`, error.message);
        }
    }
    console.log(\`  Created \${documents.length} Document objects.\`);

    // 2. Use the parser to generate nodes (chunks)
    const nodes = this.nodeParser.getNodesFromDocuments(documents);
    console.log(\`  Generated \${nodes.length} nodes (chunks) from documents.\`);

    // 3. Generate embeddings for each node and create the final chunk objects
    const chunks = [];
    let chunkGlobalId = 0;
    const embeddingBatchSize = EMBEDDING_CONFIG.EMBEDDING_BATCH_SIZE; // Use centralized batch size

    for (let i = 0; i < nodes.length; i += embeddingBatchSize) {
        const batchNodes = nodes.slice(i, i + embeddingBatchSize);
        const batchTexts = batchNodes.map(node => node.getText());
        
        console.log(\`  Embedding batch \${Math.floor(i/embeddingBatchSize) + 1}/\${Math.ceil(nodes.length/embeddingBatchSize)}...\`);

        try {
            const embeddings = await this.embedModel.getTextEmbeddingsBatch(batchTexts);
            console.log(\`  ✅ Successfully generated \${embeddings.length} embeddings\`);

            for (let j = 0; j < batchNodes.length; j++) {
                const node = batchNodes[j];
                const embedding = embeddings[j];

                if (embedding && embedding.length > 0) {
                    console.log(\`  📏 Embedding dimension for chunk \${chunkGlobalId}: \${embedding.length}\`);
                }

                chunks.push({
                    project_id: this.projectId,
                    file_path: node.id_.replace(/_part_\\d+$/, ''), // Get original file path from node id
                    chunk_id: chunkGlobalId++,
                    content: node.getText(),
                    embedding: embedding ? embedding.slice(0, this.vectorDim) : new Array(this.vectorDim).fill(0),
                });
            }
            // Add small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 200));

        } catch (embeddingError) {
            console.warn(\`⚠️  Failed to embed a batch:\`, embeddingError.message);
            console.error(\`⚠️  Embedding error details:\`, embeddingError);
        }
    }

    console.log(\`🧠 Generated \${chunks.length} embedded chunks\`);
    return chunks;
  }

  async upsertChunks(chunks) {
    console.log('💾 Upserting chunks to database...');
    
    const { error: deleteError } = await this.supabase
      .from('repo_chunks')
      .delete()
      .eq('project_id', this.projectId);
    
    if (deleteError) {
      console.warn('⚠️  Failed to delete existing chunks:', deleteError);
    }

    let insertedCount = 0;
    for (let i = 0; i < chunks.length; i += this.batchSize) {
      const batch = chunks.slice(i, i + this.batchSize);
      console.log(\`  Inserting batch \${Math.floor(i/this.batchSize) + 1}/\${Math.ceil(chunks.length/this.batchSize)}\`);
      
      try {
        const { error } = await this.supabase.from('repo_chunks').insert(batch);
        if (error) {
          console.error('❌ Failed to insert batch:', error.message);
        } else {
          insertedCount += batch.length;
        }
      } catch (batchError) {
        console.error('❌ Batch insert exception:', batchError.message);
      }
    }
    
    console.log(\`✅ Inserted \${insertedCount} chunks successfully\`);
  }

  shouldIndexFile(filePath) {
    const hasValidExtension = /\.(c|cpp|js|jsx|ts|tsx|py|java|go|rs|html|css|json|md)$/.test(filePath);
    const isExcluded = this.skipDirs.some(exclude => filePath.includes(exclude));
    return hasValidExtension && !isExcluded;
  }

  async walkDirectory(dir) {
    const files = [];
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
          files.push(...await this.walkDirectory(fullPath));
        } else if (entry.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(\`⚠️  Cannot read directory \${dir}:\`, error.message);
    }
    return files;
  }

  shouldSkipDirectory(name) {
    return this.skipDirs.includes(name) || name.startsWith('.');
  }

  getFileType(filePath) {
    const ext = extname(filePath);
    const typeMap = {
      '.js': 'javascript', '.ts': 'typescript', '.jsx': 'react', '.tsx': 'react-typescript',
      '.html': 'html', '.css': 'css', '.md': 'markdown', '.json': 'json', '.py': 'python',
      '.java': 'java', '.cpp': 'cpp', '.c': 'c', '.go': 'go', '.rs': 'rust'
    };
    return typeMap[ext] || 'text';
  }

  async generateFileSummary(content, filePath) {
    try {
      const prompt = \`Summarize this \${this.getFileType(filePath)} file in one concise line (max 100 chars):\\n\\n\${content.substring(0, 1000)}\`;
      
      // Use the correct Google AI API structure with gemini-2.0-flash
      const response = await this.genAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: prompt
      });
      
      return response.text?.trim() || \`\${this.getFileType(filePath)} file\`;
    } catch (error) {
      console.warn(\`⚠️  Failed to generate summary for \${filePath}:\`, error.message);
      return \`\${this.getFileType(filePath)} file\`;
    }
  }

  async generateRepoSummary(files) {
    try {
      const fileTypes = files.reduce((acc, file) => {
        acc[file.type] = (acc[file.type] || 0) + 1;
        return acc;
      }, {});
      const typesSummary = Object.entries(fileTypes).map(([type, count]) => \`\${count} \${type}\`).join(', ');
      return \`Repository with \${files.length} files: \${typesSummary}\`;
    } catch (error) {
      console.warn(\`⚠️  Failed to generate repo summary:\`, error.message);
      return \`Repository with \${files.length} files\`;
    }
  }

  async commitRepoMap(repoMap) {
    console.log('📝 Writing repository map...');
    try {
      // Determine the correct path for the ai directory
      const aiDir = process.cwd().endsWith('/scripts') ? '../ai' : 'ai';
      const repoMapPath = process.cwd().endsWith('/scripts') ? '../ai/repo-map.json' : 'ai/repo-map.json';
      
      await fs.mkdir(aiDir, { recursive: true });
      console.log(\`📊 Repository map contains \${repoMap.files.length} files\`);
      console.log(\`📊 Repository map summary: \${repoMap.summary}\`);
      
      await fs.writeFile(repoMapPath, JSON.stringify(repoMap, null, 2));
      console.log(\`✅ Repository map written to \${repoMapPath}\`);
      
      // Verify the file was written
      const stats = await fs.stat(repoMapPath);
      console.log(\`📁 Repository map file size: \${stats.size} bytes\`);
      
      const { error } = await this.supabase
        .from('user_projects')
        .update({
          repo_indexed_at: new Date().toISOString(),
          repo_map_path: 'ai/repo-map.json'
        })
        .eq('id', this.projectId);
      
      if (error) {
        console.warn('⚠️  Failed to update project index status:', error);
      } else {
        console.log('✅ Updated project index status in database');
      }
    } catch (error) {
      console.error('❌ Failed to write repository map:', error);
      console.error('❌ Error details:', error.message);
      throw error;
    }
  }
}

// ES module equivalent of require.main === module
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  const indexer = new RepositoryIndexer();
  indexer.run().catch(console.error);
}

export { RepositoryIndexer };
export default RepositoryIndexer;
`;
}

/**
 * Generates a repository description by analyzing the website content and user request
 * @param {string} userRequest - Original user request/prompt
 * @param {string} htmlContent - Generated website HTML content
 * @param {string} projectName - Project name
 * @param {GoogleGenAI} genAI - Google AI client
 * @returns {Promise<string>} - Repository description under 300 characters
 */
async function generateRepositoryDescription(userRequest: string, htmlContent: string, projectName: string, genAI: GoogleGenAI): Promise<string> {
    console.log(`=== GENERATING REPOSITORY DESCRIPTION WITH GEMINI-2.0-FLASH ===`);
    
    try {
        // Extract key information from HTML content for context
        const htmlPreview = htmlContent.substring(0, 1000); // First 1000 chars for context

        const prompt = `Analyze this website project and create a professional GitHub repository description (MAX 280 characters):

Project Name: ${projectName}
User Request: "${userRequest}"
Website HTML Preview: ${htmlPreview}

Create a description that:
- Describes what the website IS (not what the user requested)
- Focuses on the website's purpose and functionality
- Is professional and concise
- Avoids personal details from the user request
- Highlights the main features/content
- Is under 280 characters

Examples of good descriptions:
- "Modern e-commerce website for handmade jewelry with shopping cart and payment integration"
- "Educational platform for children's learning activities with interactive games and printables"
- "Professional portfolio website showcasing web development projects and skills"

Return only the repository description, no quotes or extra text.`;

        console.log(`Sending prompt to Gemini-2.0-flash...`);
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: prompt,
            config: {
                temperature: 0.7,
                topP: 0.8,
                maxOutputTokens: 200
            }
        });
        
        // Extract text from the actual response structure
        let repositoryDescription = '';
        if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
            repositoryDescription = result.candidates[0].content.parts[0].text.trim();
        } else if (result.text) {
            repositoryDescription = result.text.trim();
        } else {
            throw new Error('No text content found in AI response');
        }
        
        // Validate length and truncate if necessary
        if (repositoryDescription.length > 280) {
            console.log(`⚠️  AI description too long (${repositoryDescription.length} chars), truncating...`);
            return repositoryDescription.substring(0, 277) + '...';
        }
        
        console.log(`✅ Generated repository description: "${repositoryDescription}"`);
        console.log(`Length: ${repositoryDescription.length} characters`);
        
        return repositoryDescription;
        
    } catch (error) {
        console.error('Error generating repository description:', error);
        throw error;
    }
}

/**
 * Generates a commit message based on user intent
 * @param {string} userMessage - Original user message
 * @param {boolean} isEdit - Whether this is an edit or new creation
 * @returns {string} - Formatted commit message
 */
export function generateCommitMessage(userMessage: string, isEdit: boolean = false): string {
    if (isEdit) {
        // For edits, create more descriptive commit messages
        const editKeywords: Record<string, string> = {
            'color': 'style',
            'background': 'style',
            'font': 'style',
            'size': 'style',
            'add': 'feature',
            'remove': 'content',
            'delete': 'content',
            'change': 'update',
            'modify': 'update',
            'fix': 'bugfix',
            'header': 'layout',
            'footer': 'layout',
            'navigation': 'layout',
            'menu': 'layout'
        };
        
        let category = 'update';
        for (const [keyword, cat] of Object.entries(editKeywords)) {
            if (userMessage.toLowerCase().includes(keyword)) {
                category = cat;
                break;
            }
        }
        
        // Truncate message if too long, but keep it descriptive
        const maxLength = 60;
        let message = userMessage.length > maxLength
            ? userMessage.substring(0, maxLength) + '...'
            : userMessage;
        
        return `${category}: ${message}`;
    } else {
        // For initial creation
        return `feat: Initial website creation - ${userMessage.substring(0, 40)}${userMessage.length > 40 ? '...' : ''}`;
    }
}

/**
 * Updates an existing file in the repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} filePath - Path to the file
 * @param {string} content - New file content
 * @param {string} message - Commit message
 * @param {string} sha - Current file SHA
 * @param {string} token - GitHub token
 */
async function updateFile(repoName: string, filePath: string, content: string, message: string, sha: string, token: string): Promise<any> {
    console.log(`Updating file: ${filePath} in ${repoName}`);
    console.log(`Using SHA: ${sha}`);
    console.log(`Commit message: ${message}`);
    
    const response = await fetch(`https://api.github.com/repos/${repoName}/contents/${filePath}`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            content: Buffer.from(content).toString('base64'),
            sha: sha
        })
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        console.error(`GitHub API error for ${filePath}:`, {
            status: response.status,
            statusText: response.statusText,
            error: errorData
        });
        throw new Error(`Failed to update file ${filePath}: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }
    
    const result = await response.json();
    console.log(`Successfully updated file: ${filePath}`);
    console.log(`New commit SHA: ${result.commit.sha}`);
    return result;
}

/**
 * Deletes a file from the repository
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} filePath - Path to the file to delete
 * @param {string} message - Commit message
 * @param {string} sha - Current file SHA
 * @param {string} token - GitHub token
 */
async function deleteFile(repoName: string, filePath: string, message: string, sha: string, token: string): Promise<any> {
    console.log(`Deleting file: ${filePath} from ${repoName}`);
    console.log(`Using SHA: ${sha}`);
    console.log(`Commit message: ${message}`);
    
    const response = await fetch(`https://api.github.com/repos/${repoName}/contents/${filePath}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            sha: sha
        })
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        console.error(`GitHub API error for ${filePath}:`, {
            status: response.status,
            statusText: response.statusText,
            error: errorData
        });
        throw new Error(`Failed to delete file ${filePath}: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }
    
    const result = await response.json();
    console.log(`Successfully deleted file: ${filePath}`);
    console.log(`Delete commit SHA: ${result.commit.sha}`);
    return result;
}

/**
 * Sets up repository secrets for the indexing workflow
 * @param {string} repoName - Full repository name (org/repo)
 * @param {string} token - GitHub token
 */
async function setupRepositorySecrets(repoName: string, token: string): Promise<void> {
    console.log(`Setting up repository secrets for: ${repoName}`);
    
    const secrets = {
        GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
        SUPABASE_URL: process.env.SUPABASE_URL,
        SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY
    };
    
    // Get repository public key for encryption
    const publicKeyResponse = await fetch(`https://api.github.com/repos/${repoName}/actions/secrets/public-key`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json'
        }
    });
    
    if (!publicKeyResponse.ok) {
        console.warn(`⚠️  Failed to get repository public key: ${publicKeyResponse.status}`);
        return;
    }
    
    const publicKeyData = await publicKeyResponse.json();
    const publicKey = publicKeyData.key;
    const keyId = publicKeyData.key_id;
    
    // Set up each secret
    for (const [secretName, secretValue] of Object.entries(secrets)) {
        if (!secretValue) {
            console.warn(`⚠️  Skipping ${secretName} - not set in environment`);
            continue;
        }
        
        try {
            console.log(`🔐 Setting up repository secret: ${secretName}`);
            
            // Encrypt the secret value using the repository's public key
            const encryptedValue = await encryptSecret(secretValue, publicKey);
            
            const secretResponse = await fetch(`https://api.github.com/repos/${repoName}/actions/secrets/${secretName}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/vnd.github.v3+json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    encrypted_value: encryptedValue,
                    key_id: keyId
                })
            });
            
            if (secretResponse.ok) {
                console.log(`✅ Successfully set repository secret: ${secretName}`);
            } else {
                const errorData = await secretResponse.json().catch(() => ({}));
                console.error(`❌ Failed to set ${secretName}: ${secretResponse.status}`);
                console.error(`❌ Error details:`, errorData);
                
                // Log specific guidance for different error types
                if (secretResponse.status === 422) {
                    console.error(`❌ 422 error: Invalid encrypted value or key_id`);
                } else if (secretResponse.status === 404) {
                    console.error(`❌ 404 error: Repository not found or insufficient permissions`);
                } else if (secretResponse.status === 403) {
                    console.error(`❌ 403 error: Insufficient permissions to set secrets`);
                }
            }
            
        } catch (error) {
            console.error(`❌ Error setting ${secretName}:`, error.message);
            console.error(`❌ This may indicate an encryption or network issue`);
        }
    }
}

/**
 * Encrypts a secret value using the repository's public key
 * @param {string} secretValue - The secret value to encrypt
 * @param {string} publicKey - The repository's public key
 * @returns {Promise<string>} - The encrypted value
 */
async function encryptSecret(secretValue: string, publicKey: string): Promise<string> {
    // GitHub requires libsodium sealed box encryption
    console.log('🔐 Encrypting secret using libsodium sealed box...');
    
    try {
        // Import libsodium-wrappers with correct structure
        const sodiumModule = await import('libsodium-wrappers');
        const sodium = sodiumModule.default;
        await sodium.ready;
        
        console.log('✅ Libsodium initialized successfully');
        
        // Convert the public key from base64 to Uint8Array
        const publicKeyBytes = sodium.from_base64(publicKey, sodium.base64_variants.ORIGINAL);
        console.log(`📏 Public key length: ${publicKeyBytes.length} bytes`);
        
        // Convert secret value to Uint8Array
        const secretBytes = sodium.from_string(secretValue);
        console.log(`📏 Secret length: ${secretBytes.length} bytes`);
        
        // Encrypt using sealed box (anonymous encryption)
        const encryptedBytes = sodium.crypto_box_seal(secretBytes, publicKeyBytes);
        console.log(`📏 Encrypted data length: ${encryptedBytes.length} bytes`);
        
        // Convert encrypted bytes back to base64
        const encryptedBase64 = sodium.to_base64(encryptedBytes, sodium.base64_variants.ORIGINAL);
        console.log(`✅ Secret encrypted successfully (${encryptedBase64.length} chars)`);
        
        return encryptedBase64;
        
    } catch (error) {
        console.error('❌ Encryption failed:', error);
        console.error('Error details:', {
            name: (error as Error).name,
            message: (error as Error).message,
            stack: (error as Error).stack
        });
        throw new Error(`Failed to encrypt secret: ${(error as Error).message}`);
    }
}