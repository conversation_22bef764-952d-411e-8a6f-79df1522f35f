import { GoogleGenAI } from '@google/genai';
import { createClient } from '@supabase/supabase-js';
import { EMBEDDING_CONFIG, validateEmbeddingDimensions, normalizeEmbeddingDimensions } from './config.js';

interface RepoChunk {
  id: string;
  project_id: number;
  file_path: string;
  chunk_id: number;
  content: string;
  embedding: number[];
  similarity?: number;
}

interface RetrievalContext {
  query: string;
  projectId: number;
  maxChunks?: number;
  similarityThreshold?: number;
  fileTypes?: string[];
}

interface RetrievalResult {
  chunks: RepoChunk[];
  totalFound: number;
  queryEmbedding: number[];
  executionTime: number;
}

export class VectorRetrieval {
  private genAI: GoogleGenAI;
  private supabase: any;

  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY! });
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    );
  }

  /**
   * Find relevant code chunks using vector similarity search
   */
  async findRelevantChunks(context: RetrievalContext): Promise<RetrievalResult> {
    const startTime = Date.now();
    
    try {
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(context.query);
      
      // Perform similarity search
      const chunks = await this.performSimilaritySearch({
        embedding: queryEmbedding,
        projectId: context.projectId,
        maxChunks: context.maxChunks || 10,
        similarityThreshold: context.similarityThreshold || EMBEDDING_CONFIG.DEFAULT_SIMILARITY_THRESHOLD,
        fileTypes: context.fileTypes
      });

      const executionTime = Date.now() - startTime;

      return {
        chunks,
        totalFound: chunks.length,
        queryEmbedding,
        executionTime
      };

    } catch (error) {
      console.error('Vector retrieval failed:', error);
      throw new Error(`Vector retrieval failed: ${error.message}`);
    }
  }

  /**
   * Find chunks related to specific files or patterns
   */
  async findRelatedCode(
    projectId: number,
    filePaths: string[],
    query?: string
  ): Promise<RepoChunk[]> {
    try {
      let searchQuery = this.supabase
        .from('repo_chunks')
        .select('*')
        .eq('project_id', projectId);

      // Filter by file paths if provided
      if (filePaths.length > 0) {
        searchQuery = searchQuery.in('file_path', filePaths);
      }

      const { data: chunks, error } = await searchQuery.limit(50);

      if (error) {
        throw error;
      }

      // If query provided, rank by similarity
      if (query && chunks.length > 0) {
        const queryEmbedding = await this.generateEmbedding(query);
        
        return chunks
          .map(chunk => ({
            ...chunk,
            similarity: this.cosineSimilarity(queryEmbedding, chunk.embedding)
          }))
          .sort((a, b) => (b.similarity || 0) - (a.similarity || 0));
      }

      return chunks || [];

    } catch (error) {
      console.error('Related code search failed:', error);
      return [];
    }
  }

  /**
   * Get context for editing a specific file
   */
  async getEditingContext(
    projectId: number,
    targetFile: string,
    editDescription: string
  ): Promise<{
    targetChunks: RepoChunk[];
    relatedChunks: RepoChunk[];
    suggestions: string[];
  }> {
    try {
      // Get chunks from the target file
      const { data: targetChunks, error: targetError } = await this.supabase
        .from('repo_chunks')
        .select('*')
        .eq('project_id', projectId)
        .eq('file_path', targetFile)
        .order('chunk_id');

      if (targetError) {
        throw targetError;
      }

      // Find related chunks from other files
      const relatedResult = await this.findRelevantChunks({
        query: `${editDescription} ${targetFile}`,
        projectId,
        maxChunks: 8,
        similarityThreshold: EMBEDDING_CONFIG.FALLBACK_SIMILARITY_THRESHOLD
      });

      // Filter out chunks from the target file
      const relatedChunks = relatedResult.chunks.filter(
        chunk => chunk.file_path !== targetFile
      );

      // Generate editing suggestions
      const suggestions = await this.generateEditingSuggestions(
        targetChunks || [],
        relatedChunks,
        editDescription
      );

      return {
        targetChunks: targetChunks || [],
        relatedChunks,
        suggestions
      };

    } catch (error) {
      console.error('Failed to get editing context:', error);
      return {
        targetChunks: [],
        relatedChunks: [],
        suggestions: []
      };
    }
  }

  /**
   * Perform vector similarity search using pgvector
   */
  private async performSimilaritySearch({
    embedding,
    projectId,
    maxChunks,
    similarityThreshold,
    fileTypes
  }: {
    embedding: number[];
    projectId: number;
    maxChunks: number;
    similarityThreshold: number;
    fileTypes?: string[];
  }): Promise<RepoChunk[]> {
    
    // DEBUG: Log query embedding dimensions to validate our diagnosis
    console.log(`🔍 DEBUG: Query embedding dimensions: ${embedding.length} (for similarity search)`);
    
    // Build the similarity search query
    let query = this.supabase.rpc('match_repo_chunks', {
      query_embedding: embedding,
      project_id: projectId,
      match_threshold: similarityThreshold,
      match_count: maxChunks
    });

    const { data: chunks, error } = await query;

    if (error) {
      console.error(`🔍 DEBUG: Database error - likely dimension mismatch. Query embedding: ${embedding.length} dimensions`);
      throw error;
    }
    
    // DEBUG: Log first chunk embedding dimensions if available
    if (chunks && chunks.length > 0 && chunks[0].embedding) {
      console.log(`🔍 DEBUG: Database chunk embedding dimensions: ${chunks[0].embedding.length}`);
    }

    // Filter by file types if specified
    if (fileTypes && fileTypes.length > 0) {
      return chunks.filter((chunk: RepoChunk) => {
        const fileExt = chunk.file_path.split('.').pop()?.toLowerCase();
        return fileTypes.some(type => 
          type.toLowerCase().includes(fileExt || '') ||
          (fileExt && type.toLowerCase().includes(fileExt))
        );
      });
    }

    return chunks || [];
  }

  /**
   * Generate embedding for text using Gemini
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      const result = await this.genAI.models.embedContent({
        model: EMBEDDING_CONFIG.MODEL,
        contents: text
      });
      const embedding = result.embeddings?.[0]?.values || [];
      
      // DEBUG: Log embedding dimensions to validate our diagnosis
      console.log(`🔍 DEBUG: Generated embedding dimensions: ${embedding.length} (model: ${EMBEDDING_CONFIG.MODEL})`);
      
      // Validate and normalize embedding dimensions
      try {
        validateEmbeddingDimensions(embedding, 'vector-retrieval.generateEmbedding');
      } catch (dimensionError) {
        console.warn(`🔧 DEBUG: Normalizing embedding dimensions in vector-retrieval.generateEmbedding`);
        return normalizeEmbeddingDimensions(embedding, 'vector-retrieval.generateEmbedding');
      }
      
      return embedding;
    } catch (error) {
      console.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      console.error(`🔍 DEBUG: Cosine similarity dimension mismatch - Vector A: ${a.length}, Vector B: ${b.length}`);
      throw new Error(`Vectors must have the same length: ${a.length} vs ${b.length}`);
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  }

  /**
   * Generate editing suggestions based on context
   */
  private async generateEditingSuggestions(
    targetChunks: RepoChunk[],
    relatedChunks: RepoChunk[],
    editDescription: string
  ): Promise<string[]> {
    try {
      const contextSummary = this.buildContextSummary(targetChunks, relatedChunks);
      
      const prompt = `Based on this code context and edit request, provide 3-5 specific suggestions:

Edit Request: ${editDescription}

Current File Context:
${targetChunks.map(chunk => `${chunk.file_path}:${chunk.chunk_id}\n${chunk.content.substring(0, 200)}...`).join('\n\n')}

Related Code Context:
${relatedChunks.slice(0, 3).map(chunk => `${chunk.file_path}\n${chunk.content.substring(0, 150)}...`).join('\n\n')}

Provide specific, actionable suggestions for implementing this edit:`;

      const result = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: prompt,
        config: {
          temperature: 0.3,
          maxOutputTokens: 500
        }
      });
      
      const suggestions = (result.text || '')
        .split('\n')
        .filter(line => line.trim().length > 0)
        .slice(0, 5);

      return suggestions;

    } catch (error) {
      console.error('Failed to generate suggestions:', error);
      return [
        'Review the target file structure',
        'Check related files for patterns',
        'Consider impact on other components'
      ];
    }
  }

  /**
   * Build a summary of the code context
   */
  private buildContextSummary(targetChunks: RepoChunk[], relatedChunks: RepoChunk[]): string {
    const summary = {
      targetFile: targetChunks[0]?.file_path || 'unknown',
      targetChunkCount: targetChunks.length,
      relatedFiles: [...new Set(relatedChunks.map(c => c.file_path))],
      totalContext: targetChunks.length + relatedChunks.length
    };

    return `Target: ${summary.targetFile} (${summary.targetChunkCount} chunks), Related: ${summary.relatedFiles.length} files, Total context: ${summary.totalContext} chunks`;
  }

  /**
   * Check if repository is indexed and ready for vector search
   */
  async isRepositoryIndexed(projectId: number): Promise<{
    indexed: boolean;
    lastIndexed?: string;
    chunkCount?: number;
  }> {
    try {
      // Check if project has chunks
      const { data: chunks, error: chunksError } = await this.supabase
        .from('repo_chunks')
        .select('id')
        .eq('project_id', projectId)
        .limit(1);

      if (chunksError) {
        throw chunksError;
      }

      // Get indexing info from user_projects
      const { data: project, error: projectError } = await this.supabase
        .from('user_projects')
        .select('repo_indexed_at, repo_map_path')
        .eq('id', projectId)
        .single();

      if (projectError && projectError.code !== 'PGRST116') {
        throw projectError;
      }

      // Get chunk count
      const { count, error: countError } = await this.supabase
        .from('repo_chunks')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId);

      if (countError) {
        console.warn('Failed to get chunk count:', countError);
      }

      return {
        indexed: chunks && chunks.length > 0,
        lastIndexed: project?.repo_indexed_at,
        chunkCount: count || 0
      };

    } catch (error) {
      console.error('Failed to check repository index status:', error);
      return { indexed: false };
    }
  }
}

export default VectorRetrieval;