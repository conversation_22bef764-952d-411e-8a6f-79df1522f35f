/**
 * Website generation functionality using Google Gemini AI
 */
import { GoogleGenAI } from "@google/genai";
import { UserProject } from '../types/index.js';

/**
 * Generates website code using Google Gemini.
 * @param {string} prompt - The user's prompt for the website.
 * @param {GoogleGenAI} genAI - The initialized Google AI client.
 * @param {UserProject | null} existingProject - Existing project data for edits (optional).
 * @returns {Promise<string>} - The generated HTML code.
 */
export async function generateWebsite(prompt: string, genAI: GoogleGenAI, existingProject: UserProject | null = null): Promise<string> {
    // Set a timeout for the Gemini API call (5 minutes)
    const TIMEOUT_MS = 4.5 * 60 * 1000; // 4.5 minutes (leaving 30s buffer for the rest of the function)
    
    console.log("=== WEBSITE GENERATION DIAGNOSTICS ===");
    console.log(`Starting website generation with prompt: "${prompt ? prompt.substring(0, 100) : 'undefined'}..."`);
    
    // Note: The new SDK doesn't use separate model creation
    // Configuration is passed directly to generateContent
    
    console.log("=== MODEL CONFIGURATION ===");
    console.log(`Model: gemini-2.5-pro`);
    console.log(`Temperature: 1.0`);
    console.log(`TopP: 0.8`);
    console.log(`MaxOutputTokens: 131072`);
    console.log(`Google API Key present: ${!!process.env.GOOGLE_API_KEY}`);
    console.log(`Google API Key length: ${process.env.GOOGLE_API_KEY ? process.env.GOOGLE_API_KEY.length : 0}`);
    
    // Create different prompts based on whether this is a new site or an edit
    let fullPrompt: string;
    
    if (existingProject) {
        console.log(`Editing existing project: ${existingProject.project_name}`);
        console.log(`Original description: ${existingProject.description}`);
        
        fullPrompt = `You are editing an existing website. Here is the current HTML code:

${existingProject.html_content}

The user wants to make the following changes: "${prompt}"

Please modify the existing HTML code to implement these changes. Maintain the overall structure and design while incorporating the requested modifications. The result should be a complete, functional single-file HTML website with inline CSS and JavaScript. Return only the modified HTML code without any explanation.

Changes requested: "${prompt}"`;
    } else {
        console.log("Creating new website from scratch");
        fullPrompt = `Create a complete, beautiful, and fully functional single-file HTML website based on the following description. The HTML file should include inline CSS and JavaScript for styling and interactivity. The design should be modern and responsive. Do not include any explanation, just return the raw HTML code. Description: "${prompt}"`;
    }
    
    console.log("=== PROMPT ANALYSIS ===");
    console.log(`Full prompt length: ${fullPrompt.length} characters`);
    console.log(`Prompt: ${fullPrompt}`);

    console.log("Sending enhanced prompt to Gemini API");
    
    try {
        // Create a promise that rejects after the timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
                reject(new Error('Gemini API call timed out after ' + (TIMEOUT_MS/1000) + ' seconds'));
            }, TIMEOUT_MS);
        });
        
        console.log("=== MAKING GEMINI API CALL ===");
        const startTime = Date.now();
        
        // Use different models based on operation type
        const modelToUse = existingProject ? "gemini-2.5-pro" : "gemini-2.5-pro";
        const temperature = existingProject ? 0.7 : 1.0; // Lower temperature for edits for more consistency
        
        console.log(`Using model: ${modelToUse} with temperature: ${temperature}`);
        
        // Race the API call against the timeout
        const result = await Promise.race([
            genAI.models.generateContent({
                model: modelToUse,
                contents: fullPrompt,
                config: {
                    temperature: temperature,
                    topP: 0.8,
                    maxOutputTokens: 131072
                }
            }),
            timeoutPromise
        ]);
        
        const endTime = Date.now();
        console.log(`Gemini API call completed in ${endTime - startTime}ms`);
        
        let text = result.text || '';
        
        console.log(`=== GEMINI RESPONSE ANALYSIS ===`);
        console.log(`Response length: ${text.length} characters`);
        console.log(`Response starts with: "${text.substring(0, 200)}..."`);
        console.log(`Response ends with: "...${text.substring(text.length - 200)}"`);
        
        // Check if response contains safety warnings or refusals
        const safetyIndicators = [
            "I can't", "I cannot", "I'm not able", "I'm unable",
            "I don't", "I won't", "I shouldn't", "I can't help",
            "safety", "harmful", "inappropriate", "policy"
        ];
        
        const hasSafetyIssue = safetyIndicators.some(indicator =>
            text.toLowerCase().includes(indicator.toLowerCase())
        );
        
        if (hasSafetyIssue) {
            console.log("WARNING: Gemini response may contain safety refusal or warning");
            console.log(`Full response: ${text}`);
        }

        // Clean up the response to ensure it's just raw HTML
        text = cleanupGeneratedHTML(text);
        
        console.log(`After cleanup, response length: ${text.length} characters`);
        
        // Validate and potentially fix the HTML
        text = validateAndFixHTML(text, prompt, existingProject);

        return text;
    } catch (error) {
        console.error("Error generating website:", error);
        
        // If it's a timeout, return a simple but functional website
        if (error instanceof Error && error.message && error.message.includes('timed out')) {
            return generateFallbackHTML(prompt);
        }
        
        // For other errors, rethrow
        throw error;
    }
}

/**
 * Cleans up the generated HTML response from Gemini
 * @param {string} text - Raw response from Gemini
 * @returns {string} - Cleaned HTML
 */
function cleanupGeneratedHTML(text: string): string {
    if (text.startsWith("```html")) {
        console.log("Detected code block format, cleaning up response");
        text = text.substring(7, text.length - 3).trim();
    } else if (text.startsWith("```")) {
        console.log("Detected generic code block format, cleaning up response");
        const firstNewline = text.indexOf('\n');
        const lastBackticks = text.lastIndexOf('```');
        if (firstNewline > 0 && lastBackticks > firstNewline) {
            text = text.substring(firstNewline + 1, lastBackticks).trim();
        }
    }
    return text;
}

/**
 * Validates and potentially fixes HTML structure
 * @param {string} text - HTML content to validate
 * @param {string} prompt - Original user prompt for fallback
 * @param {UserProject | null} existingProject - Existing project data (for edits)
 * @returns {string} - Validated/fixed HTML
 */
function validateAndFixHTML(text: string, prompt: string, existingProject: UserProject | null = null): string {
    // Check if the HTML appears to be truncated
    const hasClosingHtml = text.includes("</html>");
    const hasClosingBody = text.includes("</body>");
    const endsAbruptly = !text.trim().endsWith(">") && !text.trim().endsWith("</html>");
    
    console.log("=== TRUNCATION CHECK ===");
    console.log(`Has closing </html> tag: ${hasClosingHtml}`);
    console.log(`Has closing </body> tag: ${hasClosingBody}`);
    console.log(`Ends abruptly (not with >): ${endsAbruptly}`);
    
    if (!hasClosingHtml || !hasClosingBody || endsAbruptly) {
        console.log("WARNING: HTML appears to be truncated! This indicates the token limit was reached.");
        console.log(`Last 500 characters: "${text.substring(text.length - 500)}"`);
        
        // If editing and HTML is truncated, try to use the original as fallback
        if (existingProject && existingProject.html_content) {
            console.log("Using original HTML as fallback due to truncation");
            return existingProject.html_content;
        }
    }
    
    // Validate the HTML contains essential elements
    const hasHtmlTag = text.includes("<html");
    const hasBodyTag = text.includes("<body");
    const hasStyleTag = text.includes("<style") || text.includes("style=");
    const hasScriptTag = text.includes("<script") || text.includes("onclick=");
    
    console.log("=== HTML VALIDATION CHECK ===");
    console.log(`- Has HTML tag: ${hasHtmlTag}`);
    console.log(`- Has BODY tag: ${hasBodyTag}`);
    console.log(`- Has STYLE: ${hasStyleTag}`);
    console.log(`- Has SCRIPT: ${hasScriptTag}`);
    
    // Check for missing elements and add them if needed
    if (!hasHtmlTag || !hasBodyTag) {
        console.log("WARNING: Generated content may not be valid HTML!");
        // Add basic HTML structure if missing
        if (!text.includes("<!DOCTYPE html>")) {
            text = `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Generated Website</title>\n</head>\n<body>\n${text}\n</body>\n</html>`;
            console.log("Added HTML structure wrapper to content");
        }
    }
    
    // Log the issue but don't automatically fix it - let's see what's actually happening
    if (!hasScriptTag) {
        console.log("ISSUE DETECTED: No JavaScript found in generated content");
        console.log("This may indicate a problem with Gemini's response or prompt interpretation");
    }

    return text;
}

/**
 * Generates a fallback HTML page when the main generation fails
 * @param {string} prompt - Original user prompt
 * @returns {string} - Fallback HTML
 */
function generateFallbackHTML(prompt: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${prompt.substring(0, 50)}...</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; }
        p { margin-bottom: 20px; }
        .note { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Website Coming Soon</h1>
        <div class="note">
            <p>We're working on creating your website based on: "${prompt}"</p>
            <p>The AI took longer than expected to generate your custom website. Please try again with a simpler description.</p>
        </div>
        <p>This is a temporary page. Your full website will be available soon!</p>
    </div>
</body>
</html>`;
}