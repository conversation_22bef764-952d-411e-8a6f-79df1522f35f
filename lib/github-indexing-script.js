/**
 * <PERSON><PERSON>ly escaped indexing script content for GitHub repository creation
 */
export function generateIndexingScriptContent() {
    return `/******************************************************************************************
 * scripts/indexRepo.mjs
 *
 * Modern, LlamaIndex-based Repository Indexer
 * - Uses llamaindex for document parsing and chunking.
 * - Embeds each chunk with Google Gemini.
 * - Upserts into Supabase \\`repo_chunks\\`.
 *
 * Prerequisites (installed in the Action or locally):
 *   npm i @google/genai @supabase/supabase-js llamaindex @llamaindex/google pg
 ******************************************************************************************/

console.log('🔍 Starting module imports...');

import { GoogleGenAI } from '@google/genai';
import { createClient } from '@supabase/supabase-js';
import { Document, Settings, SimpleNodeParser } from 'llamaindex';
import { GeminiEmbedding } from '@llamaindex/google';
import { promises as fs } from 'fs';
import { relative, join, extname } from 'path';

console.log('✅ All modules imported successfully');

class RepositoryIndexer {
  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    this.embedModel = new GeminiEmbedding({
      model: 'models/text-embedding-004', // Recommended model for text embeddings
    });
    this.supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);
    
    // Configure LlamaIndex settings
    Settings.embedModel = this.embedModel;
    Settings.chunkSize = 1024;
    Settings.chunkOverlap = 20;
    
    this.nodeParser = new SimpleNodeParser({
        chunkSize: Settings.chunkSize,
        chunkOverlap: Settings.chunkOverlap,
    });

    this.repoId = process.env.GITHUB_REPOSITORY; // owner/repo format
    this.projectId = null; // Will be resolved from user_projects table
    this.vectorDim = 768; // Must match DB schema for 'models/text-embedding-004'
    this.batchSize = 25; // Supabase insert batch
    this.skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', '.vercel', 'coverage'];
  }

  async run() {
    console.log(\\`🚀 Starting indexing for repository: \\${this.repoId}\\`);
    console.log(\\`🔧 Using embedding model: \\${this.embedModel.model || 'models/text-embedding-004'}\\`);
    console.log(\\`🔧 Vector dimensions: \\${this.vectorDim}\\`);
    
    try {
      console.log('📋 Step 1: Resolving project ID...');
      await this.resolveProjectId();
      
      console.log('📋 Step 2: Building repository map...');
      // Determine the correct root path based on current working directory
      const rootPath = process.cwd().endsWith('/scripts') ? '..' : '.';
      console.log(\\`📂 Using root path: \\${rootPath}\\`);
      const repoMap = await this.buildRepoMap(rootPath);
      
      console.log('📋 Step 3: Chunking and embedding content...');
      const chunks = await this.chunkAndEmbed(repoMap);
      
      console.log('📋 Step 4: Upserting chunks to database...');
      await this.upsertChunks(chunks);
      
      console.log('📋 Step 5: Committing repository map...');
      await this.commitRepoMap(repoMap);
      
      console.log(\\`✅ Indexing completed successfully: \\${chunks.length} chunks processed\\`);
      
    } catch (error) {
      console.error('❌ Indexing failed:', error);
      console.error('❌ Error message:', error.message);
      console.error('❌ Stack trace:', error.stack);
      
      try {
        if (this.projectId) {
          await this.supabase
            .from('user_projects')
            .update({ repo_indexed_at: null, repo_map_path: null })
            .eq('id', this.projectId);
          console.log('⚠️  Updated project status to indicate indexing failure');
        }
      } catch (updateError) {
        console.warn('Failed to update project status:', updateError.message);
      }
      
      process.exit(1);
    }
  }

  async resolveProjectId() {
    console.log(\\`🔍 Resolving project ID for repository: \\${this.repoId}\\`);
    
    const { data: project, error } = await this.supabase
      .from('user_projects')
      .select('id')
      .eq('github_repo_name', this.repoId)
      .single();
    
    if (error) {
      throw new Error(\\`Failed to find project for repository \\${this.repoId}: \\${error.message}\\`);
    }
    
    this.projectId = project.id;
    console.log(\\`✅ Resolved project ID: \\${this.projectId}\\`);
  }

  async buildRepoMap(rootPath = '.') {
    console.log('📁 Building repository map...');
    
    const repoMap = {
      structure: {},
      files: [],
      summary: '',
      lastIndexed: new Date().toISOString()
    };

    const files = await this.walkDirectory(rootPath);
    
    for (const filePath of files) {
      if (this.shouldIndexFile(filePath)) {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = relative(rootPath, filePath);
        
        console.log(\\`  Processing: \\${relativePath}\\`);
        
        repoMap.files.push({
          path: relativePath,
          type: this.getFileType(filePath),
          size: content.length,
          summary: await this.generateFileSummary(content, relativePath)
        });
      }
    }

    repoMap.summary = await this.generateRepoSummary(repoMap.files);
    console.log(\\`📊 Repository map built: \\${repoMap.files.length} files\\`);
    
    return repoMap;
  }

  async chunkAndEmbed(repoMap) {
    console.log('🔍 Chunking and embedding content with modern llamaindex...');

    // 1. Create Document objects from all files
    const documents = [];
    for (const file of repoMap.files) {
        try {
            const content = await fs.readFile(file.path, 'utf-8');
            documents.push(new Document({ text: content, id_: file.path }));
        } catch (error) {
            console.warn(\\`⚠️  Failed to read file \\${file.path}:\\`, error.message);
        }
    }
    console.log(\\`  Created \\${documents.length} Document objects.\\`);

    // 2. Use the parser to generate nodes (chunks)
    const nodes = this.nodeParser.getNodesFromDocuments(documents);
    console.log(\\`  Generated \\${nodes.length} nodes (chunks) from documents.\\`);

    // 3. Generate embeddings for each node and create the final chunk objects
    const chunks = [];
    let chunkGlobalId = 0;
    const embeddingBatchSize = 50; // Process nodes in batches for the embedding API

    for (let i = 0; i < nodes.length; i += embeddingBatchSize) {
        const batchNodes = nodes.slice(i, i + embeddingBatchSize);
        const batchTexts = batchNodes.map(node => node.getText());
        
        console.log(\\`  Embedding batch \\${Math.floor(i/embeddingBatchSize) + 1}/\\${Math.ceil(nodes.length/embeddingBatchSize)}...\\`);

        try {
            const embeddings = await this.embedModel.getTextEmbeddingsBatch(batchTexts);
            console.log(\\`  ✅ Successfully generated \\${embeddings.length} embeddings\\`);

            for (let j = 0; j < batchNodes.length; j++) {
                const node = batchNodes[j];
                const embedding = embeddings[j];

                if (embedding && embedding.length > 0) {
                    console.log(\\`  📏 Embedding dimension for chunk \\${chunkGlobalId}: \\${embedding.length}\\`);
                }

                chunks.push({
                    project_id: this.projectId,
                    file_path: node.id_.replace(/_part_\\\\d+$/, ''), // Get original file path from node id
                    chunk_id: chunkGlobalId++,
                    content: node.getText(),
                    embedding: embedding ? embedding.slice(0, this.vectorDim) : new Array(this.vectorDim).fill(0),
                });
            }
            // Add small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 200));

        } catch (embeddingError) {
            console.warn(\\`⚠️  Failed to embed a batch:\\`, embeddingError.message);
            console.error(\\`⚠️  Embedding error details:\\`, embeddingError);
        }
    }

    console.log(\\`🧠 Generated \\${chunks.length} embedded chunks\\`);
    return chunks;
  }

  async upsertChunks(chunks) {
    console.log('💾 Upserting chunks to database...');
    
    const { error: deleteError } = await this.supabase
      .from('repo_chunks')
      .delete()
      .eq('project_id', this.projectId);
    
    if (deleteError) {
      console.warn('⚠️  Failed to delete existing chunks:', deleteError);
    }

    let insertedCount = 0;
    for (let i = 0; i < chunks.length; i += this.batchSize) {
      const batch = chunks.slice(i, i + this.batchSize);
      console.log(\\`  Inserting batch \\${Math.floor(i/this.batchSize) + 1}/\\${Math.ceil(chunks.length/this.batchSize)}\\`);
      
      try {
        const { error } = await this.supabase.from('repo_chunks').insert(batch);
        if (error) {
          console.error('❌ Failed to insert batch:', error.message);
        } else {
          insertedCount += batch.length;
        }
      } catch (batchError) {
        console.error('❌ Batch insert exception:', batchError.message);
      }
    }
    
    console.log(\\`✅ Inserted \\${insertedCount} chunks successfully\\`);
  }

  shouldIndexFile(filePath) {
    const hasValidExtension = /\\\\.(c|cpp|js|jsx|ts|tsx|py|java|go|rs|html|css|json|md)$/.test(filePath);
    const isExcluded = this.skipDirs.some(exclude => filePath.includes(exclude));
    return hasValidExtension && !isExcluded;
  }

  async walkDirectory(dir) {
    const files = [];
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
          files.push(...await this.walkDirectory(fullPath));
        } else if (entry.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(\\`⚠️  Cannot read directory \\${dir}:\\`, error.message);
    }
    return files;
  }

  shouldSkipDirectory(name) {
    return this.skipDirs.includes(name) || name.startsWith('.');
  }

  getFileType(filePath) {
    const ext = extname(filePath);
    const typeMap = {
      '.js': 'javascript', '.ts': 'typescript', '.jsx': 'react', '.tsx': 'react-typescript',
      '.html': 'html', '.css': 'css', '.md': 'markdown', '.json': 'json', '.py': 'python',
      '.java': 'java', '.cpp': 'cpp', '.c': 'c', '.go': 'go', '.rs': 'rust'
    };
    return typeMap[ext] || 'text';
  }

  async generateFileSummary(content, filePath) {
    try {
      const prompt = \\`Summarize this \\${this.getFileType(filePath)} file in one concise line (max 100 chars):\\\\n\\\\n\\${content.substring(0, 1000)}\\`;
      
      // Use the correct Google AI API structure with gemini-2.0-flash
      const response = await this.genAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: prompt
      });
      
      return response.text?.trim() || \\`\\${this.getFileType(filePath)} file\\`;
    } catch (error) {
      console.warn(\\`⚠️  Failed to generate summary for \\${filePath}:\\`, error.message);
      return \\`\\${this.getFileType(filePath)} file\\`;
    }
  }

  async generateRepoSummary(files) {
    try {
      const fileTypes = files.reduce((acc, file) => {
        acc[file.type] = (acc[file.type] || 0) + 1;
        return acc;
      }, {});
      const typesSummary = Object.entries(fileTypes).map(([type, count]) => \\`\\${count} \\${type}\\`).join(', ');
      return \\`Repository with \\${files.length} files: \\${typesSummary}\\`;
    } catch (error) {
      console.warn(\\`⚠️  Failed to generate repo summary:\\`, error.message);
      return \\`Repository with \\${files.length} files\\`;
    }
  }

  async commitRepoMap(repoMap) {
    console.log('📝 Writing repository map...');
    try {
      // Determine the correct path for the ai directory
      const aiDir = process.cwd().endsWith('/scripts') ? '../ai' : 'ai';
      const repoMapPath = process.cwd().endsWith('/scripts') ? '../ai/repo-map.json' : 'ai/repo-map.json';
      
      await fs.mkdir(aiDir, { recursive: true });
      console.log(\\`📊 Repository map contains \\${repoMap.files.length} files\\`);
      console.log(\\`📊 Repository map summary: \\${repoMap.summary}\\`);
      
      await fs.writeFile(repoMapPath, JSON.stringify(repoMap, null, 2));
      console.log(\\`✅ Repository map written to \\${repoMapPath}\\`);
      
      // Verify the file was written
      const stats = await fs.stat(repoMapPath);
      console.log(\\`📁 Repository map file size: \\${stats.size} bytes\\`);
      
      const { error } = await this.supabase
        .from('user_projects')
        .update({
          repo_indexed_at: new Date().toISOString(),
          repo_map_path: 'ai/repo-map.json'
        })
        .eq('id', this.projectId);
      
      if (error) {
        console.warn('⚠️  Failed to update project index status:', error);
      } else {
        console.log('✅ Updated project index status in database');
      }
    } catch (error) {
      console.error('❌ Failed to write repository map:', error);
      console.error('❌ Error details:', error.message);
      throw error;
    }
  }
}

// ES module equivalent of require.main === module
if (import.meta.url === \\`file://\\${process.argv[1]}\\`) {
  const indexer = new RepositoryIndexer();
  indexer.run().catch(console.error);
}

export { RepositoryIndexer };
export default RepositoryIndexer;
`;
}