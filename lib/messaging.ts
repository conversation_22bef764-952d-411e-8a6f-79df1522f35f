/**
 * WhatsApp messaging functionality
 */
import twilio from 'twilio';
import { GoogleGenAI } from "@google/genai";
import { StatusMessageData } from '../types/index.js';

/**
 * Send a WhatsApp message using Twilio
 * @param {string} to - Recipient phone number (with whatsapp: prefix)
 * @param {string} body - Message content
 * @param {twilio.Twilio} twilioClient - Initialized Twilio client
 * @returns {Promise<void>}
 */
export async function sendWhatsappMessage(
  to: string, 
  body: string, 
  twilioClient: twilio.Twilio
): Promise<void> {
  try {
    await twilioClient.messages.create({
      from: `whatsapp:${process.env.TWILIO_PHONE_NUMBER}`,
      to: to,
      body: body
    });
    console.log(`WhatsApp message sent to ${to}: "${body.substring(0, 50)}..."`);
  } catch (error) {
    console.error(`Failed to send WhatsApp message to ${to}:`, error);
  }
}

/**
 * Twilio WhatsApp message character limit
 */
const TWILIO_MESSAGE_LIMIT = 1600;

/**
 * Summarizes a user request using Gemini 2.0 Flash
 * @param {string} userRequest - The original user request
 * @param {GoogleGenAI} genAI - Google AI client
 * @returns {Promise<string>} - Summarized request
 */
async function summarizeUserRequest(userRequest: string, genAI: GoogleGenAI): Promise<string> {
  try {
    if (!userRequest || userRequest.length <= 100) {
      return userRequest; // No need to summarize short requests
    }

    const prompt = `Summarize this user request for website creation/editing in 1-2 concise sentences (max 100 characters):

"${userRequest}"

Focus on the main action and key details. Be specific but brief.`;

    const result = await genAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: prompt,
      config: {
        temperature: 0.3,
        topP: 0.8,
        maxOutputTokens: 50
      }
    });

    const summary = result.text?.trim() || userRequest;
    
    // Ensure summary is not longer than original and within reasonable bounds
    if (summary.length < userRequest.length && summary.length <= 100) {
      return summary;
    }
    
    // Fallback: truncate original request
    return userRequest.length > 100 ? userRequest.substring(0, 97) + '...' : userRequest;
    
  } catch (error) {
    console.error('Error summarizing user request:', error);
    // Fallback: truncate original request
    return userRequest.length > 100 ? userRequest.substring(0, 97) + '...' : userRequest;
  }
}

/**
 * Truncates a message to fit within Twilio's character limit
 * @param {string} message - The message to truncate
 * @returns {string} - Truncated message
 */
function truncateMessage(message: string): string {
  if (message.length <= TWILIO_MESSAGE_LIMIT) {
    return message;
  }
  
  return message.substring(0, TWILIO_MESSAGE_LIMIT - 3) + '...';
}

/**
 * Send status messages during different stages of processing
 * @param {string} to - Recipient phone number
 * @param {twilio.Twilio} twilioClient - Initialized Twilio client
 * @param {string} stage - Processing stage identifier
 * @param {StatusMessageData} data - Additional data for the message
 * @param {GoogleGenAI} genAI - Google AI client (optional, for summarization)
 * @returns {Promise<void>}
 */
export async function sendStatusMessage(
  to: string,
  twilioClient: twilio.Twilio,
  stage: string,
  data: StatusMessageData = {},
  genAI?: GoogleGenAI
): Promise<void> {
  let message = '';
  
  // Helper function to get summarized description
  const getSummarizedDescription = async (description: string): Promise<string> => {
    if (!genAI || !description) return description || '';
    return await summarizeUserRequest(description, genAI);
  };
  
  switch (stage) {
    case 'audio_received':
      message = "I received your voice note! Transcribing it now...";
      break;
      
    case 'audio_transcribed':
      if (data.transcript) {
        const summarizedTranscript = genAI ? await summarizeUserRequest(data.transcript, genAI) : data.transcript;
        message = `Transcribed as: "${summarizedTranscript}". Now processing your request...`;
      } else {
        message = "Audio transcribed. Now processing your request...";
      }
      break;
      
    case 'text_received':
      if (data.text) {
        const summarizedText = genAI ? await summarizeUserRequest(data.text, genAI) : data.text;
        message = `Got it! Processing: "${summarizedText}"`;
      } else {
        message = "Got it! Processing your request...";
      }
      break;
      
    case 'intent_classified':
      const description = data.description || '';
      const summarizedDesc = genAI ? await summarizeUserRequest(description, genAI) : description;
      
      if (data.intent === 'new_site') {
        message = summarizedDesc ? `I'll create a new website for you: "${summarizedDesc}"` : "I'll create a new website for you";
      } else if (data.intent === 'edit_site') {
        const projectName = data.projectName || 'your site';
        message = summarizedDesc ? `I'll edit your existing site "${projectName}": "${summarizedDesc}"` : `I'll edit your existing site "${projectName}"`;
      } else if (data.intent === 'import_repo') {
        message = summarizedDesc ? `I'll import and work on your repository: "${summarizedDesc}"` : "I'll import and work on your repository";
      } else if (data.intent === 'scaffold_mode') {
        const projectType = data.projectType || 'Next.js';
        message = summarizedDesc ? `I'll create a multi-file ${projectType} project for you: "${summarizedDesc}"` : `I'll create a multi-file ${projectType} project for you`;
      }
      break;
      
    case 'project_named':
      message = `I'm creating a project called "${data.projectName}". This might take a few minutes...`;
      break;
      
    case 'editing_site':
      message = `I'm updating your site "${data.projectName}" with your requested changes...`;
      break;
      
    case 'generating_website':
      message = "🤖 AI is generating your website code...";
      break;
      
    case 'website_generated':
      message = "Website generated! Setting up version control...";
      break;
      
    case 'deploying':
      message = "🚀 Deploying your website to Vercel...";
      break;
      
    case 'creating_github_repo':
      message = "📁 Creating GitHub repository for version control...";
      break;
      
    case 'github_repo_created':
      message = `✅ GitHub repository created: ${data.repoName}`;
      break;
      
    case 'committing_to_github':
      message = "💾 Saving your code to GitHub...";
      break;
      
    case 'github_committed':
      message = "✅ Code committed to GitHub successfully!";
      break;
      
    case 'deployment_complete':
      message = `Done! Your new website is live with version control at: ${data.url}`;
      break;
      
    case 'update_complete':
      message = `Done! Your updated website is live at: ${data.url}`;
      break;
      
    case 'editing_website':
      message = `✏️ Updating your website "${data.projectName}" with your changes...`;
      break;
      
    case 'website_updated':
      message = `✅ Website updated successfully!\n\n🌐 Updated site: ${data.url}`;
      break;
      
    case 'repo_detected':
      message = `📁 GitHub repository detected: ${data.repoName}`;
      break;
      
    case 'repo_imported':
      message = `✅ Repository "${data.projectName}" imported successfully!\n\n📁 GitHub: ${data.repoUrl}\n\nYou can now edit it with follow-up messages!`;
      break;
      
    case 'repo_not_found':
      message = `❌ Repository not found: ${data.repoName}\n\nPlease check the repository name or URL and try again.`;
      break;
      
    case 'timeout_fallback':
      message = "Your request is taking longer than expected. I'm deploying a temporary site for now...";
      break;
      
    case 'timeout_complete':
      message = `I've created a temporary site at: ${data.url}\nPlease try again with a simpler description for better results.`;
      break;
      
    case 'importing_repository':
      message = `📦 Importing repository "${data.repoName}" and analyzing the code...`;
      break;
      
    case 'repository_imported':
      message = `✅ Repository imported successfully! Creating website from the code...`;
      break;
      
    case 'error':
      if (data.error || data.message) {
        message = (data.error || data.message) as string;
      } else {
        message = "Sorry, I encountered an error while processing your request. Please try again with a different description.";
      }
      break;
      
    case 'timeout':
      message = "⏰ The operation is taking longer than expected. Please try again in a moment.";
      break;
      
    case 'fallback_deployment':
      message = `⚠️ Deployment encountered an issue, but your website should still be accessible at: ${data.url}`;
      break;
      
    case 'scaffold_detected':
      message = `🏗️ Multi-file project detected! Creating ${data.projectType || 'Next.js'} scaffold...`;
      break;
      
    case 'generating_scaffold':
      message = `🔧 Generating ${data.projectType || 'Next.js'} project structure with all necessary files...`;
      break;
      
    case 'deploying_scaffold':
      message = `🚀 Deploying your ${data.projectType || 'Next.js'} project to GitHub and Vercel...`;
      break;
      
    case 'scaffold_complete':
      message = `✅ Your ${data.projectType || 'Next.js'} project is ready!\n\n🌐 Live site: ${data.url}\n📁 GitHub repo: ${data.repoUrl || 'Creating...'}\n\nYou can now edit it with follow-up messages!`;
      break;
      
    case 'enhanced_editing_enabled':
      message = `🚀 Enhanced vector-based editing enabled! Analyzing your codebase for intelligent modifications...`;
      break;
      
    case 'analyzing_codebase':
      message = `🔍 Analyzing your codebase and finding relevant code chunks for your request...`;
      break;
      
    case 'enhanced_changes_generated':
      message = `✨ Generated ${data.changeCount || 0} intelligent code changes based on your codebase context...`;
      break;
      
    case 'applying_enhanced_changes':
      message = `⚡ Applying context-aware changes to your project...`;
      break;
      
    case 'enhanced_edit_fallback':
      message = `⚠️ Enhanced editing unavailable (repository not indexed). Using standard editing instead...`;
      break;
      
    case 'processing':
      message = data.message || "⚙️ Processing your request...";
      break;
      
    default:
      message = data.message || "Processing your request...";
  }
  
  // Ensure message doesn't exceed Twilio's character limit
  const finalMessage = truncateMessage(message);
  
  await sendWhatsappMessage(to, finalMessage, twilioClient);
}

/**
 * Send a help message with available commands
 * @param {string} to - Recipient phone number
 * @param {twilio.Twilio} twilioClient - Initialized Twilio client
 * @returns {Promise<void>}
 */
export async function sendHelpMessage(to: string, twilioClient: twilio.Twilio): Promise<void> {
  const helpMessage = `🤖 *WhatsApp Website Bot Help*

I can help you create and manage websites! Here's what you can do:

🆕 *Create a new website:*
Just describe what you want: "Create a portfolio website for a photographer" or send a voice note!

✏️ *Edit your existing website:*
"Add a contact form to my site" or "Change the color scheme to blue"

📦 *Import from GitHub:*
"Import my repository: username/repo-name" or send a GitHub URL

🎤 *Voice commands:*
Send voice notes - I'll transcribe and process them!

💡 *Tips:*
- Be specific about what you want
- I'll create a GitHub repository for version control
- Your sites are deployed instantly to Vercel
- I remember your last project for easy editing

Just send me a message describing what you want to build! 🚀`;

  await sendWhatsappMessage(to, helpMessage, twilioClient);
}