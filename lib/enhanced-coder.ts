import { GoogleGenAI } from '@google/genai';
import VectorRetrieval from './vector-retrieval.js';
import { UserProject } from '../types/index.js';

interface EditRequest {
  projectId: number;
  targetFile?: string;
  targetFiles?: string[];
  description: string;
  userMessage: string;
}

interface EditResult {
  success: boolean;
  changes: FileChange[];
  summary: string;
  reasoning: string;
  error?: string;
}

interface FileChange {
  filePath: string;
  action: 'create' | 'modify' | 'delete';
  content?: string;
  diff?: string;
  reasoning: string;
}

interface CodeContext {
  targetChunks: any[];
  relatedChunks: any[];
  suggestions: string[];
  repoStructure: any;
}

export class EnhancedCoder {
  private genAI: GoogleGenAI;
  private vectorRetrieval: VectorRetrieval;

  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY! });
    this.vectorRetrieval = new VectorRetrieval();
  }

  /**
   * Generate context-aware code changes using vector retrieval
   */
  async generateEnhancedEdit(request: EditRequest): Promise<EditResult> {
    try {
      console.log(`🔧 Enhanced coding for project ${request.projectId}: ${request.description}`);

      // Check if repository is indexed
      const indexStatus = await this.vectorRetrieval.isRepositoryIndexed(request.projectId);
      if (!indexStatus.indexed) {
        return {
          success: false,
          changes: [],
          summary: 'Repository not indexed',
          reasoning: 'Repository must be indexed before enhanced editing can be performed',
          error: 'REPOSITORY_NOT_INDEXED'
        };
      }

      // Step 1: Use Gemini-2.0-flash to determine which files to retrieve for editing
      const targetFiles = await this.determineTargetFiles(request);
      console.log(`🎯 Gemini-2.0-flash identified ${targetFiles.length} target files:`, targetFiles);

      // Step 2: Gather context using vector retrieval with identified target files
      const context = await this.gatherCodeContext({
        ...request,
        targetFiles // Pass the identified files
      });

      // Step 3: Generate changes using AI with context
      const changes = await this.generateContextAwareChanges(request, context);

      // Step 4: Validate and refine changes
      const validatedChanges = await this.validateChanges(changes, context);

      return {
        success: true,
        changes: validatedChanges,
        summary: this.generateSummary(validatedChanges),
        reasoning: this.generateReasoning(request, context, validatedChanges)
      };

    } catch (error) {
      console.error('Enhanced coding failed:', error);
      return {
        success: false,
        changes: [],
        summary: 'Enhanced coding failed',
        reasoning: `Error: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Use Gemini-2.0-flash to determine which files should be retrieved for editing
   */
  private async determineTargetFiles(request: EditRequest): Promise<string[]> {
    try {
      console.log('🎯 Using Gemini-2.0-flash to determine target files...');

      // Get repository structure to provide context
      const repoStructure = await this.getRepositoryStructure(request.projectId);
      
      const prompt = `You are an expert software engineer analyzing a code edit request. Based on the repository structure and edit description, determine which specific files need to be retrieved and potentially modified.

EDIT REQUEST:
${request.description}

USER MESSAGE:
${request.userMessage}

REPOSITORY STRUCTURE:
Files in repository (${repoStructure.fileCount} total):
${repoStructure.files?.slice(0, 30).join('\n') || 'No files found'}

Key directories:
${repoStructure.directories?.slice(0, 10).join('\n') || 'No directories found'}

INSTRUCTIONS:
1. Analyze the edit request to understand what needs to be changed
2. Identify which specific files are most likely to need modification
3. Consider related files that might be affected (imports, dependencies, etc.)
4. Focus on files that are directly relevant to the requested changes
5. Prioritize main implementation files over configuration files unless specifically requested

Return a JSON array of file paths that should be retrieved for this edit:
["path/to/file1.ext", "path/to/file2.ext", ...]

Limit to 5-8 most relevant files. Be specific and accurate with file paths.`;

      const result = await this.genAI.models.generateContent({
        model: 'gemini-2.0-flash',
        contents: prompt,
        config: {
          temperature: 0.2,
          maxOutputTokens: 1000
        }
      });

      const response = result.text || '';
      const targetFiles = this.parseTargetFilesFromResponse(response, repoStructure.files || []);
      
      console.log(`🎯 Gemini-2.0-flash identified ${targetFiles.length} target files:`, targetFiles);
      return targetFiles;

    } catch (error) {
      console.error('Failed to determine target files:', error);
      // Fallback: use vector similarity search to find relevant files
      console.log('🔄 Falling back to vector similarity search...');
      return this.fallbackFileSelection(request);
    }
  }

  /**
   * Parse target files from Gemini response
   */
  private parseTargetFilesFromResponse(response: string, availableFiles: string[]): string[] {
    try {
      // Clean up response if it contains markdown
      let cleanResponse = response.trim();
      if (cleanResponse.includes('```json')) {
        cleanResponse = cleanResponse.replace(/.*```json\n?/, '').replace(/\n?```.*/, '');
      } else if (cleanResponse.includes('```')) {
        cleanResponse = cleanResponse.replace(/.*```\n?/, '').replace(/\n?```.*/, '');
      }

      // Try to parse as JSON
      const parsed = JSON.parse(cleanResponse);
      if (Array.isArray(parsed)) {
        // Validate that files exist in the repository
        const validFiles = parsed.filter(file =>
          typeof file === 'string' &&
          availableFiles.some(availableFile => availableFile.includes(file) || file.includes(availableFile))
        );
        return validFiles.slice(0, 8); // Limit to 8 files
      }

    } catch (error) {
      console.warn('Failed to parse JSON response, trying manual extraction:', error);
    }

    // Fallback: extract file paths manually
    const filePattern = /["']([^"']*\.(tsx?|jsx?|ts|js|py|java|cpp|c|go|rs|php|rb|swift|kt|dart|vue|svelte|astro|md|json|yaml|yml|css|scss|sass|less|html|xml))['"]/gi;
    const matches = response.match(filePattern) || [];
    const extractedFiles = matches
      .map(match => match.replace(/["']/g, ''))
      .filter(file => availableFiles.some(availableFile =>
        availableFile.includes(file) || file.includes(availableFile)
      ))
      .slice(0, 8);

    return extractedFiles.length > 0 ? extractedFiles : this.getDefaultTargetFiles(availableFiles);
  }

  /**
   * Fallback file selection using vector similarity
   */
  private async fallbackFileSelection(request: EditRequest): Promise<string[]> {
    try {
      const retrievalResult = await this.vectorRetrieval.findRelevantChunks({
        query: request.description,
        projectId: request.projectId,
        maxChunks: 10,
        similarityThreshold: 0.6
      });

      // Extract unique file paths from relevant chunks
      const uniqueFiles = [...new Set(retrievalResult.chunks.map(chunk => chunk.file_path))]
        .filter(Boolean)
        .slice(0, 6);

      return uniqueFiles;
    } catch (error) {
      console.error('Fallback file selection failed:', error);
      return [];
    }
  }

  /**
   * Get default target files when all else fails
   */
  private getDefaultTargetFiles(availableFiles: string[]): string[] {
    // Look for common main files
    const commonPatterns = [
      /page\.(tsx?|jsx?)$/,
      /layout\.(tsx?|jsx?)$/,
      /index\.(tsx?|jsx?)$/,
      /app\.(tsx?|jsx?)$/,
      /main\.(tsx?|jsx?|py|java|cpp|go|rs)$/,
      /component\.(tsx?|jsx?)$/
    ];

    const defaultFiles: string[] = [];
    
    for (const pattern of commonPatterns) {
      const matches = availableFiles.filter(file => pattern.test(file));
      defaultFiles.push(...matches.slice(0, 2));
      if (defaultFiles.length >= 4) break;
    }

    return defaultFiles.slice(0, 4);
  }

  /**
   * Gather relevant code context using vector retrieval
   */
  private async gatherCodeContext(request: EditRequest): Promise<CodeContext> {
    console.log('📚 Gathering code context...');

    let targetChunks: any[] = [];
    let relatedChunks: any[] = [];
    let suggestions: string[] = [];

    // If specific files are targeted (new approach), get their context
    if (request.targetFiles && request.targetFiles.length > 0) {
      console.log(`📁 Getting context for ${request.targetFiles.length} target files:`, request.targetFiles);
      
      // Get chunks from all target files
      for (const targetFile of request.targetFiles) {
        const editingContext = await this.vectorRetrieval.getEditingContext(
          request.projectId,
          targetFile,
          request.description
        );
        
        targetChunks.push(...editingContext.targetChunks);
        // Avoid duplicating related chunks
        const newRelatedChunks = editingContext.relatedChunks.filter(
          chunk => !relatedChunks.some(existing => existing.id === chunk.id)
        );
        relatedChunks.push(...newRelatedChunks);
        suggestions.push(...editingContext.suggestions);
      }
      
      // Remove duplicates from suggestions
      suggestions = [...new Set(suggestions)];
      
    } else if (request.targetFile) {
      // Legacy single file approach
      const editingContext = await this.vectorRetrieval.getEditingContext(
        request.projectId,
        request.targetFile,
        request.description
      );
      
      targetChunks = editingContext.targetChunks;
      relatedChunks = editingContext.relatedChunks;
      suggestions = editingContext.suggestions;
    } else {
      // Find relevant chunks across the entire repository (fallback)
      const retrievalResult = await this.vectorRetrieval.findRelevantChunks({
        query: request.description,
        projectId: request.projectId,
        maxChunks: 15,
        similarityThreshold: 0.6
      });
      
      relatedChunks = retrievalResult.chunks;
    }

    // Get repository structure (simplified)
    const repoStructure = await this.getRepositoryStructure(request.projectId);

    console.log(`📊 Context gathered: ${targetChunks.length} target chunks, ${relatedChunks.length} related chunks`);

    return {
      targetChunks,
      relatedChunks,
      suggestions,
      repoStructure
    };
  }

  /**
   * Generate context-aware code changes using AI
   */
  private async generateContextAwareChanges(
    request: EditRequest,
    context: CodeContext
  ): Promise<FileChange[]> {
    console.log('🤖 Generating context-aware changes...');

    const prompt = this.buildEnhancedPrompt(request, context);

    const result = await this.genAI.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: prompt,
      config: {
        temperature: 0.3,
        maxOutputTokens: 4000
      }
    });

    const response = result.text || '';
    return this.parseChangesFromResponse(response);
  }

  /**
   * Build comprehensive prompt with context
   */
  private buildEnhancedPrompt(request: EditRequest, context: CodeContext): string {
    const contextSummary = this.buildContextSummary(context);
    
    return `You are an expert software engineer working on a codebase. Use the provided context to make precise, well-informed changes.

EDIT REQUEST:
${request.description}

USER MESSAGE:
${request.userMessage}

REPOSITORY CONTEXT:
${contextSummary}

TARGET FILE CONTEXT:
${context.targetChunks.map(chunk => 
  `File: ${chunk.file_path} (chunk ${chunk.chunk_id})\n${chunk.content}`
).join('\n\n')}

RELATED CODE CONTEXT:
${context.relatedChunks.slice(0, 5).map(chunk => 
  `File: ${chunk.file_path}\n${chunk.content.substring(0, 300)}...`
).join('\n\n')}

AI SUGGESTIONS:
${context.suggestions.join('\n')}

INSTRUCTIONS:
1. Analyze the context to understand the codebase structure and patterns
2. Generate precise changes that follow existing patterns and conventions
3. Consider impact on related files and components
4. Provide clear reasoning for each change
5. Use unified diff format for modifications

RESPONSE FORMAT:
Return a JSON object with this structure:
{
  "changes": [
    {
      "filePath": "path/to/file.ext",
      "action": "create|modify|delete",
      "content": "full file content for create/modify",
      "diff": "unified diff for modify actions",
      "reasoning": "explanation of this change"
    }
  ]
}

Generate the changes now:`;
  }

  /**
   * Parse AI response into structured changes
   */
  private parseChangesFromResponse(response: string): FileChange[] {
    try {
      // Clean up response if it contains markdown
      let cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      const parsed = JSON.parse(cleanResponse);
      return parsed.changes || [];

    } catch (error) {
      console.error('Failed to parse AI response:', error);
      console.log('Raw response:', response);
      
      // Fallback: try to extract changes manually
      return this.extractChangesManually(response);
    }
  }

  /**
   * Manual extraction fallback
   */
  private extractChangesManually(response: string): FileChange[] {
    // Simple fallback - look for file patterns
    const changes: FileChange[] = [];
    
    // This is a basic implementation - could be enhanced
    const lines = response.split('\n');
    let currentFile = '';
    let currentContent = '';
    
    for (const line of lines) {
      if (line.includes('File:') || line.includes('filePath:')) {
        if (currentFile && currentContent) {
          changes.push({
            filePath: currentFile,
            action: 'modify',
            content: currentContent,
            reasoning: 'Extracted from AI response'
          });
        }
        currentFile = line.replace(/.*[Ff]ile[Pp]ath?[:\s]+/, '').replace(/[",].*/, '');
        currentContent = '';
      } else if (currentFile) {
        currentContent += line + '\n';
      }
    }
    
    if (currentFile && currentContent) {
      changes.push({
        filePath: currentFile,
        action: 'modify',
        content: currentContent,
        reasoning: 'Extracted from AI response'
      });
    }
    
    return changes;
  }

  /**
   * Validate and refine generated changes
   */
  private async validateChanges(
    changes: FileChange[],
    context: CodeContext
  ): Promise<FileChange[]> {
    console.log(`🔍 Validating ${changes.length} changes...`);

    const validatedChanges: FileChange[] = [];

    for (const change of changes) {
      try {
        // Basic validation
        if (!change.filePath || !change.action) {
          console.warn('Skipping invalid change:', change);
          continue;
        }

        // Validate file paths
        if (change.filePath.includes('..') || change.filePath.startsWith('/')) {
          console.warn('Skipping potentially unsafe file path:', change.filePath);
          continue;
        }

        // For modify actions, ensure we have content or diff
        if (change.action === 'modify' && !change.content && !change.diff) {
          console.warn('Skipping modify action without content:', change.filePath);
          continue;
        }

        validatedChanges.push(change);

      } catch (error) {
        console.warn('Error validating change:', error);
      }
    }

    console.log(`✅ Validated ${validatedChanges.length}/${changes.length} changes`);
    return validatedChanges;
  }

  /**
   * Get simplified repository structure
   */
  private async getRepositoryStructure(projectId: number): Promise<any> {
    try {
      // Get unique file paths from chunks
      const { data: files, error } = await this.vectorRetrieval['supabase']
        .from('repo_chunks')
        .select('file_path')
        .eq('project_id', projectId);

      if (error) {
        console.warn('Failed to get repository structure:', error);
        return {};
      }

      const uniqueFiles = [...new Set((files || []).map(f => f.file_path).filter(Boolean))] as string[];
      
      return {
        fileCount: uniqueFiles.length,
        files: uniqueFiles.slice(0, 20), // Limit for context
        directories: this.extractDirectories(uniqueFiles)
      };

    } catch (error) {
      console.warn('Error getting repository structure:', error);
      return {};
    }
  }

  /**
   * Extract directory structure from file paths
   */
  private extractDirectories(filePaths: string[]): string[] {
    const dirs = new Set<string>();
    
    for (const filePath of filePaths) {
      const parts = filePath.split('/');
      for (let i = 1; i < parts.length; i++) {
        dirs.add(parts.slice(0, i).join('/'));
      }
    }
    
    return Array.from(dirs).sort();
  }

  /**
   * Build context summary for prompt
   */
  private buildContextSummary(context: CodeContext): string {
    const summary = [
      `Repository: ${context.repoStructure.fileCount || 0} files`,
      `Target chunks: ${context.targetChunks.length}`,
      `Related chunks: ${context.relatedChunks.length}`,
      `AI suggestions: ${context.suggestions.length}`
    ];

    if (context.repoStructure.directories) {
      summary.push(`Key directories: ${context.repoStructure.directories.slice(0, 5).join(', ')}`);
    }

    return summary.join('\n');
  }

  /**
   * Generate summary of changes
   */
  private generateSummary(changes: FileChange[]): string {
    const actions = changes.reduce((acc, change) => {
      acc[change.action] = (acc[change.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const parts = Object.entries(actions).map(([action, count]) => 
      `${count} ${action}${count > 1 ? 's' : ''}`
    );

    return `Generated ${changes.length} changes: ${parts.join(', ')}`;
  }

  /**
   * Generate reasoning explanation
   */
  private generateReasoning(
    request: EditRequest,
    context: CodeContext,
    changes: FileChange[]
  ): string {
    return `Based on analysis of ${context.relatedChunks.length} related code chunks and ${context.suggestions.length} AI suggestions, generated ${changes.length} targeted changes to implement: ${request.description}`;
  }

  /**
   * Check if enhanced coding is available for a project
   */
  async isEnhancedCodingAvailable(projectId: number): Promise<boolean> {
    const indexStatus = await this.vectorRetrieval.isRepositoryIndexed(projectId);
    return indexStatus.indexed && (indexStatus.chunkCount || 0) > 0;
  }
}

export default EnhancedCoder;