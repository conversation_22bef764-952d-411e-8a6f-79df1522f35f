/**
 * Intent classification functionality using Google Gemini Flash
 */
import { GoogleGenAI } from "@google/genai";
import { IntentClassification, RepositoryInfo, UserProject } from '../types/index.js';
import { findProjectByRepo, findProjectByRepoUrl } from './database.js';

// GitHub URL and repository reference patterns
const GITHUB_URL_PATTERNS = [
    /https?:\/\/github\.com\/[\w.-]+\/[\w.-]+/gi,
    /github\.com\/[\w.-]+\/[\w.-]+/gi,
    /work on [\w.-]+ repo/gi,
    /edit [\w.-]+ repository/gi,
    /import [\w.-]+ repo/gi,
    /continue working on [\w.-]+/gi
];

/**
 * Extracts GitHub repository information from a message
 * @param {string} message - The user's message
 * @returns {RepositoryInfo | null} - Repository info or null if not found
 */
export function extractRepositoryInfo(message: string): RepositoryInfo | null {
    console.log(`Extracting repository info from: "${message}"`);
    
    // Check for GitHub URLs
    const urlMatch = message.match(/https?:\/\/github\.com\/([\w.-]+)\/([\w.-]+)/i);
    if (urlMatch) {
        const [fullUrl, owner, repo] = urlMatch;
        const repoName = `${owner}/${repo}`;
        console.log(`Found GitHub URL: ${fullUrl} -> ${repoName}`);
        return {
            type: 'url',
            fullUrl,
            repoName,
            owner,
            repo
        };
    }
    
    // Check for github.com without protocol
    const shortUrlMatch = message.match(/github\.com\/([\w.-]+)\/([\w.-]+)/i);
    if (shortUrlMatch) {
        const [, owner, repo] = shortUrlMatch;
        const repoName = `${owner}/${repo}`;
        const fullUrl = `https://github.com/${repoName}`;
        console.log(`Found GitHub reference: github.com/${repoName} -> ${fullUrl}`);
        return {
            type: 'url',
            fullUrl,
            repoName,
            owner,
            repo
        };
    }
    
    // Check for repo name patterns
    const repoPatterns = [
        /work on ([\w.-]+) repo/i,
        /edit ([\w.-]+) repository/i,
        /import ([\w.-]+) repo/i,
        /continue working on ([\w.-]+)/i
    ];
    
    for (const pattern of repoPatterns) {
        const match = message.match(pattern);
        if (match) {
            const repoIdentifier = match[1];
            console.log(`Found repository reference: ${repoIdentifier}`);
            return {
                type: 'name',
                repoIdentifier,
                fullUrl: null,
                repoName: null // Will need to be resolved later
            };
        }
    }
    
    console.log(`No repository information found in message`);
    return null;
}

/**
 * Classifies user intent as 'new_site', 'edit_site', or 'import_repo'
 * @param {string} message - The user's message
 * @param {GoogleGenAI} genAI - The initialized Google AI client
 * @param {UserProject | null} lastProject - The user's last project context (if any)
 * @param {string} phoneNumber - User's phone number for checking existing projects
 * @returns {Promise<IntentClassification>} - Classification result with intent and confidence
 */
export async function classifyIntent(message: string, genAI: GoogleGenAI, lastProject: UserProject | null = null, phoneNumber?: string): Promise<IntentClassification> {
    console.log("=== INTENT CLASSIFICATION ===");
    console.log(`Classifying message: "${message ? message.substring(0, 100) : 'undefined'}..."`);
    console.log(`Has last project: ${!!lastProject}`);
    
    // Handle null/undefined messages
    if (!message || typeof message !== 'string') {
        console.log("Invalid message provided, defaulting to new_site");
        return { intent: 'new_site', confidence: 0.5, reasoning: 'Invalid message' };
    }
    
    // First check if message contains GitHub repository references
    const repoInfo = extractRepositoryInfo(message);
    if (repoInfo && phoneNumber) {
        console.log(`Repository reference detected: ${JSON.stringify(repoInfo)}`);
        
        // Check if this repository is already imported for this user
        try {
            let existingProject: any = null;
            
            if (repoInfo.type === 'url' && repoInfo.fullUrl) {
                // First try to find by URL
                existingProject = await findProjectByRepoUrl(phoneNumber, repoInfo.fullUrl);
                
                // If not found by URL, try by repo name
                if (!existingProject && repoInfo.repoName) {
                    existingProject = await findProjectByRepo(phoneNumber, repoInfo.repoName);
                }
            } else if (repoInfo.type === 'name' && repoInfo.repoIdentifier) {
                // For name-based references, we need to check if it matches any existing project
                // This is a simplified approach - in a full implementation, you might want to
                // search by project name or other identifiers
                console.log(`Repository name reference detected: ${repoInfo.repoIdentifier}`);
                // Could potentially search by project name containing the identifier
            }
            
            if (existingProject) {
                const repoIdentifier = repoInfo.fullUrl || repoInfo.repoName || repoInfo.repoIdentifier;
                console.log(`Repository ${repoIdentifier} already exists as project: ${existingProject.project_name}`);
                console.log(`Classifying as edit_site instead of import_repo`);
                return {
                    intent: 'edit_site',
                    confidence: 0.9,
                    reasoning: `Repository ${repoIdentifier} is already imported as project "${existingProject.project_name}". Treating as edit request.`,
                    repositoryInfo: repoInfo
                };
            }
        } catch (error) {
            console.warn(`Error checking for existing repository: ${error.message}`);
            // Continue with import_repo classification if database check fails
        }
        
        return {
            intent: 'import_repo',
            confidence: 0.95,
            reasoning: 'Message contains GitHub repository reference',
            repositoryInfo: repoInfo
        };
    } else if (repoInfo && !phoneNumber) {
        // If we have repo info but no phone number, still classify as import_repo
        // The import process will handle duplicate checking
        console.log(`Repository reference detected but no phone number provided for duplicate checking`);
        return {
            intent: 'import_repo',
            confidence: 0.95,
            reasoning: 'Message contains GitHub repository reference',
            repositoryInfo: repoInfo
        };
    }
    
    const contextInfo = lastProject 
        ? `The user's last project was "${lastProject.project_name}" (${lastProject.description}) deployed at ${lastProject.url}.`
        : "The user has no previous projects.";
    
    const prompt = `You are an intent classifier for a website generation bot. Analyze the user's message and determine if they want to:
    
    1. CREATE a new single-file website (new_site)
    2. EDIT/MODIFY their existing website (edit_site)
    3. IMPORT/WORK ON an existing GitHub repository (import_repo)
    4. CREATE a new multi-file project/application (scaffold_mode)
    
    Context: ${contextInfo}
    
    User message: "${message}"
    
    Classification rules:
    - If the user mentions "new", "create", "make", "build", "generate" a simple website/site/page, classify as "new_site"
    - If the user mentions "change", "edit", "modify", "update", "fix", "add to", "remove from", "improve" their site, classify as "edit_site"
    - If the user references "my site", "the site", "current site", "this site", classify as "edit_site"
    - If the user mentions "work on", "import", "continue with" and references a repository, classify as "import_repo"
    - If the user mentions creating "app", "application", "project", or specific frameworks like "Next.js", "React", "Astro", "Remix", "Vue", classify as "scaffold_mode"
    - If the user mentions "multi-file", "full project", "complete application", "scaffold", classify as "scaffold_mode"
    - If the message is ambiguous but the user has a recent project, lean towards "edit_site"
    - If the user has no previous projects, default to "new_site" unless clear scaffold indicators
    
    Respond with ONLY a JSON object in this exact format:
    {
      "intent": "new_site" or "edit_site" or "import_repo" or "scaffold_mode",
      "confidence": 0.0 to 1.0,
      "reasoning": "brief explanation"
    }`;

    try {
        if (!genAI) {
            console.log("No AI client provided, using fallback logic");
            return await fallbackClassification(message, lastProject, phoneNumber);
        }
        
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: prompt,
            config: {
                temperature: 0.1, // Low temperature for consistent classification
                topP: 0.8,
                maxOutputTokens: 200
            }
        });
        
        let response = result.text?.trim() || '';
        console.log(`Raw classification response: ${response}`);
        
        // Clean up response if it contains markdown code blocks
        if (response.startsWith('```json')) {
            response = response.replace(/```json\n?/, '').replace(/\n?```$/, '');
        } else if (response.startsWith('```')) {
            response = response.replace(/```\n?/, '').replace(/\n?```$/, '');
        }
        
        const classification = JSON.parse(response) as IntentClassification;
        
        // Validate the response
        if (!classification.intent || !['new_site', 'edit_site', 'import_repo', 'scaffold_mode'].includes(classification.intent)) {
            throw new Error('Invalid intent classification');
        }
        
        // Default to new_site if no last project and intent is edit_site
        if (classification.intent === 'edit_site' && !lastProject) {
            console.log("Overriding edit_site to new_site - no previous project exists");
            classification.intent = 'new_site';
            classification.reasoning = 'No previous project exists, treating as new site request';
        }
        
        // For import_repo intent, don't override even if no last project
        // The repository import process will handle this
        
        console.log(`Classification result: ${classification.intent} (confidence: ${classification.confidence})`);
        console.log(`Reasoning: ${classification.reasoning}`);
        
        return classification;
        
    } catch (error) {
        console.error("Error classifying intent:", error);
        
        // Fallback logic - check for repo info first
        const repoInfo = extractRepositoryInfo(message);
        if (repoInfo && phoneNumber) {
            // Check if this repository is already imported for this user
            try {
                let existingProject: any = null;
                
                if (repoInfo.type === 'url' && repoInfo.fullUrl) {
                    // First try to find by URL
                    existingProject = await findProjectByRepoUrl(phoneNumber, repoInfo.fullUrl);
                    
                    // If not found by URL, try by repo name
                    if (!existingProject && repoInfo.repoName) {
                        existingProject = await findProjectByRepo(phoneNumber, repoInfo.repoName);
                    }
                }
                
                if (existingProject) {
                    const repoIdentifier = repoInfo.fullUrl || repoInfo.repoName || repoInfo.repoIdentifier;
                    console.log(`Fallback: Repository ${repoIdentifier} already exists, classifying as edit_site`);
                    return {
                        intent: 'edit_site',
                        confidence: 0.6,
                        reasoning: `Fallback: Repository ${repoIdentifier} is already imported. Treating as edit request.`,
                        repositoryInfo: repoInfo
                    };
                }
            } catch (error) {
                console.warn(`Fallback: Error checking for existing repository: ${error.message}`);
            }
            
            console.log(`Using fallback intent: import_repo (found repo info)`);
            return {
                intent: 'import_repo',
                confidence: 0.7,
                reasoning: 'Fallback classification - repository reference detected',
                repositoryInfo: repoInfo
            };
        } else if (repoInfo && !phoneNumber) {
            console.log(`Using fallback intent: import_repo (found repo info, no phone number)`);
            return {
                intent: 'import_repo',
                confidence: 0.7,
                reasoning: 'Fallback classification - repository reference detected',
                repositoryInfo: repoInfo
            };
        }
        
        return await fallbackClassification(message, lastProject, phoneNumber);
    }
}

/**
 * Fallback classification logic when AI client is not available
 * @param {string} message - The user's message
 * @param {UserProject | null} lastProject - The user's last project context
 * @param {string} phoneNumber - User's phone number for checking existing projects
 * @returns {Promise<IntentClassification>} - Classification result
 */
async function fallbackClassification(message: string, lastProject: UserProject | null, phoneNumber?: string): Promise<IntentClassification> {
    console.log("Using fallback classification logic");
    
    const lowerMessage = message.toLowerCase();
    
    // Check for repository-related keywords
    const repoKeywords = ['github', 'repo', 'repository', 'import', 'work on'];
    if (repoKeywords.some(keyword => lowerMessage.includes(keyword))) {
        return {
            intent: 'import_repo',
            confidence: 0.7,
            reasoning: 'Fallback: Message contains repository-related keywords'
        };
    }
    
    // Check for scaffold mode keywords
    const scaffoldKeywords = ['app', 'application', 'project', 'next.js', 'nextjs', 'react', 'astro', 'remix', 'vue', 'scaffold', 'multi-file'];
    if (scaffoldKeywords.some(keyword => lowerMessage.includes(keyword))) {
        return {
            intent: 'scaffold_mode',
            confidence: 0.7,
            reasoning: 'Fallback: Message contains scaffold/framework keywords'
        };
    }
    
    // Check for edit keywords
    if (containsEditKeywords(message) && lastProject) {
        return {
            intent: 'edit_site',
            confidence: 0.6,
            reasoning: 'Fallback: Message contains edit keywords and user has previous project'
        };
    }
    
    // Default to new site
    return {
        intent: 'new_site',
        confidence: 0.5,
        reasoning: 'Fallback: Default classification'
    };
}

/**
 * Determines if a message is likely an edit request based on keywords
 * @param {string} message - The user's message
 * @returns {boolean} - True if message contains edit keywords
 */
export function containsEditKeywords(message: string): boolean {
    const editKeywords = [
        'change', 'edit', 'modify', 'update', 'fix', 'improve',
        'add', 'remove', 'delete', 'replace', 'adjust',
        'my site', 'the site', 'current site', 'this site',
        'make it', 'can you', 'please'
    ];
    
    const lowerMessage = message.toLowerCase();
    return editKeywords.some(keyword => lowerMessage.includes(keyword));
}