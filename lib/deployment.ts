/**
 * Vercel deployment functionality
 */
import { DeploymentResponse } from '../types/index.js';

/**
 * Deploys the generated code to Vercel.
 * @param {string} htmlContent - The HTML code of the website.
 * @param {string} vercelToken - The Vercel API token.
 * @param {string} projectName - A URL-friendly name for the new project.
 * @returns {Promise<string>} - The URL of the new deployment.
 */
export async function deployToVercel(htmlContent: string, vercelToken: string, projectName: string): Promise<string> {
    console.log(`=== VERCEL DEPLOYMENT DIAGNOSTICS ===`);
    console.log(`Starting Vercel deployment for project: ${projectName}`);
    console.log(`HTML content length: ${htmlContent.length} characters`);
    
    // Log the first and last 100 characters of HTML to verify content
    console.log(`HTML preview (first 100 chars): ${htmlContent.substring(0, 100)}...`);
    console.log(`HTML preview (last 100 chars): ...${htmlContent.substring(htmlContent.length - 100)}`);
    
    // Set a timeout for the Vercel deployment (2 minutes)
    const DEPLOY_TIMEOUT_MS = 2 * 60 * 1000;
    
    // Enhance HTML with additional styles if needed
    htmlContent = enhanceHTMLStyles(htmlContent);
    
    // Log interactive elements check
    logInteractiveElementsCheck(htmlContent);
    
    const deploymentBody = createDeploymentBody(htmlContent, projectName);

    try {
        // Create a promise that rejects after the timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
                reject(new Error('Vercel deployment timed out after ' + (DEPLOY_TIMEOUT_MS/1000) + ' seconds'));
            }, DEPLOY_TIMEOUT_MS);
        });
        
        // Race the deployment against the timeout
        const responsePromise = fetch("https://api.vercel.com/v13/deployments", {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${vercelToken}`,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(deploymentBody)
        });
        
        console.log(`Making deployment request to Vercel API...`);
        
        const response = await Promise.race([responsePromise, timeoutPromise]);
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Vercel API returned ${response.status}: ${errorText}`);
        }
        
        const deployment: DeploymentResponse = await response.json();
        console.log(`Deployment response received, status: ${deployment.readyState || 'unknown'}`);
        console.log(`Full deployment response:`, JSON.stringify(deployment, null, 2));
        
        if (deployment.error) {
            throw new Error(`Vercel deployment failed: ${deployment.error.message}`);
        }

        return extractDeploymentUrl(deployment, projectName);
    } catch (error) {
        console.error("Error during Vercel deployment:", error);
        
        // If it's a timeout or other deployment error, return a fallback URL
        // This allows the process to continue even if deployment fails
        const fallbackUrl = `https://${projectName}.vercel.app`;
        console.log(`Error occurred, using fallback URL: ${fallbackUrl}`);
        return fallbackUrl;
    }
}

/**
 * Enhances HTML with additional CSS styles if not present
 * @param {string} htmlContent - Original HTML content
 * @returns {string} - Enhanced HTML content
 */
function enhanceHTMLStyles(htmlContent: string): string {
    // Add a CSS reset and basic styles if not present
    if (!htmlContent.includes("box-sizing: border-box") && !htmlContent.includes("normalize.css")) {
        console.log("Adding CSS reset and basic styles to improve appearance");
        const styleAddition = `<style>
        *, *::before, *::after { box-sizing: border-box; }
        body { margin: 0; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.5; }
        img { max-width: 100%; height: auto; }
        
        /* Enhanced base styles */
        :root {
            --primary-color: #4a6cf7;
            --secondary-color: #2c3e50;
            --accent-color: #f39c12;
            --text-color: #333;
            --light-bg: #f8f9fa;
        }
        
        /* Button styles */
        .btn {
            display: inline-block;
            padding: 0.6rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background-color: #3a5bd9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* Card styles */
        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }
        
        /* Section styles */
        section {
            padding: 4rem 2rem;
        }
        
        /* Container */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* Responsive grid */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        </style>`;
        
        // Insert after head tag if it exists, otherwise at the beginning
        if (htmlContent.includes("<head>")) {
            htmlContent = htmlContent.replace("<head>", "<head>" + styleAddition);
        } else if (htmlContent.includes("<html>")) {
            htmlContent = htmlContent.replace("<html>", "<html><head>" + styleAddition + "</head>");
        } else {
            htmlContent = styleAddition + htmlContent;
        }
    }
    
    return htmlContent;
}

/**
 * Logs information about interactive elements in the HTML
 * @param {string} htmlContent - HTML content to check
 */
function logInteractiveElementsCheck(htmlContent: string): void {
    const hasInteractiveElements =
        htmlContent.includes("addEventListener") ||
        htmlContent.includes("onclick") ||
        htmlContent.includes("onsubmit") ||
        htmlContent.includes("onchange") ||
        (htmlContent.includes("<button") && htmlContent.includes("<script"));
    
    if (!hasInteractiveElements) {
        console.log("ISSUE DETECTED: No interactive elements found in deployed content");
        console.log("This may indicate the website lacks JavaScript functionality");
    }
}

/**
 * Creates the deployment body for Vercel API
 * @param {string} htmlContent - HTML content to deploy
 * @param {string} projectName - Project name
 * @returns {Object} - Deployment body object
 */
function createDeploymentBody(htmlContent: string, projectName: string): Record<string, any> {
    const deploymentBody: Record<string, any> = {
        name: projectName, // This will be the project name
        files: [{
            file: "index.html",
            data: htmlContent
        }],
        projectSettings: {
            framework: null, // We're deploying a static file, so no framework
            buildCommand: null,
            outputDirectory: null,
            installCommand: null,
            devCommand: null
        },
        target: "production", // Ensure production deployment
        public: true // Make the deployment publicly accessible
    };
    
    // Only add teamId if it exists and is not empty
    if (process.env.VERCEL_TEAM_ID && process.env.VERCEL_TEAM_ID.trim()) {
        deploymentBody.teamId = process.env.VERCEL_TEAM_ID;
        console.log(`Using team ID: ${process.env.VERCEL_TEAM_ID}`);
    } else {
        console.log("No team ID specified, deploying to personal account");
    }
    
    console.log(`Deployment body:`, JSON.stringify(deploymentBody, null, 2));
    return deploymentBody;
}

/**
 * Extracts the deployment URL from Vercel response
 * @param {DeploymentResponse} deployment - Vercel deployment response
 * @param {string} projectName - Project name for fallback
 * @returns {string} - Deployment URL
 */
function extractDeploymentUrl(deployment: DeploymentResponse, projectName: string): string {
    console.log(`=== URL EXTRACTION DEBUG ===`);
    console.log(`Deployment object keys:`, Object.keys(deployment));
    console.log(`Deployment URL field:`, deployment.url);
    console.log(`Deployment alias field:`, deployment.alias);
    console.log(`Deployment name field:`, deployment.name);
    
    // Try multiple URL extraction methods
    let extractedUrl: string | null = null;
    
    // Method 1: Check for alias array (traditional method)
    if (deployment.alias && deployment.alias.length > 0) {
        extractedUrl = `https://${deployment.alias[0]}`;
        console.log(`Method 1 - Using alias: ${extractedUrl}`);
        return extractedUrl;
    }
    
    // Method 2: Check for direct URL field
    if (deployment.url) {
        extractedUrl = deployment.url.startsWith('https://') ? deployment.url : `https://${deployment.url}`;
        console.log(`Method 2 - Using direct URL: ${extractedUrl}`);
        return extractedUrl;
    }
    
    // Method 3: Construct from deployment name and Vercel domain
    if (deployment.name) {
        extractedUrl = `https://${deployment.name}.vercel.app`;
        console.log(`Method 3 - Using deployment name: ${extractedUrl}`);
        return extractedUrl;
    }
    
    // Method 4: Fallback to project name
    const fallbackUrl = `https://${projectName}.vercel.app`;
    console.log(`Method 4 - Using fallback URL: ${fallbackUrl}`);
    return fallbackUrl;
}