import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import { join } from 'path';
import { mkdirSync, writeFileSync, readFileSync, existsSync, readdirSync } from 'fs';
import { homedir } from 'os';

// Local type definitions (mirroring core types)
interface User {
  phoneNumber: string;
  name?: string;
  profilePicture?: string;
  language?: string;
  registeredAt?: Date;
  lastActiveAt?: Date;
  isActive?: boolean;
  preferences?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

interface Workspace {
  id: string;
  path: string;
  isGitWorktree: boolean;
  basePath?: string;
  branch?: string;
  historyPath?: string;
  type: 'temporary' | 'persistent' | 'template' | 'import';
  user: User;
  project?: WorkspaceProject;
  createdAt: Date;
  lastAccessedAt: Date;
  isActive: boolean;
  metadata?: Record<string, unknown>;
}

interface WorkspaceProject {
  name: string;
  description?: string;
  type: 'static' | 'spa' | 'react' | 'vue' | 'angular' | 'nextjs' | 'nuxt' | 'svelte' | 'custom';
  stack: {
    framework?: string;
    styling?: string;
    buildTool?: string;
    packageManager?: string;
    runtime?: string;
    dependencies?: string[];
  };
  config: {
    srcDir: string;
    buildDir: string;
    publicDir: string;
    entryPoint: string;
    configFiles?: string[];
    env?: Record<string, string>;
  };
  build?: {
    command: string;
    outputDir: string;
    env?: Record<string, string>;
    options?: Record<string, unknown>;
  };
  deployment?: {
    platform: string;
    config: Record<string, unknown>;
    env?: Record<string, string>;
    domain?: string;
  };
}

interface WorkspaceFile {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified: Date;
  permissions?: string;
  isBinary?: boolean;
}

interface ProjectStructure {
  files: Array<{
    path: string;
    content: string;
    type: 'file' | 'directory';
    description?: string;
  }>;
  config: Record<string, unknown>;
  setupInstructions: string[];
  dependencies: string[];
}

interface CodeGenerationContext {
  project: WorkspaceProject;
  existingFiles: WorkspaceFile[];
  requirements: string;
  constraints?: string[];
  stylePreferences?: Record<string, unknown>;
}

interface GeneratedFile {
  path: string;
  content: string;
  description: string;
  type: string;
}

interface CodeAnalysis {
  qualityScore: number;
  issues: Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
    line?: number;
    column?: number;
    category: string;
    fix?: string;
  }>;
  suggestions: string[];
  metrics: {
    linesOfCode: number;
    complexity: number;
    maintainability: number;
    testCoverage?: number;
    performance?: number;
  };
}

interface Improvement {
  type: 'performance' | 'security' | 'accessibility' | 'seo' | 'code_quality';
  title: string;
  description: string;
  steps: string[];
  priority: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
}

interface IGeminiRunner {
  initialize(workspace: Workspace): void;
  generateProjectStructure(prompt: string, project: WorkspaceProject): Promise<ProjectStructure>;
  generateCodeFiles(prompt: string, context: CodeGenerationContext): Promise<GeneratedFile[]>;
  updateCode(filePath: string, content: string, instructions: string): Promise<string>;
  analyzeCode(filePath: string, content: string): Promise<CodeAnalysis>;
  generateDocumentation(project: WorkspaceProject): Promise<string>;
  fixCodeIssues(filePath: string, content: string, issues: string[]): Promise<string>;
  suggestImprovements(project: WorkspaceProject): Promise<Improvement[]>;
}

/**
 * Gemini session configuration
 */
export interface GeminiSessionConfig {
  /** Session ID */
  sessionId?: string;
  /** Workspace to operate in */
  workspace: Workspace;
  /** Working directory */
  workingDirectory?: string;
  /** Gemini CLI path */
  geminiCliPath?: string;
  /** Model to use */
  model?: string;
  /** Temperature setting */
  temperature?: number;
  /** Max tokens */
  maxTokens?: number;
  /** System prompt */
  systemPrompt?: string;
  /** Allowed tools */
  allowedTools?: string[];
  /** Session timeout in milliseconds */
  timeout?: number;
}

/**
 * Gemini session info
 */
export interface GeminiSession {
  /** Session ID */
  sessionId: string;
  /** Workspace */
  workspace: Workspace;
  /** Session start time */
  startedAt: Date;
  /** Whether session is running */
  isRunning: boolean;
  /** Child process */
  process?: ChildProcess;
  /** Session config */
  config: GeminiSessionConfig;
  /** Session messages */
  messages: GeminiMessage[];
  /** Session state */
  state: 'created' | 'active' | 'completed' | 'failed' | 'timeout';
  /** Error information */
  error?: string;
}

/**
 * Gemini message
 */
export interface GeminiMessage {
  /** Message ID */
  id: string;
  /** Message role */
  role: 'user' | 'assistant' | 'system';
  /** Message content */
  content: string;
  /** Message timestamp */
  timestamp: Date;
  /** Message metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Project context for code generation
 */
export interface ProjectContext {
  /** Project configuration */
  project: WorkspaceProject;
  /** Workspace */
  workspace: Workspace;
  /** Existing files */
  files: string[];
  /** Project requirements */
  requirements: string;
  /** Technical constraints */
  constraints?: string[];
}

/**
 * Code generation result
 */
export interface CodeGeneration {
  /** Generated code */
  code: string;
  /** Generation description */
  description: string;
  /** Generated files */
  files: GeneratedFile[];
  /** Success status */
  success: boolean;
  /** Error message */
  error?: string;
}

/**
 * Gemini runner events
 */
export interface GeminiRunnerEvents {
  'session-started': (session: GeminiSession) => void;
  'session-ended': (sessionId: string) => void;
  message: (sessionId: string, message: GeminiMessage) => void;
  output: (sessionId: string, data: string) => void;
  error: (sessionId: string, error: string) => void;
  stream: (sessionId: string, chunk: string) => void;
}

/**
 * GeminiRunner class that wraps gemini-cli for AI-powered development
 */
export class GeminiRunner extends EventEmitter implements IGeminiRunner {
  private sessions: Map<string, GeminiSession> = new Map();
  private defaultConfig: GeminiSessionConfig;
  private sessionCounter = 0;
  private persistencePath: string;

  constructor(config: Partial<GeminiSessionConfig> = {}) {
    super();

    this.defaultConfig = {
      geminiCliPath: 'gemini-cli',
      model: 'gemini-pro',
      temperature: 0.7,
      maxTokens: 8192,
      timeout: 300000, // 5 minutes
      allowedTools: ['read', 'write', 'execute'],
      ...config,
    } as GeminiSessionConfig;

    // Set up persistence path
    this.persistencePath = join(homedir(), '.whatsite-bot', 'gemini-sessions');
    this.ensurePersistenceDirectory();

    // Load existing sessions
    this.loadPersistedSessions();
  }

  /**
   * Initialize Gemini runner for workspace
   */
  initialize(workspace: Workspace): void {
    console.log(`[GeminiRunner] Initializing for workspace: ${workspace.id}`);

    // Ensure workspace directory exists
    if (!existsSync(workspace.path)) {
      mkdirSync(workspace.path, { recursive: true });
    }

    // Set up workspace-specific configuration
    this.defaultConfig.workspace = workspace;
    this.defaultConfig.workingDirectory = workspace.path;

    console.log(`[GeminiRunner] Initialized for workspace at: ${workspace.path}`);
  }

  /**
   * Start a new Gemini session
   */
  startSession(config: GeminiSessionConfig): GeminiSession {
    const sessionId = config.sessionId || this.generateSessionId();

    if (this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} already exists`);
    }

    const session: GeminiSession = {
      sessionId,
      workspace: config.workspace,
      startedAt: new Date(),
      isRunning: false,
      config: { ...this.defaultConfig, ...config },
      messages: [],
      state: 'created',
    };

    this.sessions.set(sessionId, session);

    try {
      // Start the gemini-cli process
      this.startGeminiProcess(session);

      session.state = 'active';
      session.isRunning = true;

      // Persist session
      this.persistSession(session);

      this.emit('session-started', session);
      console.log(`[GeminiRunner] Started session: ${sessionId}`);

      return session;
    } catch (error) {
      session.state = 'failed';
      session.error = error instanceof Error ? error.message : 'Unknown error';
      this.persistSession(session);
      throw error;
    }
  }

  /**
   * Send a message to a session
   */
  sendMessage(sessionId: string, message: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (!session.isRunning) {
      throw new Error(`Session ${sessionId} is not running`);
    }

    const geminiMessage: GeminiMessage = {
      id: this.generateMessageId(),
      role: 'user',
      content: message,
      timestamp: new Date(),
    };

    session.messages.push(geminiMessage);

    // Send to gemini-cli process
    if (session.process && session.process.stdin) {
      session.process.stdin.write(message + '\n');
    }

    this.emit('message', sessionId, geminiMessage);
    this.persistSession(session);
  }

  /**
   * Stop a session
   */
  stopSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (session.process) {
      session.process.kill('SIGTERM');
    }

    session.isRunning = false;
    session.state = 'completed';

    this.persistSession(session);
    this.emit('session-ended', sessionId);

    console.log(`[GeminiRunner] Stopped session: ${sessionId}`);
  }

  /**
   * Generate code using project context
   */
  async generateCode(prompt: string, context: ProjectContext): Promise<CodeGeneration> {
    const sessionConfig: GeminiSessionConfig = {
      workspace: context.workspace,
      workingDirectory: context.workspace.path,
      systemPrompt: this.buildSystemPrompt(context),
    };

    const session = this.startSession(sessionConfig);

    try {
      // Send the generation request
      this.sendMessage(session.sessionId, prompt);

      // Wait for response with timeout
      const response = await this.waitForResponse(session.sessionId, 30000);

      // Parse response into code generation result
      const result = this.parseCodeGeneration(response, context);

      this.stopSession(session.sessionId);

      return result;
    } catch (error) {
      this.stopSession(session.sessionId);
      throw error;
    }
  }

  /**
   * Stream response from a session
   */
  streamResponse(sessionId: string, callback: (chunk: string) => void): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Set up streaming listener
    const streamListener = (sid: string, chunk: string) => {
      if (sid === sessionId) {
        callback(chunk);
      }
    };

    this.on('stream', streamListener);

    // Clean up listener when session ends
    const endListener = (sid: string) => {
      if (sid === sessionId) {
        this.off('stream', streamListener);
        this.off('session-ended', endListener);
      }
    };

    this.on('session-ended', endListener);
  }

  /**
   * Handle output from gemini-cli process
   */
  handleOutput(sessionId: string, data: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Process the output
    const lines = data
      .toString()
      .split('\n')
      .filter(line => line.trim());

    for (const line of lines) {
      // Try to parse as JSON message
      try {
        const parsed: unknown = JSON.parse(line);
        if (
          typeof parsed === 'object' &&
          parsed !== null &&
          'type' in parsed &&
          (parsed as { type: unknown }).type === 'message' &&
          'content' in parsed &&
          typeof (parsed as { content: unknown }).content === 'string'
        ) {
          const parsedMessage = parsed as {
            type: string;
            content: string;
            metadata?: Record<string, unknown>;
          };

          const message: GeminiMessage = {
            id: this.generateMessageId(),
            role: 'assistant',
            content: parsedMessage.content,
            timestamp: new Date(),
            metadata: parsedMessage.metadata,
          };

          session.messages.push(message);
          this.emit('message', sessionId, message);
        }
      } catch {
        // Not JSON, treat as plain text
        this.emit('stream', sessionId, line);
      }
    }

    this.emit('output', sessionId, data);
    this.persistSession(session);
  }

  /**
   * Process error from gemini-cli
   */
  processError(sessionId: string, error: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.error = error;
    session.state = 'failed';
    session.isRunning = false;

    this.emit('error', sessionId, error);
    this.persistSession(session);

    console.error(`[GeminiRunner] Session ${sessionId} error:`, error);
  }

  /**
   * Generate project structure
   */
  async generateProjectStructure(
    prompt: string,
    project: WorkspaceProject
  ): Promise<ProjectStructure> {
    const context: ProjectContext = {
      project,
      workspace: this.defaultConfig.workspace,
      files: [],
      requirements: prompt,
    };

    const codeGeneration = await this.generateCode(
      `Generate a complete project structure for: ${prompt}`,
      context
    );

    return {
      files: codeGeneration.files.map(f => ({
        path: f.path,
        content: f.content,
        type: f.type === 'directory' ? 'directory' : 'file',
        description: f.description,
      })),
      config: project.config,
      setupInstructions: [`Generated project structure for ${project.name}`],
      dependencies: [],
    };
  }

  /**
   * Generate code files
   */
  async generateCodeFiles(
    prompt: string,
    context: CodeGenerationContext
  ): Promise<GeneratedFile[]> {
    const projectContext: ProjectContext = {
      project: context.project,
      workspace: this.defaultConfig.workspace,
      files: context.existingFiles.map((f: WorkspaceFile) => f.path),
      requirements: context.requirements,
      constraints: context.constraints,
    };

    const codeGeneration = await this.generateCode(prompt, projectContext);
    return codeGeneration.files;
  }

  /**
   * Update existing code
   */
  async updateCode(filePath: string, content: string, instructions: string): Promise<string> {
    const prompt = `Update the following code file according to the instructions:
    
File: ${filePath}
Content:
${content}

Instructions: ${instructions}

Please provide the updated code.`;

    const context: ProjectContext = {
      project: this.defaultConfig.workspace.project!,
      workspace: this.defaultConfig.workspace,
      files: [filePath],
      requirements: instructions,
    };

    const result = await this.generateCode(prompt, context);
    return result.code;
  }

  /**
   * Analyze code for improvements
   */
  async analyzeCode(filePath: string, content: string): Promise<CodeAnalysis> {
    const prompt = `Analyze the following code and provide quality assessment:

File: ${filePath}
Content:
${content}

Please provide:
1. Quality score (0-100)
2. Issues found
3. Improvement suggestions
4. Code metrics`;

    const context: ProjectContext = {
      project: this.defaultConfig.workspace.project!,
      workspace: this.defaultConfig.workspace,
      files: [filePath],
      requirements: 'Code analysis',
    };

    const result = await this.generateCode(prompt, context);

    // Parse the analysis result (simplified)
    return {
      qualityScore: 75, // Would be parsed from result
      issues: [],
      suggestions: [result.description],
      metrics: {
        linesOfCode: content.split('\n').length,
        complexity: 5,
        maintainability: 80,
      },
    };
  }

  /**
   * Generate documentation
   */
  async generateDocumentation(project: WorkspaceProject): Promise<string> {
    const prompt = `Generate comprehensive documentation for the following project:

Project: ${project.name}
Description: ${project.description}
Type: ${project.type}
Stack: ${JSON.stringify(project.stack)}

Please provide detailed documentation including:
1. Project overview
2. Setup instructions
3. Usage guide
4. API documentation (if applicable)
5. Contributing guidelines`;

    const context: ProjectContext = {
      project,
      workspace: this.defaultConfig.workspace,
      files: [],
      requirements: 'Generate documentation',
    };

    const result = await this.generateCode(prompt, context);
    return result.code;
  }

  /**
   * Fix code issues
   */
  async fixCodeIssues(filePath: string, content: string, issues: string[]): Promise<string> {
    const prompt = `Fix the following issues in the code:

File: ${filePath}
Content:
${content}

Issues to fix:
${issues.map((issue, i) => `${i + 1}. ${issue}`).join('\n')}

Please provide the corrected code.`;

    const context: ProjectContext = {
      project: this.defaultConfig.workspace.project!,
      workspace: this.defaultConfig.workspace,
      files: [filePath],
      requirements: 'Fix code issues',
    };

    const result = await this.generateCode(prompt, context);
    return result.code;
  }

  /**
   * Suggest improvements
   */
  async suggestImprovements(project: WorkspaceProject): Promise<Improvement[]> {
    const prompt = `Suggest improvements for the following project:

Project: ${project.name}
Description: ${project.description}
Type: ${project.type}
Stack: ${JSON.stringify(project.stack)}

Please provide specific improvement suggestions covering:
1. Performance optimizations
2. Security enhancements
3. Code quality improvements
4. Architecture suggestions`;

    const context: ProjectContext = {
      project,
      workspace: this.defaultConfig.workspace,
      files: [],
      requirements: 'Suggest improvements',
    };

    const result = await this.generateCode(prompt, context);

    // Parse suggestions (simplified)
    return [
      {
        type: 'performance',
        title: 'Performance Optimization',
        description: result.description,
        steps: ['Analyze performance bottlenecks', 'Implement optimizations'],
        priority: 'medium',
        effort: 'medium',
      },
    ];
  }

  /**
   * Start gemini-cli process
   */
  private startGeminiProcess(session: GeminiSession): void {
    const args: string[] = [
      '--model',
      session.config.model || 'gemini-pro',
      '--temperature',
      (session.config.temperature || 0.7).toString(),
      '--max-tokens',
      (session.config.maxTokens || 8192).toString(),
      '--working-directory',
      session.config.workingDirectory || process.cwd(),
      '--json-output',
    ];

    if (session.config.systemPrompt) {
      args.push('--system-prompt', session.config.systemPrompt);
    }

    const childProcess: ChildProcess = spawn(session.config.geminiCliPath || 'gemini-cli', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: session.config.workingDirectory,
    });

    session.process = childProcess;

    // Set up process event handlers
    childProcess.stdout?.on('data', (data: Buffer) => {
      this.handleOutput(session.sessionId, data.toString());
    });

    childProcess.stderr?.on('data', (data: Buffer) => {
      this.processError(session.sessionId, data.toString());
    });

    childProcess.on('close', (code: number | null) => {
      session.isRunning = false;
      session.state = code === 0 ? 'completed' : 'failed';
      if (code !== 0) {
        session.error = `Process exited with code ${code}`;
      }
      this.persistSession(session);
      this.emit('session-ended', session.sessionId);
    });

    childProcess.on('error', (error: Error) => {
      this.processError(session.sessionId, error.message);
    });

    // Set up timeout
    if (session.config.timeout) {
      setTimeout(() => {
        if (session.isRunning) {
          session.state = 'timeout';
          this.processError(session.sessionId, 'Session timeout');
          this.stopSession(session.sessionId);
        }
      }, session.config.timeout);
    }
  }

  /**
   * Build system prompt for context
   */
  private buildSystemPrompt(context: ProjectContext): string {
    return `You are an AI assistant specialized in ${context.project.type} development.

Project: ${context.project.name}
Description: ${context.project.description}
Technology Stack: ${JSON.stringify(context.project.stack)}

Working Directory: ${context.workspace.path}
Requirements: ${context.requirements}

Please provide high-quality, well-structured code that follows best practices.`;
  }

  /**
   * Wait for response from session
   */
  private async waitForResponse(sessionId: string, timeout: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const session = this.sessions.get(sessionId);
      if (!session) {
        reject(new Error(`Session ${sessionId} not found`));
        return;
      }

      let response = '';
      const timer = setTimeout(() => {
        reject(new Error('Response timeout'));
      }, timeout);

      const messageListener = (sid: string, message: GeminiMessage) => {
        if (sid === sessionId && message.role === 'assistant') {
          response += message.content;
        }
      };

      const endListener = (sid: string) => {
        if (sid === sessionId) {
          clearTimeout(timer);
          this.off('message', messageListener);
          this.off('session-ended', endListener);
          resolve(response);
        }
      };

      this.on('message', messageListener);
      this.on('session-ended', endListener);
    });
  }

  /**
   * Parse code generation response
   */
  private parseCodeGeneration(response: string, context: ProjectContext): CodeGeneration {
    // Simplified parsing - in a real implementation, this would be more sophisticated
    const lines = response.split('\n');
    const codeLines = lines.filter(line => !line.startsWith('//') && line.trim());

    // Use context to determine appropriate file extension
    const extension = context.project.type === 'react' ? 'jsx' : 'js';
    const filename = `generated.${extension}`;

    return {
      code: codeLines.join('\n'),
      description: 'Generated code',
      files: [
        {
          path: filename,
          content: codeLines.join('\n'),
          description: 'Generated code file',
          type: context.project.type,
        },
      ],
      success: true,
    };
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `gemini_${Date.now()}_${++this.sessionCounter}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Ensure persistence directory exists
   */
  private ensurePersistenceDirectory(): void {
    if (!existsSync(this.persistencePath)) {
      mkdirSync(this.persistencePath, { recursive: true });
    }
  }

  /**
   * Persist session to disk
   */
  private persistSession(session: GeminiSession): void {
    try {
      const sessionFile = join(this.persistencePath, `${session.sessionId}.json`);
      const sessionData = {
        ...session,
        process: undefined, // Don't serialize process
      };
      writeFileSync(sessionFile, JSON.stringify(sessionData, null, 2));
    } catch (error) {
      console.error(`[GeminiRunner] Failed to persist session ${session.sessionId}:`, error);
    }
  }

  /**
   * Load persisted sessions
   */
  private loadPersistedSessions(): void {
    try {
      const files = readdirSync(this.persistencePath);
      for (const file of files) {
        if (typeof file === 'string' && file.endsWith('.json')) {
          const sessionFile = join(this.persistencePath, file);
          const sessionData: unknown = JSON.parse(readFileSync(sessionFile, 'utf8'));

          // Type guard to ensure sessionData has the required properties
          if (
            typeof sessionData === 'object' &&
            sessionData !== null &&
            'sessionId' in sessionData &&
            typeof (sessionData as { sessionId: unknown }).sessionId === 'string'
          ) {
            const typedSessionData = sessionData as GeminiSession;

            // Restore session but mark as not running
            typedSessionData.isRunning = false;
            typedSessionData.process = undefined;

            this.sessions.set(typedSessionData.sessionId, typedSessionData);
          }
        }
      }

      console.log(`[GeminiRunner] Loaded ${this.sessions.size} persisted sessions`);
    } catch (error) {
      console.error('[GeminiRunner] Failed to load persisted sessions:', error);
    }
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): GeminiSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all sessions
   */
  getSessions(): GeminiSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): GeminiSession[] {
    return Array.from(this.sessions.values()).filter(s => s.isRunning);
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions = Array.from(this.sessions.values()).filter(session => {
      const sessionAge = now - session.startedAt.getTime();
      return sessionAge > (session.config.timeout || 300000);
    });

    for (const session of expiredSessions) {
      if (session.isRunning) {
        this.stopSession(session.sessionId);
      }
      this.sessions.delete(session.sessionId);
    }

    console.log(`[GeminiRunner] Cleaned up ${expiredSessions.length} expired sessions`);
  }
}
