import { Readable } from 'stream';

import { OpenAI } from 'openai';

// TODO: Import from core package when available
const config = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
  },
  google: {
    apiKey: process.env.GOOGLE_API_KEY || '',
  },
};

export interface AIIntegrationConfig {
  openaiApiKey: string;
  geminiApiKey: string;
  preferredProvider: 'openai' | 'gemini';
}

export class AIIntegration {
  private openai: OpenAI;
  private config: AIIntegrationConfig;

  constructor(customConfig?: Partial<AIIntegrationConfig>) {
    this.config = {
      openaiApiKey: config.openai.apiKey,
      geminiApiKey: config.google.apiKey,
      preferredProvider: 'gemini',
      ...customConfig,
    };

    this.openai = new OpenAI({
      apiKey: this.config.openaiApiKey,
    });
  }

  /**
   * Transcribe audio using OpenAI Whisper
   */
  async transcribeAudio(audioUrl: string): Promise<string> {
    try {
      console.log('Transcribing audio with OpenAI Whisper...');

      // Fetch audio file
      const response = await fetch(audioUrl);
      const audioBuffer = await response.arrayBuffer();
      const audioBufferNode = Buffer.from(audioBuffer);

      // Create a Readable stream from the buffer for OpenAI
      const audioStream = Readable.from(audioBufferNode);

      const transcription = await this.openai.audio.transcriptions.create({
        file: audioStream as unknown as File,
        model: 'whisper-1',
        response_format: 'text',
      });

      console.log('Audio transcription completed successfully');
      return transcription;
    } catch (error) {
      console.error('Audio transcription failed:', error);
      throw new Error('Failed to transcribe audio');
    }
  }

  /**
   * Generate text using preferred AI provider
   */
  async generateText(
    prompt: string,
    options?: {
      maxTokens?: number;
      temperature?: number;
    }
  ): Promise<string> {
    const { maxTokens = 1000, temperature = 0.7 } = options || {};

    if (this.config.preferredProvider === 'openai') {
      return this.generateWithOpenAI(prompt, maxTokens, temperature);
    } else {
      // Fallback to OpenAI if Gemini is not available
      return this.generateWithOpenAI(prompt, maxTokens, temperature);
    }
  }

  /**
   * Generate text using OpenAI
   */
  private async generateWithOpenAI(
    prompt: string,
    maxTokens: number,
    temperature: number
  ): Promise<string> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: maxTokens,
        temperature,
      });

      return completion.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenAI generation failed:', error);
      throw new Error('Failed to generate text with OpenAI');
    }
  }

  /**
   * Analyze code for improvements
   */
  async analyzeCode(code: string): Promise<{
    suggestions: string[];
    improvements: string[];
    issues: string[];
  }> {
    const analysisPrompt = `Analyze this HTML/CSS/JavaScript code and provide:
1. Suggestions for improvement
2. Potential issues
3. Best practices recommendations

Code:
${code}

Please provide a structured analysis.`;

    try {
      const analysis = await this.generateText(analysisPrompt);

      // Simple parsing - in a real implementation, you'd want more sophisticated parsing
      return {
        suggestions: [analysis],
        improvements: [],
        issues: [],
      };
    } catch (error) {
      console.error('Code analysis failed:', error);
      return {
        suggestions: [],
        improvements: [],
        issues: ['Failed to analyze code'],
      };
    }
  }
}
