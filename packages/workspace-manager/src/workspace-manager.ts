/**
 * WorkspaceManager implementation with git worktree support
 * Based on Cyrus workspace management patterns adapted for WhatsApp Website Bot
 */

import { spawn } from 'child_process';
import { mkdir, rm, readdir, readFile, writeFile, stat, access } from 'fs/promises';
import { join, dirname } from 'path';
import { existsSync } from 'fs';
import { randomUUID } from 'crypto';

import type {
  WorkspaceManager as IWorkspaceManager,
  Workspace,
  WorkspaceOptions,
  WorkspaceProject,
  GitWorktreeConfig,
  BuildResult,
  DeploymentResult,
  WorkspaceStatus,
  WorkspaceFile,
  GitStatus,
  ProjectStatus,
  TechStack,
  ProjectConfig,
  User,
} from '@whatsite-bot/core';
import {
  WorkspaceError,
  WorkspaceCreationError,
  WorkspaceAccessError,
  GitError,
  GitRepositoryNotFoundError,
  GitCloneError,
} from '@whatsite-bot/core';

/**
 * Configuration options for WorkspaceManager
 */
export interface WorkspaceManagerConfig {
  /** Base directory for all workspaces */
  baseWorkspaceDir: string;
  /** Default git remote URL */
  defaultGitRemote?: string;
  /** Default branch name */
  defaultBranch: string;
  /** Maximum number of workspaces per user */
  maxWorkspacesPerUser: number;
  /** Workspace timeout in milliseconds */
  workspaceTimeout: number;
  /** Git configuration */
  gitConfig: {
    /** Git user name */
    userName: string;
    /** Git user email */
    userEmail: string;
    /** Git authentication token */
    authToken?: string;
  };
}

/**
 * WorkspaceManager class implementing git worktree support
 */
export class WorkspaceManager implements IWorkspaceManager {
  private config: WorkspaceManagerConfig;
  private workspaces: Map<string, Workspace> = new Map();
  private userWorkspaces: Map<string, Set<string>> = new Map();

  constructor(config: WorkspaceManagerConfig) {
    this.config = config;
    void this.ensureBaseDirectoryExists();
  }

  /**
   * Create a new workspace
   */
  async createWorkspace(options: WorkspaceOptions): Promise<Workspace> {
    try {
      // Validate user workspace limits
      await this.validateUserWorkspaceLimit(options.user.phoneNumber);

      const workspace: Workspace = {
        id: randomUUID(),
        path: '',
        isGitWorktree: false,
        type: options.type,
        user: options.user,
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        isActive: true,
        metadata: options.metadata || {},
      };

      // Determine workspace path
      const workspaceName = this.generateWorkspaceName(options.user.phoneNumber, workspace.id);
      workspace.path = join(this.config.baseWorkspaceDir, workspaceName);

      // Create workspace directory
      await mkdir(workspace.path, { recursive: true });

      // Initialize git repository if needed
      if (options.gitUrl) {
        await this.cloneRepository(workspace, options.gitUrl, options.branch);
      } else {
        await this.initializeGitRepository(workspace);
      }

      // Initialize project if provided
      if (options.project) {
        const fullProject = this.completeProjectConfig(options.project);
        workspace.project = fullProject;
        await this.initializeProject(workspace, fullProject);
      }

      // Store workspace
      this.workspaces.set(workspace.id, workspace);
      this.addWorkspaceToUser(options.user.phoneNumber, workspace.id);

      return workspace;
    } catch (error) {
      throw new WorkspaceCreationError(
        `Failed to create workspace: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { options, error },
      );
    }
  }

  /**
   * Get workspace by ID
   */
  getWorkspace(id: string): Promise<Workspace | null> {
    const workspace = this.workspaces.get(id);
    if (workspace) {
      // Update last accessed time
      workspace.lastAccessedAt = new Date();
      return Promise.resolve(workspace);
    }
    return Promise.resolve(null);
  }

  /**
   * Get workspaces by user
   */
  getWorkspacesByUser(phoneNumber: string): Promise<Workspace[]> {
    const userWorkspaceIds = this.userWorkspaces.get(phoneNumber) || new Set();
    const workspaces: Workspace[] = [];

    for (const workspaceId of userWorkspaceIds) {
      const workspace = this.workspaces.get(workspaceId);
      if (workspace) {
        workspaces.push(workspace);
      }
    }

    return Promise.resolve(
      workspaces.sort((a, b) => b.lastAccessedAt.getTime() - a.lastAccessedAt.getTime()),
    );
  }

  /**
   * Update workspace
   */
  updateWorkspace(workspace: Workspace): Promise<void> {
    if (!this.workspaces.has(workspace.id)) {
      throw new WorkspaceError(`Workspace ${workspace.id} not found`, 'WORKSPACE_NOT_FOUND', {
        workspaceId: workspace.id,
      });
    }

    workspace.lastAccessedAt = new Date();
    this.workspaces.set(workspace.id, workspace);
    return Promise.resolve();
  }

  /**
   * Delete workspace
   */
  async deleteWorkspace(id: string): Promise<void> {
    const workspace = this.workspaces.get(id);
    if (!workspace) {
      throw new WorkspaceError(`Workspace ${id} not found`, 'WORKSPACE_NOT_FOUND', {
        workspaceId: id,
      });
    }

    try {
      // Clean up workspace
      await this.cleanupWorkspace(workspace);

      // Remove from storage
      this.workspaces.delete(id);
      this.removeWorkspaceFromUser(workspace.user.phoneNumber, id);
    } catch (error) {
      throw new WorkspaceError(
        `Failed to delete workspace ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WORKSPACE_DELETE_FAILED',
        { workspaceId: id, error },
      );
    }
  }

  /**
   * Create git worktree
   */
  async createGitWorktree(config: GitWorktreeConfig): Promise<Workspace> {
    try {
      // Validate base repository exists
      if (!existsSync(config.basePath)) {
        throw new GitRepositoryNotFoundError(config.basePath);
      }

      // Create worktree directory
      await mkdir(dirname(config.worktreePath), { recursive: true });

      // Create git worktree
      await this.executeGitCommand(
        ['worktree', 'add', config.worktreePath, config.branch],
        config.basePath,
      );

      // Create workspace object
      const workspace: Workspace = {
        id: randomUUID(),
        path: config.worktreePath,
        isGitWorktree: true,
        basePath: config.basePath,
        branch: config.branch,
        type: 'persistent',
        user: { phoneNumber: 'system' } as User, // System workspace
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        isActive: true,
        metadata: { remoteUrl: config.remoteUrl },
      };

      // Store workspace
      this.workspaces.set(workspace.id, workspace);

      return workspace;
    } catch (error) {
      throw new GitError(
        `Failed to create git worktree: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GIT_WORKTREE_CREATION_FAILED',
        { config, error },
      );
    }
  }

  /**
   * Initialize project in workspace
   */
  async initializeProject(workspace: Workspace, project: WorkspaceProject): Promise<void> {
    try {
      // Create project directory structure
      await this.createProjectStructure(workspace, project);

      // Initialize package.json if needed
      if (project.type !== 'static') {
        await this.initializePackageJson(workspace, project);
      }

      // Create initial files
      await this.createInitialFiles(workspace, project);

      // Install dependencies if needed
      if (project.stack.packageManager && project.stack.dependencies) {
        await this.installDependencies(workspace, project);
      }

      // Update workspace with project
      workspace.project = project;
      await this.updateWorkspace(workspace);
    } catch (error) {
      throw new WorkspaceError(
        `Failed to initialize project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PROJECT_INITIALIZATION_FAILED',
        { workspaceId: workspace.id, project, error },
      );
    }
  }

  /**
   * Clone repository into workspace
   */
  async cloneRepository(workspace: Workspace, gitUrl: string, branch?: string): Promise<void> {
    try {
      // Clean workspace directory if it exists
      if (existsSync(workspace.path)) {
        await rm(workspace.path, { recursive: true, force: true });
      }

      // Clone repository
      const cloneArgs = ['clone'];
      if (branch) {
        cloneArgs.push('--branch', branch);
      }
      cloneArgs.push(gitUrl, workspace.path);

      await this.executeGitCommand(cloneArgs, process.cwd());

      // Configure git
      await this.configureGitRepository(workspace);

      // Update workspace metadata
      workspace.metadata = {
        ...workspace.metadata,
        gitUrl,
        branch: branch || 'main',
      };
    } catch (error) {
      throw new GitCloneError(gitUrl, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Build project in workspace
   */
  async buildProject(workspace: Workspace): Promise<BuildResult> {
    const startTime = Date.now();

    try {
      if (!workspace.project?.build) {
        throw new WorkspaceError('No build configuration found', 'NO_BUILD_CONFIG', {
          workspaceId: workspace.id,
        });
      }

      const buildConfig = workspace.project.build;
      const buildOutput = await this.executeCommand(
        buildConfig.command,
        workspace.path,
        buildConfig.env,
      );

      return {
        success: true,
        output: buildOutput,
        duration: Date.now() - startTime,
        files: await this.getBuildFiles(workspace, buildConfig.outputDir),
      };
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Deploy project from workspace
   */
  deployProject(workspace: Workspace): Promise<DeploymentResult> {
    const startTime = Date.now();

    try {
      if (!workspace.project?.deployment) {
        throw new WorkspaceError('No deployment configuration found', 'NO_DEPLOYMENT_CONFIG', {
          workspaceId: workspace.id,
        });
      }

      // This is a placeholder - actual deployment would depend on platform
      const deploymentConfig = workspace.project.deployment;

      return Promise.resolve({
        success: true,
        url: `https://${workspace.id}.example.com`,
        duration: Date.now() - startTime,
        metadata: { platform: deploymentConfig.platform },
      });
    } catch (error) {
      return Promise.resolve({
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Clean up workspace
   */
  async cleanupWorkspace(workspace: Workspace): Promise<void> {
    try {
      if (workspace.isGitWorktree && workspace.basePath) {
        // Remove git worktree
        await this.executeGitCommand(['worktree', 'remove', workspace.path], workspace.basePath);
      } else {
        // Remove directory
        if (existsSync(workspace.path)) {
          await rm(workspace.path, { recursive: true, force: true });
        }
      }
    } catch (error) {
      throw new WorkspaceError(
        `Failed to cleanup workspace: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WORKSPACE_CLEANUP_FAILED',
        { workspaceId: workspace.id, error },
      );
    }
  }

  /**
   * Get workspace status
   */
  async getWorkspaceStatus(workspace: Workspace): Promise<WorkspaceStatus> {
    try {
      const exists = existsSync(workspace.path);
      const accessible = exists && (await this.isDirectoryAccessible(workspace.path));

      let gitStatus: GitStatus | undefined;
      let diskUsage: number | undefined;
      let fileCount: number | undefined;
      let lastModified: Date | undefined;
      let projectStatus: ProjectStatus | undefined;

      if (accessible) {
        // Get git status
        gitStatus = await this.getGitStatus(workspace);

        // Get file system stats
        const stats = await this.getDirectoryStats(workspace.path);
        diskUsage = stats.diskUsage;
        fileCount = stats.fileCount;
        lastModified = stats.lastModified;

        // Get project status
        if (workspace.project) {
          projectStatus = await this.getProjectStatus(workspace);
        }
      }

      return {
        exists,
        accessible,
        git: gitStatus,
        diskUsage,
        fileCount,
        lastModified,
        project: projectStatus,
      };
    } catch (error) {
      throw new WorkspaceError(
        `Failed to get workspace status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WORKSPACE_STATUS_FAILED',
        { workspaceId: workspace.id, error },
      );
    }
  }

  /**
   * List workspace files
   */
  async listWorkspaceFiles(workspace: Workspace, directory?: string): Promise<WorkspaceFile[]> {
    try {
      const targetDir = directory ? join(workspace.path, directory) : workspace.path;

      if (!existsSync(targetDir)) {
        return [];
      }

      const entries = await readdir(targetDir, { withFileTypes: true });
      const files: WorkspaceFile[] = [];

      for (const entry of entries) {
        const fullPath = join(targetDir, entry.name);
        const relativePath = directory ? join(directory, entry.name) : entry.name;
        const stats = await stat(fullPath);

        files.push({
          name: entry.name,
          path: relativePath,
          type: entry.isDirectory() ? 'directory' : 'file',
          size: entry.isFile() ? stats.size : undefined,
          lastModified: stats.mtime,
          permissions: stats.mode.toString(8),
          isBinary: entry.isFile() ? await this.isBinaryFile(fullPath) : false,
        });
      }

      return files.sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      throw new WorkspaceError(
        `Failed to list workspace files: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WORKSPACE_LIST_FILES_FAILED',
        { workspaceId: workspace.id, directory, error },
      );
    }
  }

  /**
   * Read workspace file
   */
  async readWorkspaceFile(workspace: Workspace, filePath: string): Promise<string> {
    try {
      const fullPath = join(workspace.path, filePath);
      return await readFile(fullPath, 'utf-8');
    } catch (error) {
      throw new WorkspaceAccessError(
        filePath,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Write workspace file
   */
  async writeWorkspaceFile(workspace: Workspace, filePath: string, content: string): Promise<void> {
    try {
      const fullPath = join(workspace.path, filePath);
      await mkdir(dirname(fullPath), { recursive: true });
      await writeFile(fullPath, content, 'utf-8');
    } catch (error) {
      throw new WorkspaceAccessError(
        filePath,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Delete workspace file
   */
  async deleteWorkspaceFile(workspace: Workspace, filePath: string): Promise<void> {
    try {
      const fullPath = join(workspace.path, filePath);
      await rm(fullPath, { recursive: true, force: true });
    } catch (error) {
      throw new WorkspaceAccessError(
        filePath,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  // Private helper methods

  private async ensureBaseDirectoryExists(): Promise<void> {
    if (!existsSync(this.config.baseWorkspaceDir)) {
      await mkdir(this.config.baseWorkspaceDir, { recursive: true });
    }
  }

  private validateUserWorkspaceLimit(phoneNumber: string): Promise<void> {
    const userWorkspaces = this.userWorkspaces.get(phoneNumber) || new Set();
    if (userWorkspaces.size >= this.config.maxWorkspacesPerUser) {
      throw new WorkspaceError(
        `Maximum workspace limit (${this.config.maxWorkspacesPerUser}) reached for user`,
        'WORKSPACE_LIMIT_EXCEEDED',
        { phoneNumber, currentCount: userWorkspaces.size },
      );
    }
    return Promise.resolve();
  }

  private generateWorkspaceName(phoneNumber: string, workspaceId: string): string {
    const sanitizedPhone = phoneNumber.replace(/[^0-9]/g, '');
    const shortId = workspaceId.split('-')[0];
    return `ws_${sanitizedPhone}_${shortId}`;
  }

  private addWorkspaceToUser(phoneNumber: string, workspaceId: string): void {
    const userWorkspaces = this.userWorkspaces.get(phoneNumber) || new Set();
    userWorkspaces.add(workspaceId);
    this.userWorkspaces.set(phoneNumber, userWorkspaces);
  }

  private removeWorkspaceFromUser(phoneNumber: string, workspaceId: string): void {
    const userWorkspaces = this.userWorkspaces.get(phoneNumber);
    if (userWorkspaces) {
      userWorkspaces.delete(workspaceId);
      if (userWorkspaces.size === 0) {
        this.userWorkspaces.delete(phoneNumber);
      }
    }
  }

  private async initializeGitRepository(workspace: Workspace): Promise<void> {
    await this.executeGitCommand(['init'], workspace.path);
    await this.configureGitRepository(workspace);
  }

  private async configureGitRepository(workspace: Workspace): Promise<void> {
    await this.executeGitCommand(
      ['config', 'user.name', this.config.gitConfig.userName],
      workspace.path,
    );
    await this.executeGitCommand(
      ['config', 'user.email', this.config.gitConfig.userEmail],
      workspace.path,
    );
  }

  private async executeGitCommand(args: string[], cwd: string): Promise<string> {
    return this.executeCommand(`git ${args.join(' ')}`, cwd);
  }

  private async executeCommand(
    command: string,
    cwd: string,
    env?: Record<string, string>,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const childProcess = spawn('sh', ['-c', command], {
        cwd,
        env: { ...process.env, ...env },
        stdio: 'pipe',
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      childProcess.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      childProcess.on('close', (code: number | null) => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(stderr || `Command failed with code ${code}`));
        }
      });

      childProcess.on('error', (error: Error) => {
        reject(error);
      });
    });
  }

  private completeProjectConfig(project: Partial<WorkspaceProject>): WorkspaceProject {
    const defaultStack: TechStack = {
      framework: 'vanilla',
      styling: 'css',
      buildTool: 'none',
      packageManager: 'npm',
      runtime: 'browser',
      dependencies: [],
    };

    const defaultConfig: ProjectConfig = {
      srcDir: 'src',
      buildDir: 'dist',
      publicDir: 'public',
      entryPoint: 'index.html',
      configFiles: [],
      env: {},
    };

    return {
      name: project.name || 'untitled',
      description: project.description,
      type: project.type || 'static',
      stack: { ...defaultStack, ...project.stack },
      config: { ...defaultConfig, ...project.config },
      build: project.build,
      deployment: project.deployment,
    };
  }

  private async createProjectStructure(
    workspace: Workspace,
    project: WorkspaceProject,
  ): Promise<void> {
    const directories = [project.config.srcDir, project.config.buildDir, project.config.publicDir];

    for (const dir of directories) {
      await mkdir(join(workspace.path, dir), { recursive: true });
    }
  }

  private async initializePackageJson(
    workspace: Workspace,
    project: WorkspaceProject,
  ): Promise<void> {
    const packageJson = {
      name: project.name,
      version: '1.0.0',
      description: project.description || '',
      main: project.config.entryPoint,
      scripts: {
        build: project.build?.command || 'echo "No build command configured"',
        start: 'serve dist',
        dev: 'serve src',
      },
      dependencies: {},
      devDependencies: {},
    };

    await this.writeWorkspaceFile(workspace, 'package.json', JSON.stringify(packageJson, null, 2));
  }

  private async createInitialFiles(workspace: Workspace, project: WorkspaceProject): Promise<void> {
    // Create basic HTML file
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project.name}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>Welcome to ${project.name}</h1>
    <p>${project.description || 'Your website is ready!'}</p>
    <script src="script.js"></script>
</body>
</html>`;

    await this.writeWorkspaceFile(
      workspace,
      join(project.config.srcDir, 'index.html'),
      htmlContent,
    );

    // Create basic CSS file
    const cssContent = `/* ${project.name} Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}`;

    await this.writeWorkspaceFile(workspace, join(project.config.srcDir, 'styles.css'), cssContent);

    // Create basic JavaScript file
    const jsContent = `// ${project.name} Script
console.log('Welcome to ${project.name}!');

// Add your JavaScript code here
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
});`;

    await this.writeWorkspaceFile(workspace, join(project.config.srcDir, 'script.js'), jsContent);
  }

  private async installDependencies(
    workspace: Workspace,
    project: WorkspaceProject,
  ): Promise<void> {
    if (!project.stack.packageManager || !project.stack.dependencies?.length) {
      return;
    }

    const installCommand = `${project.stack.packageManager} install ${project.stack.dependencies.join(' ')}`;
    await this.executeCommand(installCommand, workspace.path);
  }

  private async getBuildFiles(workspace: Workspace, outputDir: string): Promise<string[]> {
    const buildPath = join(workspace.path, outputDir);
    if (!existsSync(buildPath)) {
      return [];
    }

    const files = await readdir(buildPath, { recursive: true });
    return files.filter(file => typeof file === 'string');
  }

  private async isDirectoryAccessible(path: string): Promise<boolean> {
    try {
      await access(path);
      return true;
    } catch {
      return false;
    }
  }

  private async getGitStatus(workspace: Workspace): Promise<GitStatus> {
    try {
      const statusOutput = await this.executeGitCommand(['status', '--porcelain'], workspace.path);
      const branchOutput = await this.executeGitCommand(
        ['branch', '--show-current'],
        workspace.path,
      );

      const modifiedFiles: string[] = [];
      const untrackedFiles: string[] = [];
      const stagedFiles: string[] = [];

      statusOutput.split('\n').forEach(line => {
        if (line.trim()) {
          const status = line.substring(0, 2);
          const file = line.substring(3);

          if (status.includes('M')) modifiedFiles.push(file);
          if (status.includes('?')) untrackedFiles.push(file);
          if (status[0] !== ' ') stagedFiles.push(file);
        }
      });

      return {
        branch: branchOutput.trim(),
        isClean: statusOutput.trim() === '',
        modifiedFiles,
        untrackedFiles,
        stagedFiles,
      };
    } catch {
      return {
        isClean: false,
        modifiedFiles: [],
        untrackedFiles: [],
        stagedFiles: [],
      };
    }
  }

  private async getDirectoryStats(path: string): Promise<{
    diskUsage: number;
    fileCount: number;
    lastModified: Date;
  }> {
    let diskUsage = 0;
    let fileCount = 0;
    let lastModified = new Date(0);

    const traverse = async (currentPath: string): Promise<void> => {
      const entries = await readdir(currentPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(currentPath, entry.name);
        const stats = await stat(fullPath);

        if (entry.isFile()) {
          diskUsage += stats.size;
          fileCount++;
          if (stats.mtime > lastModified) {
            lastModified = stats.mtime;
          }
        } else if (entry.isDirectory()) {
          await traverse(fullPath);
        }
      }
    };

    await traverse(path);

    return { diskUsage, fileCount, lastModified };
  }

  private getProjectStatus(workspace: Workspace): Promise<ProjectStatus> {
    const project = workspace.project!;

    // Check if project is properly initialized
    const initialized = existsSync(join(workspace.path, project.config.srcDir));

    // Check if project can be built
    const buildable = project.build ? existsSync(join(workspace.path, 'package.json')) : true;

    // Check if project can be deployed
    const deployable = project.deployment ? buildable : true;

    return Promise.resolve({
      initialized,
      buildable,
      deployable,
      missingDependencies: [],
      configIssues: [],
    });
  }

  private async isBinaryFile(filePath: string): Promise<boolean> {
    try {
      const buffer = await readFile(filePath);
      // Simple heuristic: if file contains null bytes, it's likely binary
      return buffer.includes(0);
    } catch {
      return false;
    }
  }
}
