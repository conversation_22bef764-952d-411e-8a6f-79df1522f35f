{"name": "@whatsite-bot/core", "version": "0.1.0", "description": "Core types and utilities for WhatsApp Website Bot", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "exports": {".": "./dist/index.js", "./workspace": "./dist/workspace.js", "./types": "./dist/types.js", "./errors": "./dist/errors.js", "./webhook": "./dist/webhook.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "lint": "eslint . --ext .ts,.js,.cjs,.mjs", "lint:fix": "pnpm lint --fix", "format": "prettier --write ."}, "dependencies": {"@google/genai": "^0.3.0", "@supabase/supabase-js": "^2.39.0", "dotenv": "^17.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "prettier": "^3.0.0", "eslint": "^8.0.0"}, "publishConfig": {"access": "public"}}