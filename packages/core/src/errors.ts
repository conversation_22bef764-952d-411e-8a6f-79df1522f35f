/**
 * Custom error types for WhatsApp Website Bot
 * Comprehensive error handling for better debugging and user experience
 */

/**
 * Logger interface for error logging
 */
export interface Logger {
  info(message: unknown): void;
  warn(message: unknown): void;
  error(message: unknown): void;
}

/**
 * Base error class for all bot errors
 */
export abstract class BotError extends Error {
  /** Error code for programmatic handling */
  public readonly code: string;
  /** Error category */
  public readonly category: string;
  /** Error severity level */
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  /** Error context data */
  public readonly context?: Record<string, unknown>;
  /** Error timestamp */
  public readonly timestamp: Date;
  /** Whether error is retryable */
  public readonly retryable: boolean;
  /** User-friendly error message */
  public readonly userMessage?: string;

  constructor(
    message: string,
    code: string,
    category: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.category = category;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date();
    this.retryable = retryable;
    this.userMessage = userMessage;

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Convert error to JSON for logging
   */
  toJSON(): ErrorJSON {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      category: this.category,
      severity: this.severity,
      context: this.context,
      timestamp: this.timestamp,
      retryable: this.retryable,
      userMessage: this.userMessage,
      stack: this.stack,
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    return this.userMessage || 'An error occurred. Please try again.';
  }
}

/**
 * Error JSON representation
 */
export interface ErrorJSON {
  name: string;
  message: string;
  code: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, unknown>;
  timestamp: Date;
  retryable: boolean;
  userMessage?: string;
  stack?: string;
}

/**
 * Session-related errors
 */
export class SessionError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'session', 'medium', context, retryable, userMessage);
  }
}

/**
 * Session creation failed
 */
export class SessionCreationError extends SessionError {
  constructor(reason: string, context?: Record<string, unknown>) {
    super(
      `Failed to create session: ${reason}`,
      'SESSION_CREATION_FAILED',
      context,
      true,
      'Unable to start your request. Please try again.',
    );
  }
}

/**
 * Session timeout error
 */
export class SessionTimeoutError extends SessionError {
  constructor(sessionId: string, timeout: number) {
    super(
      `Session ${sessionId} timed out after ${timeout}ms`,
      'SESSION_TIMEOUT',
      { sessionId, timeout },
      true,
      'Your request took too long to process. Please try again with a simpler request.',
    );
  }
}

/**
 * Session not found error
 */
export class SessionNotFoundError extends SessionError {
  constructor(sessionId: string) {
    super(
      `Session ${sessionId} not found`,
      'SESSION_NOT_FOUND',
      { sessionId },
      false,
      'Session not found. Please start a new conversation.',
    );
  }
}

/**
 * Workspace-related errors
 */
export class WorkspaceError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'workspace', 'medium', context, retryable, userMessage);
  }
}

/**
 * Workspace creation failed
 */
export class WorkspaceCreationError extends WorkspaceError {
  constructor(reason: string, context?: Record<string, unknown>) {
    super(
      `Failed to create workspace: ${reason}`,
      'WORKSPACE_CREATION_FAILED',
      context,
      true,
      'Unable to set up your development environment. Please try again.',
    );
  }
}

/**
 * Workspace access error
 */
export class WorkspaceAccessError extends WorkspaceError {
  constructor(workspacePath: string, reason: string) {
    super(
      `Cannot access workspace at ${workspacePath}: ${reason}`,
      'WORKSPACE_ACCESS_ERROR',
      { workspacePath, reason },
      true,
      'Unable to access your workspace. Please try again.',
    );
  }
}

/**
 * Git-related errors
 */
export class GitError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'git', 'medium', context, retryable, userMessage);
  }
}

/**
 * Git repository not found
 */
export class GitRepositoryNotFoundError extends GitError {
  constructor(repoUrl: string) {
    super(
      `Git repository not found: ${repoUrl}`,
      'GIT_REPO_NOT_FOUND',
      { repoUrl },
      false,
      'Repository not found. Please check the URL and try again.',
    );
  }
}

/**
 * Git clone failed
 */
export class GitCloneError extends GitError {
  constructor(repoUrl: string, reason: string) {
    super(
      `Failed to clone repository ${repoUrl}: ${reason}`,
      'GIT_CLONE_FAILED',
      { repoUrl, reason },
      true,
      'Unable to clone repository. Please check the URL and try again.',
    );
  }
}

/**
 * Git authentication error
 */
export class GitAuthenticationError extends GitError {
  constructor(repoUrl: string) {
    super(
      `Git authentication failed for ${repoUrl}`,
      'GIT_AUTH_FAILED',
      { repoUrl },
      false,
      'Authentication failed. Please check your repository permissions.',
    );
  }
}

/**
 * Communication-related errors
 */
export class CommunicationError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'communication', 'medium', context, retryable, userMessage);
  }
}

/**
 * WhatsApp message send failed
 */
export class MessageSendError extends CommunicationError {
  constructor(phoneNumber: string, reason: string) {
    super(
      `Failed to send message to ${phoneNumber}: ${reason}`,
      'MESSAGE_SEND_FAILED',
      { phoneNumber, reason },
      true,
      'Unable to send message. Please try again.',
    );
  }
}

/**
 * Webhook processing error
 */
export class WebhookProcessingError extends CommunicationError {
  constructor(webhookType: string, reason: string, context?: Record<string, unknown>) {
    super(
      `Failed to process webhook ${webhookType}: ${reason}`,
      'WEBHOOK_PROCESSING_FAILED',
      { webhookType, reason, ...context },
      true,
      'Unable to process your request. Please try again.',
    );
  }
}

/**
 * AI/Generation-related errors
 */
export class AIError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'ai', 'medium', context, retryable, userMessage);
  }
}

/**
 * AI service unavailable
 */
export class AIServiceUnavailableError extends AIError {
  constructor(service: string) {
    super(
      `AI service ${service} is unavailable`,
      'AI_SERVICE_UNAVAILABLE',
      { service },
      true,
      'AI service is temporarily unavailable. Please try again later.',
    );
  }
}

/**
 * AI generation failed
 */
export class AIGenerationError extends AIError {
  constructor(task: string, reason: string, context?: Record<string, unknown>) {
    super(
      `AI generation failed for task ${task}: ${reason}`,
      'AI_GENERATION_FAILED',
      { task, reason, ...context },
      true,
      'Unable to generate your request. Please try again with different instructions.',
    );
  }
}

/**
 * AI rate limit exceeded
 */
export class AIRateLimitError extends AIError {
  constructor(service: string, resetTime?: Date) {
    super(
      `AI service ${service} rate limit exceeded`,
      'AI_RATE_LIMIT_EXCEEDED',
      { service, resetTime },
      true,
      'Too many requests. Please wait a moment and try again.',
    );
  }
}

/**
 * Deployment-related errors
 */
export class DeploymentError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'deployment', 'medium', context, retryable, userMessage);
  }
}

/**
 * Deployment failed
 */
export class DeploymentFailedError extends DeploymentError {
  constructor(platform: string, reason: string, context?: Record<string, unknown>) {
    super(
      `Deployment to ${platform} failed: ${reason}`,
      'DEPLOYMENT_FAILED',
      { platform, reason, ...context },
      true,
      'Deployment failed. Please try again.',
    );
  }
}

/**
 * Build error
 */
export class BuildError extends DeploymentError {
  constructor(reason: string, buildLogs?: string[], context?: Record<string, unknown>) {
    super(
      `Build failed: ${reason}`,
      'BUILD_FAILED',
      { reason, buildLogs, ...context },
      true,
      'Build failed. Please check your code and try again.',
    );
  }
}

/**
 * Configuration-related errors
 */
export class ConfigurationError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'configuration', 'high', context, retryable, userMessage);
  }
}

/**
 * Invalid configuration
 */
export class InvalidConfigurationError extends ConfigurationError {
  constructor(configType: string, reason: string, context?: Record<string, unknown>) {
    super(
      `Invalid ${configType} configuration: ${reason}`,
      'INVALID_CONFIGURATION',
      { configType, reason, ...context },
      false,
      'Configuration error. Please contact support.',
    );
  }
}

/**
 * Missing configuration
 */
export class MissingConfigurationError extends ConfigurationError {
  constructor(configKey: string, context?: Record<string, unknown>) {
    super(
      `Missing required configuration: ${configKey}`,
      'MISSING_CONFIGURATION',
      { configKey, ...context },
      false,
      'System configuration error. Please contact support.',
    );
  }
}

/**
 * Validation-related errors
 */
export class ValidationError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'validation', 'low', context, retryable, userMessage);
  }
}

/**
 * Invalid input error
 */
export class InvalidInputError extends ValidationError {
  constructor(input: string, reason: string, context?: Record<string, unknown>) {
    super(
      `Invalid input '${input}': ${reason}`,
      'INVALID_INPUT',
      { input, reason, ...context },
      false,
      `Invalid input: ${reason}. Please try again.`,
    );
  }
}

/**
 * Missing required field error
 */
export class MissingRequiredFieldError extends ValidationError {
  constructor(field: string, context?: Record<string, unknown>) {
    super(
      `Missing required field: ${field}`,
      'MISSING_REQUIRED_FIELD',
      { field, ...context },
      false,
      `Required field '${field}' is missing. Please provide all required information.`,
    );
  }
}

/**
 * System-related errors
 */
export class SystemError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'system', 'critical', context, retryable, userMessage);
  }
}

/**
 * Database connection error
 */
export class DatabaseError extends SystemError {
  constructor(operation: string, reason: string, context?: Record<string, unknown>) {
    super(
      `Database operation '${operation}' failed: ${reason}`,
      'DATABASE_ERROR',
      { operation, reason, ...context },
      true,
      'Database error. Please try again.',
    );
  }
}

/**
 * External service error
 */
export class ExternalServiceError extends SystemError {
  constructor(service: string, reason: string, context?: Record<string, unknown>) {
    super(
      `External service '${service}' error: ${reason}`,
      'EXTERNAL_SERVICE_ERROR',
      { service, reason, ...context },
      true,
      'External service error. Please try again later.',
    );
  }
}

/**
 * Rate limiting error
 */
export class RateLimitError extends SystemError {
  constructor(resource: string, resetTime?: Date, context?: Record<string, unknown>) {
    super(
      `Rate limit exceeded for ${resource}`,
      'RATE_LIMIT_EXCEEDED',
      { resource, resetTime, ...context },
      true,
      'Too many requests. Please wait a moment and try again.',
    );
  }
}

/**
 * Error utility functions
 */
export class ErrorUtils {
  /**
   * Create error from unknown error object
   */
  static fromUnknown(
    error: unknown,
    defaultMessage: string = 'An unknown error occurred',
  ): BotError {
    if (error instanceof BotError) {
      return error;
    }

    if (error instanceof Error) {
      return new SystemError(
        error.message || defaultMessage,
        'UNKNOWN_ERROR',
        { originalError: error.name, stack: error.stack },
        true,
        'An unexpected error occurred. Please try again.',
      );
    }

    return new SystemError(
      defaultMessage,
      'UNKNOWN_ERROR',
      { originalError: error },
      true,
      'An unexpected error occurred. Please try again.',
    );
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(error: Error): boolean {
    return error instanceof BotError && error.retryable;
  }

  /**
   * Get error severity
   */
  static getSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    return error instanceof BotError ? error.severity : 'medium';
  }

  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: Error): string {
    return error instanceof BotError
      ? error.getUserMessage()
      : 'An error occurred. Please try again.';
  }

  /**
   * Log error with appropriate level
   */
  static logError(error: Error, logger: Logger): void {
    if (error instanceof BotError) {
      const logLevel =
        error.severity === 'critical' ? 'error' : error.severity === 'high' ? 'warn' : 'info';
      logger[logLevel](error.toJSON());
    } else {
      logger.error({
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }
  }
}

/**
 * Error handler interface
 */
export interface ErrorHandler {
  /** Handle error */
  handle(error: Error, context?: Record<string, unknown>): Promise<void>;
  /** Check if error should be handled */
  canHandle(error: Error): boolean;
}

/**
 * Error recovery strategy
 */
export interface ErrorRecoveryStrategy {
  /** Attempt to recover from error */
  recover(error: BotError, context?: Record<string, unknown>): Promise<boolean>;
  /** Check if recovery is possible */
  canRecover(error: BotError): boolean;
}
