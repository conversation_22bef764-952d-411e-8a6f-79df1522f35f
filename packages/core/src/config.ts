/**
 * Configuration types for WhatsApp Website Bot
 * Centralized configuration management
 */

/**
 * Main bot configuration
 */
export interface BotConfiguration {
  /** Environment (development, staging, production) */
  environment: 'development' | 'staging' | 'production';

  /** Server configuration */
  server: ServerConfig;

  /** Database configuration */
  database: DatabaseConfig;

  /** External service configurations */
  services: ServiceConfigs;

  /** Security configuration */
  security: SecurityConfig;

  /** Logging configuration */
  logging: LoggingConfig;

  /** Feature flags */
  features: FeatureFlags;
}

/**
 * Server configuration
 */
export interface ServerConfig {
  /** Server host */
  host: string;
  /** Server port */
  port: number;
  /** Base URL */
  baseUrl: string;
  /** CORS configuration */
  cors: CorsConfig;
  /** Rate limiting configuration */
  rateLimit: RateLimitConfig;
}

/**
 * Database configuration
 */
export interface DatabaseConfig {
  /** Database URL */
  url: string;
  /** Connection pool size */
  poolSize: number;
  /** Connection timeout */
  timeout: number;
  /** SSL configuration */
  ssl: boolean;
  /** Migration settings */
  migrations: MigrationConfig;
}

/**
 * External service configurations
 */
export interface ServiceConfigs {
  /** Twilio configuration */
  twilio: TwilioConfig;
  /** OpenAI configuration */
  openai: OpenAIConfig;
  /** Google configuration */
  google: GoogleConfig;
  /** Vercel configuration */
  vercel: VercelConfig;
  /** GitHub configuration */
  github: GitHubConfig;
  /** Supabase configuration */
  supabase: SupabaseConfig;
}

/**
 * Twilio configuration
 */
export interface TwilioConfig {
  /** Account SID */
  accountSid: string;
  /** Auth token */
  authToken: string;
  /** WhatsApp phone number */
  phoneNumber: string;
  /** Webhook URL */
  webhookUrl: string;
}

/**
 * OpenAI configuration
 */
export interface OpenAIConfig {
  /** API key */
  apiKey: string;
  /** Model name */
  model: string;
  /** Max tokens */
  maxTokens: number;
  /** Temperature */
  temperature: number;
}

/**
 * Google configuration
 */
export interface GoogleConfig {
  /** API key */
  apiKey: string;
  /** Gemini model */
  model: string;
  /** Max tokens */
  maxTokens: number;
}

/**
 * Vercel configuration
 */
export interface VercelConfig {
  /** API token */
  token: string;
  /** Team ID */
  teamId?: string;
  /** Project ID */
  projectId?: string;
}

/**
 * GitHub configuration
 */
export interface GitHubConfig {
  /** Personal access token */
  token: string;
  /** Default organization */
  organization?: string;
  /** Webhook secret */
  webhookSecret?: string;
}

/**
 * Supabase configuration
 */
export interface SupabaseConfig {
  /** Project URL */
  url: string;
  /** Anonymous key */
  anonKey: string;
  /** Service role key */
  serviceRoleKey?: string;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  /** JWT secret */
  jwtSecret: string;
  /** Session timeout */
  sessionTimeout: number;
  /** Encryption key */
  encryptionKey: string;
  /** HTTPS enforcement */
  httpsOnly: boolean;
}

/**
 * Logging configuration
 */
export interface LoggingConfig {
  /** Log level */
  level: 'debug' | 'info' | 'warn' | 'error';
  /** Log format */
  format: 'json' | 'text';
  /** Log destination */
  destination: 'console' | 'file' | 'both';
  /** Log file path */
  filePath?: string;
}

/**
 * CORS configuration
 */
export interface CorsConfig {
  /** Allowed origins */
  origins: string[];
  /** Allowed methods */
  methods: string[];
  /** Allowed headers */
  headers: string[];
  /** Credentials allowed */
  credentials: boolean;
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  /** Requests per window */
  maxRequests: number;
  /** Window duration in milliseconds */
  windowMs: number;
  /** Skip successful requests */
  skipSuccessfulRequests: boolean;
}

/**
 * Migration configuration
 */
export interface MigrationConfig {
  /** Auto-run migrations */
  autoRun: boolean;
  /** Migration table name */
  tableName: string;
  /** Migration directory */
  directory: string;
}

/**
 * Feature flags
 */
export interface FeatureFlags {
  /** Enable session persistence */
  sessionPersistence: boolean;
  /** Enable streaming responses */
  streamingResponses: boolean;
  /** Enable code analysis */
  codeAnalysis: boolean;
  /** Enable deployment */
  deployment: boolean;
  /** Enable git integration */
  gitIntegration: boolean;
  /** Enable webhook validation */
  webhookValidation: boolean;
  /** Enable rate limiting */
  rateLimiting: boolean;
  /** Enable error recovery */
  errorRecovery: boolean;
}

/**
 * Configuration manager interface
 */
export interface ConfigurationManager {
  /** Load configuration */
  load(): Promise<BotConfiguration>;
  /** Get configuration value */
  get<T>(key: string): T | undefined;
  /** Set configuration value */
  set<T>(key: string, value: T): void;
  /** Validate configuration */
  validate(): Promise<ConfigurationValidationResult>;
  /** Reload configuration */
  reload(): Promise<void>;
}

/**
 * Configuration validation result
 */
export interface ConfigurationValidationResult {
  /** Whether configuration is valid */
  valid: boolean;
  /** Validation errors */
  errors: ConfigValidationError[];
  /** Validation warnings */
  warnings: ConfigurationWarning[];
}

/**
 * Configuration validation error
 */
export interface ConfigValidationError {
  /** Error key */
  key: string;
  /** Error message */
  message: string;
  /** Error severity */
  severity: 'error' | 'warning';
}

/**
 * Configuration warning
 */
export interface ConfigurationWarning {
  /** Warning key */
  key: string;
  /** Warning message */
  message: string;
  /** Suggested value */
  suggestion?: unknown;
}

/**
 * Environment configuration
 */
export interface EnvironmentConfig {
  /** Environment variables */
  vars: Record<string, string>;
  /** Required environment variables */
  required: string[];
  /** Optional environment variables */
  optional: string[];
}

/**
 * Configuration defaults
 */
export const DEFAULT_CONFIG: Partial<BotConfiguration> = {
  environment: 'development',
  server: {
    host: 'localhost',
    port: 3000,
    baseUrl: 'http://localhost:3000',
    cors: {
      origins: ['*'],
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      headers: ['Content-Type', 'Authorization'],
      credentials: true,
    },
    rateLimit: {
      maxRequests: 100,
      windowMs: 15 * 60 * 1000, // 15 minutes
      skipSuccessfulRequests: false,
    },
  },
  database: {
    url: process.env.DATABASE_URL || 'postgresql://localhost:5432/whatsapp_bot',
    poolSize: 10,
    timeout: 30000,
    ssl: false,
    migrations: {
      autoRun: false,
      tableName: 'migrations',
      directory: './migrations',
    },
  },
  logging: {
    level: 'info',
    format: 'json',
    destination: 'console',
  },
  security: {
    jwtSecret: process.env.JWT_SECRET || 'dev-secret-key',
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    encryptionKey: process.env.ENCRYPTION_KEY || 'dev-encryption-key',
    httpsOnly: false,
  },
  features: {
    sessionPersistence: true,
    streamingResponses: true,
    codeAnalysis: true,
    deployment: true,
    gitIntegration: true,
    webhookValidation: true,
    rateLimiting: true,
    errorRecovery: true,
  },
};
