/**
 * Webhook and communication types for WhatsApp Website Bot
 * Based on Cyrus webhook patterns adapted for WhatsApp integration
 */

import type { User, ProjectData } from './types.js';
import type { SessionSummary } from './session.js';

/**
 * WhatsApp webhook event types
 */
export type WebhookEventType =
  | 'message.received'
  | 'message.sent'
  | 'message.delivered'
  | 'message.read'
  | 'message.failed'
  | 'session.started'
  | 'session.completed'
  | 'session.failed'
  | 'user.registered'
  | 'project.created'
  | 'project.updated'
  | 'project.deployed'
  | 'system.error'
  | 'system.maintenance';

/**
 * WhatsApp message types
 */
export type WhatsAppMessageType =
  | 'text'
  | 'image'
  | 'audio'
  | 'video'
  | 'document'
  | 'location'
  | 'contact';

/**
 * WhatsApp message status
 */
export type WhatsAppMessageStatus = 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Base webhook event structure
 */
export interface BaseWebhookEvent {
  /** Event ID */
  id: string;
  /** Event type */
  type: WebhookEventType;
  /** Event timestamp */
  timestamp: Date;
  /** Event source */
  source: string;
  /** Event version */
  version: string;
  /** Event metadata */
  metadata?: Record<string, unknown>;
}

/**
 * WhatsApp message received event
 */
export interface MessageReceivedEvent extends BaseWebhookEvent {
  type: 'message.received';
  /** Message data */
  data: {
    /** Message ID */
    messageId: string;
    /** Sender information */
    from: User;
    /** Message content */
    message: WebhookWhatsAppMessage;
    /** Message context */
    context?: MessageContext;
  };
}

/**
 * WhatsApp message sent event
 */
export interface MessageSentEvent extends BaseWebhookEvent {
  type: 'message.sent';
  /** Message data */
  data: {
    /** Message ID */
    messageId: string;
    /** Recipient information */
    to: User;
    /** Message content */
    message: WebhookWhatsAppMessage;
    /** Message status */
    status: WhatsAppMessageStatus;
  };
}

/**
 * WhatsApp message delivered event
 */
export interface MessageDeliveredEvent extends BaseWebhookEvent {
  type: 'message.delivered';
  /** Message data */
  data: {
    /** Message ID */
    messageId: string;
    /** Recipient phone number */
    to: string;
    /** Delivery timestamp */
    deliveredAt: Date;
  };
}

/**
 * WhatsApp message read event
 */
export interface MessageReadEvent extends BaseWebhookEvent {
  type: 'message.read';
  /** Message data */
  data: {
    /** Message ID */
    messageId: string;
    /** Recipient phone number */
    to: string;
    /** Read timestamp */
    readAt: Date;
  };
}

/**
 * WhatsApp message failed event
 */
export interface MessageFailedEvent extends BaseWebhookEvent {
  type: 'message.failed';
  /** Message data */
  data: {
    /** Message ID */
    messageId: string;
    /** Recipient phone number */
    to: string;
    /** Error information */
    error: {
      code: string;
      message: string;
      details?: Record<string, unknown>;
    };
  };
}

/**
 * Session started event
 */
export interface SessionStartedEvent extends BaseWebhookEvent {
  type: 'session.started';
  /** Session data */
  data: {
    /** Session summary */
    session: SessionSummary;
    /** Trigger message */
    triggerMessage?: WebhookWhatsAppMessage;
  };
}

/**
 * Session completed event
 */
export interface SessionCompletedEvent extends BaseWebhookEvent {
  type: 'session.completed';
  /** Session data */
  data: {
    /** Session summary */
    session: SessionSummary;
    /** Project data (if created) */
    project?: ProjectData;
    /** Completion details */
    completion: {
      /** Completion message */
      message: string;
      /** Output files */
      files?: string[];
      /** Deployment URL */
      deploymentUrl?: string;
    };
  };
}

/**
 * Session failed event
 */
export interface SessionFailedEvent extends BaseWebhookEvent {
  type: 'session.failed';
  /** Session data */
  data: {
    /** Session summary */
    session: SessionSummary;
    /** Error information */
    error: {
      code: string;
      message: string;
      details?: Record<string, unknown>;
    };
  };
}

/**
 * User registered event
 */
export interface UserRegisteredEvent extends BaseWebhookEvent {
  type: 'user.registered';
  /** User data */
  data: {
    /** User information */
    user: User;
    /** Registration source */
    source: string;
  };
}

/**
 * Project created event
 */
export interface ProjectCreatedEvent extends BaseWebhookEvent {
  type: 'project.created';
  /** Project data */
  data: {
    /** Project information */
    project: ProjectData;
    /** Creator information */
    creator: User;
  };
}

/**
 * Project updated event
 */
export interface ProjectUpdatedEvent extends BaseWebhookEvent {
  type: 'project.updated';
  /** Project data */
  data: {
    /** Project information */
    project: ProjectData;
    /** Updater information */
    updater: User;
    /** Update details */
    changes: {
      /** Changed fields */
      fields: string[];
      /** Change summary */
      summary: string;
    };
  };
}

/**
 * Project deployed event
 */
export interface ProjectDeployedEvent extends BaseWebhookEvent {
  type: 'project.deployed';
  /** Project data */
  data: {
    /** Project information */
    project: ProjectData;
    /** Deployer information */
    deployer: User;
    /** Deployment details */
    deployment: {
      /** Deployment URL */
      url: string;
      /** Deployment platform */
      platform: string;
      /** Deployment status */
      status: 'success' | 'failed';
      /** Deployment logs */
      logs?: string[];
    };
  };
}

/**
 * System error event
 */
export interface SystemErrorEvent extends BaseWebhookEvent {
  type: 'system.error';
  /** Error data */
  data: {
    /** Error information */
    error: {
      code: string;
      message: string;
      stack?: string;
      details?: Record<string, unknown>;
    };
    /** Error context */
    context?: {
      /** User context */
      user?: User;
      /** Session context */
      session?: string;
      /** Request context */
      request?: Record<string, unknown>;
    };
  };
}

/**
 * System maintenance event
 */
export interface SystemMaintenanceEvent extends BaseWebhookEvent {
  type: 'system.maintenance';
  /** Maintenance data */
  data: {
    /** Maintenance action */
    action: 'start' | 'end' | 'update';
    /** Maintenance message */
    message: string;
    /** Estimated duration */
    duration?: number;
    /** Affected services */
    services?: string[];
  };
}

/**
 * Union of all webhook events
 */
export type WebhookEvent =
  | MessageReceivedEvent
  | MessageSentEvent
  | MessageDeliveredEvent
  | MessageReadEvent
  | MessageFailedEvent
  | SessionStartedEvent
  | SessionCompletedEvent
  | SessionFailedEvent
  | UserRegisteredEvent
  | ProjectCreatedEvent
  | ProjectUpdatedEvent
  | ProjectDeployedEvent
  | SystemErrorEvent
  | SystemMaintenanceEvent;

/**
 * WhatsApp webhook message structure
 */
export interface WebhookWhatsAppMessage {
  /** Message ID */
  id: string;
  /** Message type */
  type: WhatsAppMessageType;
  /** Message content */
  content: MessageContent;
  /** Message timestamp */
  timestamp: Date;
  /** Message metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Message content based on type
 */
export type MessageContent =
  | TextMessageContent
  | ImageMessageContent
  | AudioMessageContent
  | VideoMessageContent
  | DocumentMessageContent
  | LocationMessageContent
  | ContactMessageContent;

/**
 * Text message content
 */
export interface TextMessageContent {
  type: 'text';
  /** Text content */
  text: string;
}

/**
 * Image message content
 */
export interface ImageMessageContent {
  type: 'image';
  /** Image URL */
  url: string;
  /** Image caption */
  caption?: string;
  /** Image filename */
  filename?: string;
  /** Image mime type */
  mimeType?: string;
  /** Image size in bytes */
  size?: number;
}

/**
 * Audio message content
 */
export interface AudioMessageContent {
  type: 'audio';
  /** Audio URL */
  url: string;
  /** Audio duration in seconds */
  duration?: number;
  /** Audio filename */
  filename?: string;
  /** Audio mime type */
  mimeType?: string;
  /** Audio size in bytes */
  size?: number;
}

/**
 * Video message content
 */
export interface VideoMessageContent {
  type: 'video';
  /** Video URL */
  url: string;
  /** Video caption */
  caption?: string;
  /** Video duration in seconds */
  duration?: number;
  /** Video filename */
  filename?: string;
  /** Video mime type */
  mimeType?: string;
  /** Video size in bytes */
  size?: number;
}

/**
 * Document message content
 */
export interface DocumentMessageContent {
  type: 'document';
  /** Document URL */
  url: string;
  /** Document filename */
  filename: string;
  /** Document mime type */
  mimeType: string;
  /** Document size in bytes */
  size: number;
}

/**
 * Location message content
 */
export interface LocationMessageContent {
  type: 'location';
  /** Latitude */
  latitude: number;
  /** Longitude */
  longitude: number;
  /** Location name */
  name?: string;
  /** Location address */
  address?: string;
}

/**
 * Contact message content
 */
export interface ContactMessageContent {
  type: 'contact';
  /** Contact name */
  name: string;
  /** Contact phone number */
  phone: string;
  /** Contact email */
  email?: string;
  /** Contact organization */
  organization?: string;
}

/**
 * Message context for conversation threading
 */
export interface MessageContext {
  /** Thread ID */
  threadId?: string;
  /** Parent message ID */
  parentMessageId?: string;
  /** Conversation ID */
  conversationId?: string;
  /** Session ID */
  sessionId?: string;
  /** User intent */
  intent?: string;
  /** Context data */
  data?: Record<string, unknown>;
}

/**
 * Webhook payload structure
 */
export interface WebhookPayload {
  /** Webhook event */
  event: WebhookEvent;
  /** Webhook signature */
  signature?: string;
  /** Webhook delivery attempt */
  deliveryAttempt?: number;
  /** Webhook retry count */
  retryCount?: number;
}

/**
 * Webhook handler interface
 */
export interface WebhookHandler {
  /** Handle webhook event */
  handle(event: WebhookEvent): Promise<WebhookResponse>;
  /** Get supported event types */
  getSupportedEvents(): WebhookEventType[];
  /** Validate webhook signature */
  validateSignature?(payload: WebhookPayload, signature: string): boolean;
}

/**
 * Webhook response structure
 */
export interface WebhookResponse {
  /** Response status */
  status: 'success' | 'error' | 'retry';
  /** Response message */
  message?: string;
  /** Response data */
  data?: Record<string, unknown>;
  /** Retry after seconds (for retry status) */
  retryAfter?: number;
}

/**
 * Streaming communication interface
 */
export interface StreamingCommunication {
  /** Stream ID */
  streamId: string;
  /** Stream type */
  type: 'session' | 'deployment' | 'build' | 'analysis';
  /** Stream status */
  status: 'active' | 'paused' | 'completed' | 'failed';
  /** Stream data */
  data: StreamingData;
  /** Stream metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Streaming data structure
 */
export interface StreamingData {
  /** Data chunks */
  chunks: StreamingChunk[];
  /** Total chunks */
  totalChunks?: number;
  /** Completed chunks */
  completedChunks?: number;
  /** Stream progress percentage */
  progress?: number;
}

/**
 * Streaming chunk structure
 */
export interface StreamingChunk {
  /** Chunk ID */
  id: string;
  /** Chunk sequence number */
  sequence: number;
  /** Chunk type */
  type: 'text' | 'json' | 'binary' | 'event';
  /** Chunk data */
  data: unknown;
  /** Chunk timestamp */
  timestamp: Date;
  /** Chunk metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Communication channel interface
 */
export interface CommunicationChannel {
  /** Channel ID */
  id: string;
  /** Channel type */
  type: 'whatsapp' | 'webhook' | 'streaming' | 'internal';
  /** Channel status */
  status: 'active' | 'inactive' | 'error';
  /** Send message */
  send(message: unknown): Promise<void>;
  /** Receive message */
  receive(): Promise<unknown>;
  /** Close channel */
  close(): Promise<void>;
  /** Get channel status */
  getStatus(): Promise<string>;
}

/**
 * Type guards for webhook events
 */
export function isMessageReceivedEvent(event: WebhookEvent): event is MessageReceivedEvent {
  return event.type === 'message.received';
}

export function isMessageSentEvent(event: WebhookEvent): event is MessageSentEvent {
  return event.type === 'message.sent';
}

export function isSessionStartedEvent(event: WebhookEvent): event is SessionStartedEvent {
  return event.type === 'session.started';
}

export function isSessionCompletedEvent(event: WebhookEvent): event is SessionCompletedEvent {
  return event.type === 'session.completed';
}

export function isSessionFailedEvent(event: WebhookEvent): event is SessionFailedEvent {
  return event.type === 'session.failed';
}

export function isProjectCreatedEvent(event: WebhookEvent): event is ProjectCreatedEvent {
  return event.type === 'project.created';
}

export function isProjectDeployedEvent(event: WebhookEvent): event is ProjectDeployedEvent {
  return event.type === 'project.deployed';
}

export function isSystemErrorEvent(event: WebhookEvent): event is SystemErrorEvent {
  return event.type === 'system.error';
}
