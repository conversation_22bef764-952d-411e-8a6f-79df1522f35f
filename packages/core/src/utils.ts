/**
 * Utility functions for the WhatsApp Website Bot
 */

/**
 * Create a timeout promise that rejects after the specified time
 */
export function createTimeoutPromise(ms: number, message: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error(message)), ms);
  });
}

/**
 * Generate a project name from a user prompt
 */
export function generateProjectNameFromPrompt(prompt: string): string {
  // Simple project name generation logic
  const words = prompt
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 2)
    .slice(0, 3);

  return words.join('-') || 'whatsapp-bot-project';
}

/**
 * Validate website code content
 */
export function validateWebsiteCode(code: string): boolean {
  if (!code || code.trim().length === 0) {
    throw new Error('Website code is empty');
  }

  if (code.length < 100) {
    throw new Error('Website code is too short');
  }

  if (!code.includes('<html') && !code.includes('<!DOCTYPE')) {
    throw new Error('Website code does not appear to be valid HTML');
  }

  return true;
}

/**
 * Log main process diagnostics
 */
export function logMainProcessDiagnostics(
  projectName: string,
  prompt: string,
  envVars: Record<string, boolean>,
): void {
  console.log('=== MAIN PROCESS DIAGNOSTICS ===');
  console.log(`Project: ${projectName}`);
  console.log(`Prompt: ${prompt.substring(0, 100)}...`);
  console.log(`Environment variables status:`, envVars);
  console.log(`Timestamp: ${new Date().toISOString()}`);
  console.log('================================');
}

/**
 * Generate timeout fallback HTML
 */
export function generateTimeoutFallbackHTML(prompt: string, projectName: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${projectName} - Processing</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <h1>⏳ Processing Your Request</h1>
        <div class="spinner"></div>
        <p>Your website is being generated based on: <strong>"${prompt}"</strong></p>
        <p>The process took longer than expected, but we've created this placeholder for you.</p>
        <p>You can try editing this project to get your desired website.</p>
    </div>
</body>
</html>`;
}

/**
 * Sanitize input string for safe processing
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/[&]/g, '&amp;') // Escape ampersands
    .trim();
}

/**
 * Format timestamp for logging
 */
export function formatTimestamp(date: Date = new Date()): string {
  return date
    .toISOString()
    .replace('T', ' ')
    .replace(/\.\d{3}Z$/, '');
}

/**
 * Extract repository information from URL
 */
export function extractRepositoryInfo(url: string): { owner: string; repo: string } | null {
  const githubRegex = /github\.com\/([^/]+)\/([^/]+)/;
  const match = url.match(githubRegex);

  if (match && match[1] && match[2]) {
    return {
      owner: match[1],
      repo: match[2].replace(/\.git$/, ''),
    };
  }

  return null;
}
