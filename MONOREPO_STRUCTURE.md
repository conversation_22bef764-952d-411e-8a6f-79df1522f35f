# WhatsApp Website Bot - Monorepo Structure

## Created Files and Directories

### Root Configuration
- `pnpm-workspace.yaml` - pnpm workspace configuration
- `tsconfig.base.json` - Base TypeScript configuration with path mappings
- `package.json` - Updated root workspace configuration
- `README.md` - Updated with monorepo documentation
- `fly.toml` - Fly.io deployment configuration for bot-engine app

### Apps Structure
```
apps/
├── webhook-handler/
│   ├── package.json              # Vercel function dependencies
│   ├── tsconfig.json             # TypeScript configuration
│   ├── vercel.json               # Vercel deployment configuration
│   └── api/
│       └── whatsapp.ts           # WhatsApp webhook handler (placeholder)
│
└── bot-engine/
    ├── package.json              # Fly.io app dependencies
    ├── tsconfig.json             # TypeScript configuration
    ├── Dockerfile                # Container configuration
    └── src/
        └── index.ts              # Bot engine main entry point
```

### Packages Structure
```
packages/
├── core/
│   ├── package.json              # Core package configuration
│   ├── tsconfig.json             # TypeScript configuration
│   └── src/
│       ├── index.ts              # Main export file
│       ├── types.ts              # Shared type definitions
│       ├── config.ts             # Configuration management
│       └── utils.ts              # Utility functions
│
├── workspace-manager/
│   ├── package.json              # Workspace manager dependencies
│   ├── tsconfig.json             # TypeScript configuration
│   └── src/
│       ├── index.ts              # Main export file
│       ├── workspace-manager.ts  # Git worktree management
│       └── git-operations.ts     # Git operations utilities
│
└── gemini-runner/
    ├── package.json              # Gemini runner dependencies
    ├── tsconfig.json             # TypeScript configuration
    └── src/
        ├── index.ts              # Main export file
        ├── gemini-runner.ts      # Gemini AI integration
        └── ai-integration.ts     # AI utilities
```

## Key Features

### 1. Workspace Dependencies
- Packages reference each other using `workspace:*` syntax
- Apps depend on shared packages for common functionality
- Proper dependency resolution across the monorepo

### 2. TypeScript Configuration
- **Base config**: `tsconfig.base.json` with shared compiler options
- **Path mappings**: Clean imports between workspace packages
- **Package-specific**: Each package/app extends the base configuration

### 3. Build System
- **pnpm workspaces**: Efficient package management and linking
- **Parallel builds**: All packages can be built simultaneously
- **Filtering**: Target specific packages for operations

### 4. Development Experience
- **Concurrent development**: Run all apps/packages in watch mode
- **Type checking**: Workspace-wide TypeScript validation
- **Shared tooling**: Consistent linting, formatting, and testing

## Package Dependencies

### Core Package
- Shared types and interfaces
- Configuration management
- Utility functions
- No external workspace dependencies

### Workspace Manager
- Depends on: `@whatsite-bot/core`
- External: `simple-git`
- Handles git operations and worktree management

### Gemini Runner
- Depends on: `@whatsite-bot/core`
- External: `@google/genai`, `openai`
- AI integration and content generation

### Webhook Handler App
- Depends on: `@whatsite-bot/core`, `@whatsite-bot/workspace-manager`, `@whatsite-bot/gemini-runner`
- External: Vercel, Twilio, WhatsApp integration dependencies
- Handles incoming webhooks from WhatsApp

### Bot Engine App
- Depends on: `@whatsite-bot/core`, `@whatsite-bot/workspace-manager`, `@whatsite-bot/gemini-runner`
- External: Express, WebSocket, Fly.io dependencies
- Handles persistent bot logic and real-time connections

## Next Steps

1. **Set up CI/CD**: Configure build and deployment pipelines
2. **Testing**: Add comprehensive test suites for all packages
3. **Documentation**: Complete API documentation for each package
4. **Environment setup**: Configure environment-specific deployments

## Benefits

- **Separation of concerns**: Clear boundaries between components
- **Code reusability**: Shared packages eliminate duplication
- **Scalability**: Easy to add new apps/packages as features grow
- **Development efficiency**: Consistent tooling and workflows
- **Deployment flexibility**: Different deployment strategies for different components