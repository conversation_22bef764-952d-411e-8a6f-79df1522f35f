/******************************************************************************************
 * scripts/indexRepo.mjs
 *
 * AST-aware Repository Indexer
 * - Uses @llamaindex/node for CodeASTNodeParser (function/class-level chunks)
 * - Embeds each chunk with Google Gemini (`@google/genai`)
 * - Upserts into Supabase `repo_chunks`
 *
 * Prerequisites (already installed in the Action):
 *   npm i @google/genai@1.9 @supabase/supabase-js@2 @llamaindex/node@0.10 pg@8
 ******************************************************************************************/

import { GoogleGenAI }             from '@google/genai';
import { createClient }            from '@supabase/supabase-js';
import { CodeASTNodeParser }       from '@llamaindex/node';
import fs                          from 'fs/promises';
import path                        from 'path';
import process                     from 'process';

/* ───────────────────────── 0. Environment ───────────────────────── */

const {
  GOOGLE_API_KEY,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  GITHUB_REPOSITORY           // "owner/repo"
} = process.env;

if (!GOOGLE_API_KEY || !SUPABASE_URL || !SUPABASE_ANON_KEY || !GITHUB_REPOSITORY) {
  console.error('❌  Missing required env vars');
  process.exit(1);
}

const VECTOR_DIM = 1536;                // must match DB schema
const BATCH_SIZE = 25;                  // Supabase insert batch
const SKIP_DIRS  = ['node_modules','.git','dist','build','.next','.vercel','coverage'];

/* ─────────────────────── 1. Library clients ─────────────────────── */

const ai   = new GoogleGenAI({ apiKey: GOOGLE_API_KEY });
const supa = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const parser = new CodeASTNodeParser({ maxLines: 120 });

/* ─────────────────────── 2. Helper functions ────────────────────── */

/** Recursively collect file paths */
async function walk(dir, arr = []) {
  for (const entry of await fs.readdir(dir, { withFileTypes: true })) {
    const fp = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      if (SKIP_DIRS.includes(entry.name)) continue;
      await walk(fp, arr);
    } else {
      arr.push(fp);
    }
  }
  return arr;
}

/** Quick extension filter */
function isIndexable(fp) {
  return /\.(c|cpp|js|jsx|ts|tsx|py|java|go|rs|html|css|json|md)$/.test(fp);
}

/** Call Gemini embeddings endpoint */
async function embed(text) {
  const { embedding } = await ai.models.embedContent({
    model: 'gemini-embedding-001',
    contents: text
  });
  return embedding.values.slice(0, VECTOR_DIM);      // ensures dim match
}

/** Get one-line summary via Gemini-Flash */
async function summarize(text, fileType) {
  const { text: sum } = await ai.models.generateContent({
    model: 'gemini-2.0-flash',
    contents: `Summarize this ${fileType} file in ≤100 characters:\n\n${text.slice(0, 800)}`,
    config: { temperature: 0.3, maxOutputTokens: 50 }
  });
  return sum.trim();
}

/** Resolve user_projects.id from github_repo_name */
async function getProjectId() {
  const { data, error } = await supa
    .from('user_projects')
    .select('id')
    .eq('github_repo_name', GITHUB_REPOSITORY)
    .single();
  if (error) throw new Error(`Project lookup failed: ${error.message}`);
  return data.id;
}

/* ───────────────────────── 3. Main flow ─────────────────────────── */

(async () => {
  console.log(`🚀  Indexing repository: ${GITHUB_REPOSITORY}`);
  const projectId = await getProjectId();

  const files = (await walk('.')).filter(isIndexable);
  console.log(`📂  Found ${files.length} indexable files`);

  /* Clear previous chunks */
  await supa.from('repo_chunks').delete().eq('project_id', projectId);

  let chunkGlobalId = 0;
  const batch = [];

  for (const filePath of files) {
    const relPath = path.relative('.', filePath);
    const code    = await fs.readFile(filePath, 'utf8');
    const chunks  = parser.splitText(code);          // AST-aware

    const summary = await summarize(code, path.extname(filePath).slice(1));

    for (const chunkText of chunks) {
      const vector = await embed(chunkText);

      batch.push({
        project_id : projectId,
        file_path  : relPath,
        chunk_id   : chunkGlobalId++,
        content    : chunkText,
        embedding  : vector
      });

      if (batch.length >= BATCH_SIZE) {
        const { error } = await supa.from('repo_chunks').insert(batch);
        if (error) console.error('❌  Batch insert error:', error.message);
        batch.length = 0;
      }
    }

    /* Write repo-map entry (lazy create dir) */
    await fs.mkdir('ai', { recursive: true });
    await fs.appendFile('ai/repo-map.jsonl',
      JSON.stringify({ path: relPath, summary, lines: code.split('\n').length }) + '\n');
  }

  /* Flush last batch */
  if (batch.length) {
    const { error } = await supa.from('repo_chunks').insert(batch);
    if (error) console.error('❌  Tail insert error:', error.message);
  }

  /* Mark project indexed & point to repo-map */
  await supa.from('user_projects')
            .update({
              repo_indexed_at: new Date().toISOString(),
              repo_map_path  : 'ai/repo-map.jsonl'
            })
            .eq('id', projectId);

  console.log(`✅  Completed: ${chunkGlobalId} chunks uploaded.`);
})();