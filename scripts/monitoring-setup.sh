#!/bin/bash

# Monitoring Setup Script for Fly.io WhatsApp Bot Engine
# Sets up comprehensive monitoring, alerting, and logging

set -e

# Configuration
APP_NAME="whatsite-bot-engine"
GRAFANA_ENABLED=true
PROMETHEUS_ENABLED=true
ALERT_WEBHOOK_URL=""
LOG_LEVEL="info"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites for monitoring setup..."
    
    if ! command_exists fly; then
        log_error "Fly CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists curl; then
        log_error "curl is not installed. Please install it first."
        exit 1
    fi
    
    # Check if we're logged into Fly.io
    if ! fly auth whoami >/dev/null 2>&1; then
        log_error "Not logged into Fly.io. Please run 'fly auth login' first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Set up Fly.io metrics
setup_fly_metrics() {
    log_info "Setting up Fly.io metrics collection..."
    
    # Enable metrics collection
    fly config set metrics-enabled=true
    
    # Set up custom metrics
    cat > /tmp/metrics.toml << EOF
[metrics]
enabled = true
port = 9090
path = "/metrics"

[metrics.prometheus]
enabled = true
scrape_interval = "15s"
scrape_timeout = "10s"

[metrics.custom]
sessions_active = "gauge"
sessions_created_total = "counter"
sessions_completed_total = "counter"
sessions_failed_total = "counter"
webhooks_received_total = "counter"
webhooks_processed_total = "counter"
websites_created_total = "counter"
error_rate = "gauge"
EOF
    
    # Apply metrics configuration
    fly config import /tmp/metrics.toml
    
    log_success "Fly.io metrics configured"
}

# Set up Prometheus monitoring
setup_prometheus() {
    if [[ "$PROMETHEUS_ENABLED" != "true" ]]; then
        log_info "Prometheus monitoring disabled, skipping..."
        return
    fi
    
    log_info "Setting up Prometheus monitoring..."
    
    # Create Prometheus configuration
    cat > /tmp/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'whatsite-bot-engine'
    static_configs:
      - targets: ['${APP_NAME}.fly.dev:9090']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'fly-metrics'
    static_configs:
      - targets: ['${APP_NAME}.fly.dev:3000']
    metrics_path: /metrics
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF
    
    # Create alert rules
    cat > /tmp/alert_rules.yml << EOF
groups:
- name: whatsite-bot-engine
  rules:
  - alert: HighErrorRate
    expr: error_rate > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ \$value }} for the last 5 minutes"

  - alert: HighMemoryUsage
    expr: process_memory_bytes > 1.5e+9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is {{ \$value }} bytes"

  - alert: SessionFailures
    expr: increase(sessions_failed_total[5m]) > 10
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High session failure rate"
      description: "{{ \$value }} sessions failed in the last 5 minutes"

  - alert: WebhookProcessingDelay
    expr: histogram_quantile(0.95, rate(webhooks_processing_duration_seconds_bucket[5m])) > 30
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "Webhook processing delays"
      description: "95th percentile processing time is {{ \$value }} seconds"

  - alert: ApplicationDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Application is down"
      description: "WhatsApp Bot Engine is not responding"

  - alert: TooManyRequests
    expr: rate(http_requests_total[5m]) > 100
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High request rate"
      description: "Request rate is {{ \$value }} per second"
EOF
    
    log_success "Prometheus configuration created"
}

# Set up Grafana dashboard
setup_grafana() {
    if [[ "$GRAFANA_ENABLED" != "true" ]]; then
        log_info "Grafana dashboard disabled, skipping..."
        return
    fi
    
    log_info "Setting up Grafana dashboard..."
    
    # Create Grafana dashboard JSON
    cat > /tmp/grafana-dashboard.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "WhatsApp Bot Engine Dashboard",
    "tags": ["whatsite", "bot-engine", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "error_rate",
            "legendFormat": "Error Rate"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Active Sessions",
        "type": "graph",
        "targets": [
          {
            "expr": "sessions_active",
            "legendFormat": "Active Sessions"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "process_memory_bytes",
            "legendFormat": "Memory Usage (bytes)"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      },
      {
        "id": 5,
        "title": "Session Metrics",
        "type": "graph",
        "targets": [
          {
            "expr": "increase(sessions_created_total[5m])",
            "legendFormat": "Sessions Created"
          },
          {
            "expr": "increase(sessions_completed_total[5m])",
            "legendFormat": "Sessions Completed"
          },
          {
            "expr": "increase(sessions_failed_total[5m])",
            "legendFormat": "Sessions Failed"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
      },
      {
        "id": 6,
        "title": "Webhook Processing",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(webhooks_received_total[5m])",
            "legendFormat": "Webhooks Received/sec"
          },
          {
            "expr": "rate(webhooks_processed_total[5m])",
            "legendFormat": "Webhooks Processed/sec"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}
      },
      {
        "id": 7,
        "title": "Website Creation",
        "type": "graph",
        "targets": [
          {
            "expr": "increase(websites_created_total[5m])",
            "legendFormat": "Websites Created"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
EOF
    
    log_success "Grafana dashboard configuration created"
}

# Set up logging
setup_logging() {
    log_info "Setting up structured logging..."
    
    # Configure log forwarding
    cat > /tmp/logging.toml << EOF
[logging]
level = "${LOG_LEVEL}"
format = "json"
structured = true

[logging.destinations]
stdout = true
stderr = true

[logging.fields]
timestamp = true
level = true
message = true
service = "whatsite-bot-engine"
version = "1.0.0"

[logging.sampling]
enabled = true
rate = 1.0
EOF
    
    # Apply logging configuration
    fly config import /tmp/logging.toml
    
    log_success "Logging configuration applied"
}

# Set up alerting
setup_alerting() {
    log_info "Setting up alerting..."
    
    # Configure alert webhook if provided
    if [[ -n "$ALERT_WEBHOOK_URL" ]]; then
        fly secrets set ALERT_WEBHOOK_URL="$ALERT_WEBHOOK_URL"
        log_success "Alert webhook configured"
    else
        log_warning "No alert webhook URL provided"
    fi
    
    # Set up alert rules
    cat > /tmp/alert-config.toml << EOF
[alerting]
enabled = true
webhook_url = "${ALERT_WEBHOOK_URL}"

[alerting.rules]
high_error_rate = {
  metric = "error_rate",
  operator = "gt",
  threshold = 0.05,
  duration = 300,
  severity = "high"
}

high_memory_usage = {
  metric = "process_memory_bytes",
  operator = "gt",
  threshold = **********,
  duration = 300,
  severity = "medium"
}

session_failures = {
  metric = "sessions_failed_total",
  operator = "gt",
  threshold = 10,
  duration = 600,
  severity = "high"
}

slow_webhook_processing = {
  metric = "webhooks_processing_duration_seconds",
  operator = "gt",
  threshold = 30,
  duration = 180,
  severity = "medium"
}
EOF
    
    # Apply alert configuration
    fly config import /tmp/alert-config.toml
    
    log_success "Alerting configuration applied"
}

# Set up health check monitoring
setup_health_monitoring() {
    log_info "Setting up health check monitoring..."
    
    # Create health check script
    cat > /tmp/health-monitor.sh << 'EOF'
#!/bin/bash

# Health monitoring script for continuous monitoring

APP_URL="https://whatsite-bot-engine.fly.dev"
ENDPOINTS=("/health" "/ready" "/metrics")
LOG_FILE="/tmp/health-monitor.log"

log_message() {
    echo "$(date -u +"%Y-%m-%d %H:%M:%S UTC") - $1" | tee -a "$LOG_FILE"
}

check_endpoint() {
    local endpoint=$1
    local url="${APP_URL}${endpoint}"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null --max-time 10 "$url")
    
    if [[ "$response" == "200" ]]; then
        log_message "✓ $endpoint - OK"
        return 0
    else
        log_message "✗ $endpoint - Failed (HTTP $response)"
        return 1
    fi
}

main() {
    log_message "Starting health monitoring cycle"
    
    local failed_checks=0
    
    for endpoint in "${ENDPOINTS[@]}"; do
        if ! check_endpoint "$endpoint"; then
            ((failed_checks++))
        fi
    done
    
    if [[ $failed_checks -gt 0 ]]; then
        log_message "Health monitoring failed: $failed_checks endpoints failed"
        exit 1
    else
        log_message "Health monitoring passed: all endpoints healthy"
    fi
}

main "$@"
EOF
    
    chmod +x /tmp/health-monitor.sh
    
    log_success "Health monitoring script created"
}

# Set up monitoring secrets
setup_monitoring_secrets() {
    log_info "Setting up monitoring secrets..."
    
    local secrets=(
        "METRICS_ENABLED=true"
        "LOG_LEVEL=${LOG_LEVEL}"
        "MONITORING_ENABLED=true"
    )
    
    # Add optional secrets
    [[ -n "$ALERT_WEBHOOK_URL" ]] && secrets+=("ALERT_WEBHOOK_URL=${ALERT_WEBHOOK_URL}")
    
    for secret in "${secrets[@]}"; do
        echo "$secret" | fly secrets set --stdin
    done
    
    log_success "Monitoring secrets configured"
}

# Test monitoring setup
test_monitoring() {
    log_info "Testing monitoring setup..."
    
    local app_url="https://${APP_NAME}.fly.dev"
    
    # Test metrics endpoint
    log_info "Testing metrics endpoint..."
    if curl -s -f "${app_url}/metrics" | grep -q "http_requests_total"; then
        log_success "✓ Metrics endpoint working"
    else
        log_error "✗ Metrics endpoint not working"
        return 1
    fi
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if curl -s -f "${app_url}/health" >/dev/null; then
        log_success "✓ Health endpoint working"
    else
        log_error "✗ Health endpoint not working"
        return 1
    fi
    
    # Test ready endpoint
    log_info "Testing ready endpoint..."
    if curl -s -f "${app_url}/ready" >/dev/null; then
        log_success "✓ Ready endpoint working"
    else
        log_error "✗ Ready endpoint not working"
        return 1
    fi
    
    log_success "All monitoring tests passed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -f /tmp/metrics.toml /tmp/prometheus.yml /tmp/alert_rules.yml
    rm -f /tmp/grafana-dashboard.json /tmp/logging.toml /tmp/alert-config.toml
    rm -f /tmp/health-monitor.sh
}

# Main function
main() {
    log_info "Starting monitoring setup for $APP_NAME..."
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Run setup steps
    check_prerequisites
    setup_fly_metrics
    setup_prometheus
    setup_grafana
    setup_logging
    setup_alerting
    setup_health_monitoring
    setup_monitoring_secrets
    
    # Test the setup
    sleep 10  # Give time for configuration to apply
    test_monitoring
    
    log_success "🎉 Monitoring setup completed successfully!"
    
    # Display monitoring URLs
    echo ""
    log_info "Monitoring URLs:"
    echo "  Health Check: https://${APP_NAME}.fly.dev/health"
    echo "  Ready Check:  https://${APP_NAME}.fly.dev/ready"
    echo "  Metrics:      https://${APP_NAME}.fly.dev/metrics"
    echo ""
    
    if [[ "$GRAFANA_ENABLED" == "true" ]]; then
        log_info "Grafana dashboard configuration saved to /tmp/grafana-dashboard.json"
    fi
    
    if [[ "$PROMETHEUS_ENABLED" == "true" ]]; then
        log_info "Prometheus configuration saved to /tmp/prometheus.yml"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --app-name NAME           Set application name (default: whatsite-bot-engine)"
    echo "  --no-grafana             Disable Grafana dashboard setup"
    echo "  --no-prometheus          Disable Prometheus monitoring"
    echo "  --alert-webhook URL      Set alert webhook URL"
    echo "  --log-level LEVEL        Set log level (debug|info|warn|error)"
    echo "  --help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Basic setup"
    echo "  $0 --alert-webhook https://...       # With alerting"
    echo "  $0 --no-grafana --log-level debug    # Custom configuration"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --app-name)
            APP_NAME="$2"
            shift 2
            ;;
        --no-grafana)
            GRAFANA_ENABLED=false
            shift
            ;;
        --no-prometheus)
            PROMETHEUS_ENABLED=false
            shift
            ;;
        --alert-webhook)
            ALERT_WEBHOOK_URL="$2"
            shift 2
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main "$@"