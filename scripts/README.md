# Gemini Model Testing Scripts

## test-gemini-2.5-pro.mjs

A comprehensive test script to check the availability and functionality of Google's Gemini models, with a focus on Gemini-2.5-pro.

### Usage

```bash
# Run directly
node scripts/test-gemini-2.5-pro.mjs

# Or use npm script
npm run test:gemini
```

### What it tests

1. **Basic Availability** - Tests if Gemini-2.5-pro responds to simple prompts
2. **Complex Prompts** - Tests model capabilities with HTML analysis tasks
3. **Fallback Models** - Tests alternative models (gemini-2.0-flash, gemini-1.5-pro, gemini-1.5-flash)

### Current Findings (as of testing)

- ✅ **Gemini-2.5-pro**: Available but with response format issues
  - Model responds successfully
  - Has unique `thoughtsTokenCount` indicating internal reasoning
  - Response time: ~2-3 seconds
  - Text content may be missing from response parts
  
- ✅ **Gemini-2.0-flash**: Fully functional
  - Fast response time: ~0.5 seconds
  - Complete text responses
  - Recommended as primary fallback
  
- ❌ **Gemini-1.5-pro**: Quota exceeded (429 errors)
  - Free tier limits reached
  - Not recommended for production use
  
- ✅ **Gemini-1.5-flash**: Functional but slower
  - Response time: ~1 second
  - Good secondary fallback option

### Prerequisites

- `GOOGLE_API_KEY` set in `.env.local`
- Node.js with ES modules support
- `@google/genai` package installed

### Error Codes

The script provides detailed error analysis:
- **429**: Quota exceeded or rate limited
- **404**: Model not found or unavailable
- **401/403**: Authentication issues
- **Response format issues**: Model available but response structure changed

### Recommendations

Based on current testing:
1. Use **gemini-2.0-flash** as primary model (fast, reliable)
2. Use **gemini-1.5-flash** as secondary fallback
3. Monitor **gemini-2.5-pro** for response format fixes
4. Avoid **gemini-1.5-pro** due to quota limitations