#!/bin/bash

# Docker Setup Script for WhatsApp Bot Engine
# This script helps set up the development environment quickly

set -e

echo "🚀 Setting up WhatsApp Bot Engine Docker Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are installed"

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_status "Created .env file from .env.example"
        print_warning "Please edit .env file with your configuration before continuing"
        echo ""
        echo "Required variables to set:"
        echo "- TWILIO_ACCOUNT_SID"
        echo "- TWILIO_AUTH_TOKEN"
        echo "- TWILIO_PHONE_NUMBER"
        echo "- GITHUB_TOKEN"
        echo "- GEMINI_API_KEY"
        echo ""
        read -p "Press Enter to continue after editing .env file..."
    else
        print_error ".env.example file not found. Please create environment configuration."
        exit 1
    fi
else
    print_status ".env file exists"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p workspaces sessions logs

# Check if pnpm-lock.yaml exists
if [ ! -f pnpm-lock.yaml ]; then
    print_warning "pnpm-lock.yaml not found. You may need to run 'pnpm install' first."
fi

# Function to handle cleanup
cleanup() {
    print_status "Cleaning up containers and volumes..."
    docker-compose down -v
    docker system prune -f
    print_status "Cleanup completed"
}

# Function to start development environment
start_dev() {
    print_status "Starting development environment..."
    
    # Build and start containers
    docker-compose up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if docker-compose ps | grep -q "Up"; then
        print_status "Services are running!"
        echo ""
        echo "🎉 Development environment is ready!"
        echo "======================================="
        echo "• Bot Engine: http://localhost:3000"
        echo "• Health Check: http://localhost:3000/health"
        echo "• Database Admin: http://localhost:8080"
        echo "• Redis: localhost:6379"
        echo "• PostgreSQL: localhost:5432"
        echo ""
        echo "To view logs: docker-compose logs -f"
        echo "To stop: docker-compose down"
    else
        print_error "Some services failed to start. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Function to start production environment
start_prod() {
    print_status "Building production image..."
    
    # Build production image
    docker build -f apps/bot-engine/Dockerfile -t whatsite-bot-engine .
    
    if [ $? -eq 0 ]; then
        print_status "Production image built successfully!"
        echo ""
        echo "To run production container:"
        echo "docker run -p 3000:3000 --env-file .env whatsite-bot-engine"
    else
        print_error "Failed to build production image"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests in containers..."
    
    # Start test environment
    docker-compose up -d
    
    # Wait for services
    sleep 10
    
    # Run tests
    docker-compose exec bot-engine npm test
    
    # Stop test environment
    docker-compose down
}

# Main menu
echo ""
echo "What would you like to do?"
echo "1) Start development environment"
echo "2) Build production image"
echo "3) Run tests"
echo "4) Clean up containers and volumes"
echo "5) Show logs"
echo "6) Exit"
echo ""

while true; do
    read -p "Enter your choice (1-6): " choice
    case $choice in
        1)
            start_dev
            break
            ;;
        2)
            start_prod
            break
            ;;
        3)
            run_tests
            break
            ;;
        4)
            cleanup
            break
            ;;
        5)
            if docker-compose ps | grep -q "Up"; then
                docker-compose logs -f
            else
                print_warning "No containers are running. Start development environment first."
            fi
            ;;
        6)
            print_status "Goodbye!"
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please enter 1-6."
            ;;
    esac
done