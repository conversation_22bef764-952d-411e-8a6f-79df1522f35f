#!/usr/bin/env node

/**
 * Test script to check Gemini-2.5-pro availability
 * Usage: node scripts/test-gemini-2.5-pro.mjs
 */

import { GoogleGenAI } from '@google/genai';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

if (!GOOGLE_API_KEY) {
    console.error('❌ GOOGLE_API_KEY not found in environment variables');
    console.error('Please set GOOGLE_API_KEY in your .env.local file');
    process.exit(1);
}

console.log('🔍 Testing Gemini-2.5-pro availability...');
console.log(`🔑 API Key present: ${GOOGLE_API_KEY.substring(0, 8)}...`);

const genAI = new GoogleGenAI({ apiKey: GOOGLE_API_KEY });

async function testGemini25Pro() {
    try {
        console.log('\n📋 Test 1: Basic model availability check');
        
        const startTime = Date.now();
        
        const result = await genAI.models.generateContent({
            model: "gemini-2.5-pro",
            contents: "Hello! Please respond with 'Gemini-2.5-pro is working' to confirm you're available.",
            config: {
                temperature: 0.1,
                maxOutputTokens: 100
            }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // Debug: Log the full response structure
        console.log('🔍 Debug - Full response structure:', JSON.stringify(result, null, 2));
        
        // Extract response text with multiple fallback methods
        let responseText = '';
        
        // Method 1: Standard structure
        if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
            responseText = result.candidates[0].content.parts[0].text.trim();
        }
        // Method 2: Direct text property
        else if (result.text) {
            responseText = result.text.trim();
        }
        // Method 3: Response property
        else if (result.response?.text) {
            responseText = result.response.text.trim();
        }
        // Method 4: Check if result itself is a string
        else if (typeof result === 'string') {
            responseText = result.trim();
        }
        // Method 5: Try to find text anywhere in the response
        else {
            const findText = (obj) => {
                if (typeof obj === 'string') return obj;
                if (typeof obj !== 'object' || obj === null) return null;
                
                for (const key in obj) {
                    if (key === 'text' && typeof obj[key] === 'string') {
                        return obj[key];
                    }
                    const found = findText(obj[key]);
                    if (found) return found;
                }
                return null;
            };
            
            const foundText = findText(result);
            if (foundText) {
                responseText = foundText.trim();
            }
        }
        
        if (!responseText) {
            throw new Error('No text content found in response - model may not be available or response format changed');
        }
        
        console.log(`✅ Gemini-2.5-pro is available!`);
        console.log(`📝 Response: "${responseText}"`);
        console.log(`⏱️  Response time: ${responseTime}ms`);
        
        return true;
        
    } catch (error) {
        console.error(`❌ Gemini-2.5-pro test failed:`);
        console.error(`Error type: ${error.name || 'Unknown'}`);
        console.error(`Error message: ${error.message}`);
        
        // Check for specific error types
        if (error.message.includes('overloaded') || error.message.includes('quota') || error.message.includes('429')) {
            console.error('🚫 Model appears to be overloaded or quota exceeded');
        } else if (error.message.includes('not found') || error.message.includes('invalid') || error.message.includes('404')) {
            console.error('🚫 Model not found or invalid model name - Gemini-2.5-pro may not be available yet');
        } else if (error.message.includes('permission') || error.message.includes('auth') || error.message.includes('401') || error.message.includes('403')) {
            console.error('🚫 Authentication or permission issue');
        } else if (error.message.includes('No text content found')) {
            console.error('🚫 Model responded but with unexpected format - may be in preview/limited access');
        } else {
            console.error('🚫 Unknown error occurred');
        }
        
        return false;
    }
}

async function testFallbackModels() {
    console.log('\n📋 Test 2: Testing fallback models');
    
    const fallbackModels = [
        'gemini-2.5-flash',
        'gemini-2.0-flash',
        'gemini-1.5-pro',
        'gemini-1.5-flash'
    ];
    
    for (const model of fallbackModels) {
        try {
            console.log(`\n🔍 Testing ${model}...`);
            
            const startTime = Date.now();
            
            const result = await genAI.models.generateContent({
                model: model,
                contents: `Hello! Please respond with '${model} is working' to confirm you're available.`,
                config: {
                    temperature: 0.1,
                    maxOutputTokens: 50
                }
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // Extract response text
            let responseText = '';
            if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                responseText = result.candidates[0].content.parts[0].text.trim();
            } else if (result.text) {
                responseText = result.text.trim();
            }
            
            console.log(`✅ ${model} is available!`);
            console.log(`📝 Response: "${responseText}"`);
            console.log(`⏱️  Response time: ${responseTime}ms`);
            
        } catch (error) {
            console.error(`❌ ${model} failed: ${error.message}`);
        }
    }
}

async function testModelCapabilities() {
    console.log('\n📋 Test 3: Testing model capabilities with complex prompt');
    
    try {
        const complexPrompt = `Please analyze this simple HTML and provide a brief summary:
        
<html>
<head><title>Test Page</title></head>
<body>
    <h1>Welcome</h1>
    <p>This is a test page.</p>
</body>
</html>

Provide a one-sentence summary of what this HTML creates.`;

        console.log('🔍 Testing complex prompt with Gemini-2.5-pro...');
        
        const startTime = Date.now();
        
        const result = await genAI.models.generateContent({
            model: "gemini-2.5-pro",
            contents: complexPrompt,
            config: {
                temperature: 0.3,
                maxOutputTokens: 100
            }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // Extract response text
        let responseText = '';
        if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
            responseText = result.candidates[0].content.parts[0].text.trim();
        } else if (result.text) {
            responseText = result.text.trim();
        }
        
        console.log(`✅ Complex prompt test successful!`);
        console.log(`📝 Analysis: "${responseText}"`);
        console.log(`⏱️  Response time: ${responseTime}ms`);
        
        return true;
        
    } catch (error) {
        console.error(`❌ Complex prompt test failed: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🚀 Starting Gemini-2.5-pro availability tests...\n');
    
    const test1Result = await testGemini25Pro();
    
    if (test1Result) {
        await testModelCapabilities();
    }
    
    await testFallbackModels();
    
    console.log('\n📊 Test Summary:');
    console.log(`Gemini-2.5-pro basic test: ${test1Result ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (test1Result) {
        console.log('🎉 Gemini-2.5-pro is available and working correctly!');
    } else {
        console.log('⚠️  Gemini-2.5-pro is not available. Consider using fallback models.');
    }
    
    console.log('\n💡 Tip: If Gemini-2.5-pro is overloaded, try again in a few minutes or use gemini-2.0-flash as a fallback.');
}

// Run the tests
main().catch(console.error);