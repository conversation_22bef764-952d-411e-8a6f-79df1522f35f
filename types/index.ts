/**
 * Core type definitions for the WhatsApp Website Bot
 */

export interface WhatsAppMessage {
  From: string;
  Body: string;
  NumMedia: number;
  MediaUrl0?: string;
}

export interface ProjectData {
  projectName: string;
  description: string;
  url: string;
  htmlContent: string;
  githubRepoUrl?: string | null;
  githubRepoName?: string | null;
  lastCommitSha?: string | null;
}

export interface UserProject {
  id: number;
  phone_number: string;
  project_name: string;
  description: string;
  url: string;
  html_content: string;
  github_repo_url?: string | null;
  github_repo_name?: string | null;
  last_commit_sha?: string | null;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface IntentClassification {
  intent: 'new_site' | 'edit_site' | 'import_repo' | 'scaffold_mode';
  confidence: number;
  reasoning: string;
  repositoryInfo?: RepositoryInfo;
}

export interface RepositoryInfo {
  type: 'url' | 'name';
  fullUrl?: string | null;
  repoName?: string | null;
  owner?: string;
  repo?: string;
  repoIdentifier?: string;
}

export interface StatusMessageData {
  [key: string]: any;
  text?: string;
  transcript?: string;
  intent?: string;
  description?: string;
  projectName?: string;
  url?: string;
  repoName?: string;
  error?: string;
  message?: string;
}

export interface GitHubFileContent {
  name: string;
  path: string;
  content: string;
  type: 'file' | 'dir';
}

export interface GitHubCommitData {
  message: string;
  tree: string;
  parents: string[];
}

export interface DeploymentResponse {
  url?: string;
  alias?: string[];
  name?: string;
  readyState?: string;
  error?: {
    message: string;
  };
}

export interface EnvironmentValidation {
  isValid: boolean;
  missing: string[];
  envVars: Record<string, boolean>;
}

export interface GitHubRepositoryData {
  repoUrl: string;
  repoName: string;
  cloneUrl: string;
  defaultBranch: string;
}

export interface GitHubFileData {
  content: string;
  sha: string;
  size: number;
}

export interface GitHubCommitData {
  sha: string;
  html_url: string;
}

export interface GitHubCreateResponse {
  commit: GitHubCommitData;
}

export interface RepositoryValidation {
  isValid: boolean;
  error?: string;
  message?: string;
  repoData?: {
    fullName: string;
    description: string | null;
    htmlUrl: string;
    cloneUrl: string;
    defaultBranch: string;
    createdAt: string;
    updatedAt: string;
  };
}


export interface RepositoryMetadata {
  fullName: string;
  name: string;
  description: string;
  htmlUrl: string;
  cloneUrl: string;
  defaultBranch: string;
  createdAt: string;
  updatedAt: string;
  latestCommitSha: string | null;
}

export interface GitHubCreateOptions {
  genAI?: any;
  userMessage?: string;
}

export interface GitHubUpdateOptions {
  userMessage?: string;
  oldContent?: string;
  projectName?: string;
  genAI?: any;
}

// Type aliases for consistency with github.ts
export type GitHubValidationResult = RepositoryValidation;

export interface GitHubImportData {
  projectName: string;
  description: string;
  htmlContent: string;
  githubRepoUrl: string;
  githubRepoName: string;
  lastCommitSha: string | null;
  url: string | null;
}