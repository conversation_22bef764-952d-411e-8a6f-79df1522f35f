# WhatsApp Website Bot - Monorepo

A monorepo structure for the WhatsApp Website Bot integration with Cyrus patterns.

## Structure

```
whatsite-bot/
├── apps/
│   ├── webhook-handler/          # Vercel function for WhatsApp webhooks
│   └── bot-engine/               # Fly.io app for persistent bot logic
├── packages/
│   ├── core/                     # Shared types and utilities
│   ├── workspace-manager/        # Git worktree management
│   └── gemini-runner/            # Gemini CLI wrapper
├── package.json                  # Root workspace configuration
├── pnpm-workspace.yaml           # pnpm workspace definition
└── tsconfig.base.json            # Base TypeScript configuration
```

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 9.0+

### Installation

```bash
# Install dependencies for all workspaces
pnpm install

# Build all packages
pnpm build

# Run development servers
pnpm dev
```

### Development

```bash
# Start webhook handler (Vercel)
pnpm start:webhook

# Start bot engine (Fly.io)
pnpm start:bot-engine

# Build specific package
pnpm --filter core build

# Test specific package
pnpm --filter core test
```

## Apps

### webhook-handler

Vercel function that handles incoming WhatsApp webhooks. Processes messages and coordinates with the bot engine.

**Deploy:**
```bash
pnpm --filter webhook-handler deploy
```

### bot-engine

Fly.io application that handles persistent bot logic, WebSocket connections, and long-running operations.

**Deploy:**
```bash
pnpm --filter bot-engine deploy
```

## Packages

### core

Shared types, utilities, and configuration used across all apps and packages.

### workspace-manager

Git worktree management utilities for handling repository operations and workspace management.

### gemini-runner

Wrapper around gemini-cli and AI integration utilities for content generation.

## Environment Variables

Create a `.env.local` file in the root directory:

```env
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
OPENAI_API_KEY=
GOOGLE_API_KEY=
VERCEL_API_TOKEN=
VERCEL_TEAM_ID=
SUPABASE_URL=
SUPABASE_ANON_KEY=
GITHUB_TOKEN=
```

## Scripts

- `pnpm build` - Build all packages and apps
- `pnpm dev` - Start all development servers
- `pnpm test` - Run all tests
- `pnpm typecheck` - Type check all packages
- `pnpm clean` - Clean all build artifacts
- `pnpm format` - Format all code
- `pnpm lint` - Lint all code

## Architecture

This monorepo follows Cyrus patterns with:

- **Workspace dependencies**: Packages reference each other using `workspace:*`
- **Shared configuration**: Common TypeScript and build configuration
- **Path mappings**: Clean imports between packages
- **Deployment separation**: Different deployment strategies for different components

## Next Steps

1. Set up CI/CD pipelines for monorepo
2. Configure environment-specific deployments