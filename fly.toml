# fly.toml app configuration file generated for whatsite-bot-engine on 2025-07-17T22:40:48+02:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'whatsite-bot-engine'
primary_region = 'sjc'
kill_signal = 'SIGINT'
kill_timeout = '10s'

[build]
  dockerfile = 'apps/bot-engine/Dockerfile'

[deploy]
  strategy = 'rolling'
  release_command = "echo 'Starting production deployment...'"

[env]
  GRACEFUL_SHUTDOWN_TIMEOUT = '30s'
  HEALTH_CHECK_TIMEOUT = '5s'
  LOG_LEVEL = 'info'
  METRICS_ENABLED = 'true'
  NODE_ENV = 'production'
  PORT = '3000'
  WORKSPACE_BASE_DIR = "/data/workspaces"
  SESSION_STORAGE_DIR = "/data/sessions"

[[mounts]]
  source      = "whatsite_data"
  destination = "/data"

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'off'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

  [http_service.concurrency]
    type = 'connections'
    hard_limit = 500
    soft_limit = 400

  [[http_service.checks]]
    interval = '10s'
    timeout = '5s'
    grace_period = '10s'
    method = 'GET'
    path = '/health'
    protocol = 'http'

    [http_service.checks.headers]
      User-Agent = 'Fly Health Check'
      X-Forwarded-Proto = 'https'

  [[http_service.checks]]
    interval = '30s'
    timeout = '10s'
    grace_period = '15s'
    method = 'GET'
    path = '/ready'
    protocol = 'http'

  [[http_service.checks]]
    interval = '1m0s'
    timeout = '15s'
    grace_period = '20s'
    method = 'GET'
    path = '/startup'
    protocol = 'http'

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[[metrics]]
  port = 9090
  path = '/metrics'
