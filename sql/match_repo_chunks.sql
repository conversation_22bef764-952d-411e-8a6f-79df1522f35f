-- SQL function for vector similarity search in repo_chunks
-- This function should be created in your Supabase database

create or replace function match_repo_chunks(
  query_embedding vector(768),
  project_id bigint,
  match_threshold float,
  match_count int
)
returns table (
  id bigint,
  project_id bigint,
  file_path varchar(500),
  chunk_id integer,
  content text,
  embedding vector(768),
  similarity float
)
language sql stable
as $$
  select
    rc.id,
    rc.project_id,
    rc.file_path,
    rc.chunk_id,
    rc.content,
    rc.embedding,
    1 - (rc.embedding <=> query_embedding) as similarity
  from repo_chunks rc
  where rc.project_id = match_repo_chunks.project_id
    and 1 - (rc.embedding <=> query_embedding) > match_threshold
  order by rc.embedding <=> query_embedding
  limit match_count;
$$;