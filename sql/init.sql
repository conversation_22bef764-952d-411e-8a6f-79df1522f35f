-- Initialize database for WhatsApp Bot Engine development
-- This script runs when the PostgreSQL container starts

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS whatsite_dev;

-- Connect to the database
\c whatsite_dev;

-- Create basic tables for development
CREATE TABLE IF NOT EXISTS sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    session_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS workspaces (
    id SERIAL PRIMARY KEY,
    workspace_id VARCHAR(255) UNIQUE NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    project_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    github_repo VARCHAR(255),
    vercel_project_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    workspace_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(session_id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS deployment_history (
    id SERIAL PRIMARY KEY,
    workspace_id VARCHAR(255) NOT NULL,
    deployment_type VARCHAR(50) NOT NULL, -- 'github', 'vercel', etc.
    deployment_url TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    deployment_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(workspace_id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sessions_phone_number ON sessions(phone_number);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_workspaces_session_id ON workspaces(session_id);
CREATE INDEX IF NOT EXISTS idx_workspaces_status ON workspaces(status);
CREATE INDEX IF NOT EXISTS idx_deployment_history_workspace_id ON deployment_history(workspace_id);
CREATE INDEX IF NOT EXISTS idx_deployment_history_status ON deployment_history(status);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_sessions_updated_at 
    BEFORE UPDATE ON sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workspaces_updated_at 
    BEFORE UPDATE ON workspaces 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data for development
INSERT INTO sessions (session_id, user_id, phone_number, session_data) 
VALUES 
    ('dev_session_1', 'dev_user_1', '+1234567890', '{"name": "Development User", "preferences": {"theme": "light"}}'),
    ('dev_session_2', 'dev_user_2', '+0987654321', '{"name": "Test User", "preferences": {"theme": "dark"}}')
ON CONFLICT (session_id) DO NOTHING;

INSERT INTO workspaces (workspace_id, session_id, project_name, status, workspace_path)
VALUES 
    ('workspace_1', 'dev_session_1', 'my-awesome-website', 'active', '/app/workspaces/workspace_1'),
    ('workspace_2', 'dev_session_2', 'portfolio-site', 'active', '/app/workspaces/workspace_2')
ON CONFLICT (workspace_id) DO NOTHING;

-- Create a view for active sessions with workspace info
CREATE OR REPLACE VIEW active_sessions_with_workspaces AS
SELECT 
    s.session_id,
    s.user_id,
    s.phone_number,
    s.session_data,
    s.created_at as session_created_at,
    w.workspace_id,
    w.project_name,
    w.github_repo,
    w.vercel_project_id,
    w.status as workspace_status,
    w.workspace_path
FROM sessions s
LEFT JOIN workspaces w ON s.session_id = w.session_id
WHERE s.expires_at IS NULL OR s.expires_at > CURRENT_TIMESTAMP;

-- Grant permissions to the whatsite user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO whatsite;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO whatsite;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO whatsite;

-- Print success message
SELECT 'Database initialized successfully!' as status;