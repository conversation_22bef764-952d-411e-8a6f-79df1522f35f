version: '3.8'

services:
  bot-engine:
    build:
      context: .
      dockerfile: apps/bot-engine/Dockerfile
      target: dependencies
    container_name: whatsite-bot-engine
    ports:
      - "3000:3000"
    volumes:
      # Source code for hot reload
      - ./apps/bot-engine/src:/app/apps/bot-engine/src
      - ./packages:/app/packages
      # Persistent workspace data
      - bot-workspaces:/app/workspaces
      - bot-sessions:/app/sessions
      # Git configuration
      - ~/.gitconfig:/home/<USER>/.gitconfig:ro
      - ~/.ssh:/home/<USER>/.ssh:ro
    environment:
      - NODE_ENV=development
      - PORT=3000
      - WORKSPACE_BASE_DIR=/app/workspaces
      - SESSION_STORAGE_DIR=/app/sessions
      # Twilio configuration
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}
      # GitHub configuration
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - GITHUB_OWNER=${GITHUB_OWNER}
      - GITHUB_REPO=${GITHUB_REPO}
      # Vercel configuration
      - VERCEL_TOKEN=${VERCEL_TOKEN}
      - VERCEL_PROJECT_ID=${VERCEL_PROJECT_ID}
      - VERCEL_ORG_ID=${VERCEL_ORG_ID}
      # Supabase configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      # Gemini configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    command: pnpm --filter bot-engine dev
    depends_on:
      - redis
    networks:
      - bot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: whatsite-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - bot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Development database (PostgreSQL)
  postgres:
    image: postgres:15-alpine
    container_name: whatsite-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=whatsite_dev
      - POSTGRES_USER=whatsite
      - POSTGRES_PASSWORD=dev_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - bot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U whatsite -d whatsite_dev"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Development tools
  adminer:
    image: adminer:latest
    container_name: whatsite-adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - bot-network
    restart: unless-stopped
    environment:
      - ADMINER_DEFAULT_SERVER=postgres

networks:
  bot-network:
    driver: bridge

volumes:
  bot-workspaces:
    driver: local
  bot-sessions:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local