# Application Configuration
NODE_ENV=development
PORT=3000
WORKSPACE_BASE_DIR=/app/workspaces
SESSION_STORAGE_DIR=/app/sessions

# Twilio Configuration (WhatsApp)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# GitHub Configuration
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_OWNER=your_github_username
GITHUB_REPO=your_default_repository

# Vercel Configuration
VERCEL_TOKEN=your_vercel_token
VERCEL_PROJECT_ID=your_vercel_project_id
VERCEL_ORG_ID=your_vercel_org_id

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Configuration
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key

# Database Configuration (Development)
DATABASE_URL=************************************************/whatsite_dev

# Redis Configuration (Development)
REDIS_URL=redis://redis:6379

# Security Configuration
JWT_SECRET=your_jwt_secret_key
WEBHOOK_SECRET=your_webhook_secret

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Feature Flags
ENABLE_WEBSOCKET=true
ENABLE_HEALTH_CHECK=true
ENABLE_METRICS=false

# Development Settings
DEBUG=false
VERBOSE_LOGGING=false