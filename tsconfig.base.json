{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "lib": ["ES2022"], "moduleResolution": "nodenext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "allowJs": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@whatsite-bot/core": ["packages/core/src"], "@whatsite-bot/core/*": ["packages/core/src/*"], "@whatsite-bot/workspace-manager": ["packages/workspace-manager/src"], "@whatsite-bot/workspace-manager/*": ["packages/workspace-manager/src/*"], "@whatsite-bot/gemini-runner": ["packages/gemini-runner/src"], "@whatsite-bot/gemini-runner/*": ["packages/gemini-runner/src/*"]}}, "exclude": ["node_modules", "dist", "build"]}