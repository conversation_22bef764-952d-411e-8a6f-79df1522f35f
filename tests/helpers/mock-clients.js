/**
 * Mock clients for testing
 */

import { vi } from 'vitest'

/**
 * Mock Google AI client
 */
export const createMockGenAI = () => ({
  models: {
    generateContent: vi.fn().mockResolvedValue({
      text: 'feat: create test website'
    })
  }
})

/**
 * Mock Twilio client
 */
export const createMockTwilioClient = () => ({
  messages: {
    create: vi.fn().mockResolvedValue({
      sid: 'mock-message-sid',
      status: 'sent'
    })
  }
})

/**
 * Mock Supabase client with proper chaining
 */
export const createMockSupabaseClient = () => {
  // Create a shared chainable mock that all operations return
  const createChainableMock = () => {
    const mockChain = {}
    
    // Define all chainable methods
    const chainableMethods = [
      'select', 'insert', 'update', 'delete', 'upsert',
      'eq', 'neq', 'gt', 'gte', 'lt', 'lte', 'like', 'ilike', 'is', 'in',
      'contains', 'containedBy', 'rangeGt', 'rangeGte', 'rangeLt', 'rangeLte',
      'rangeAdjacent', 'overlaps', 'textSearch', 'match', 'not', 'or', 'filter',
      'order', 'limit', 'range', 'returns'
    ]
    
    // Create mock functions for all chainable methods
    chainableMethods.forEach(method => {
      mockChain[method] = vi.fn().mockReturnValue(mockChain)
    })
    
    // Terminal methods that return promises
    mockChain.single = vi.fn().mockResolvedValue({ data: null, error: null })
    mockChain.maybeSingle = vi.fn().mockResolvedValue({ data: null, error: null })
    mockChain.csv = vi.fn().mockResolvedValue({ data: '', error: null })
    mockChain.geojson = vi.fn().mockResolvedValue({ data: {}, error: null })
    mockChain.explain = vi.fn().mockResolvedValue({ data: '', error: null })
    mockChain.rollback = vi.fn().mockResolvedValue({ data: null, error: null })
    mockChain.then = vi.fn().mockResolvedValue({ data: [], error: null })
    
    return mockChain
  }

  // Create the main client
  const mockClient = {
    from: vi.fn().mockImplementation(() => createChainableMock()),
    rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn().mockResolvedValue({ data: null, error: null }),
        download: vi.fn().mockResolvedValue({ data: null, error: null }),
        list: vi.fn().mockResolvedValue({ data: [], error: null }),
        remove: vi.fn().mockResolvedValue({ data: null, error: null }),
        createSignedUrl: vi.fn().mockResolvedValue({ data: null, error: null }),
        createSignedUrls: vi.fn().mockResolvedValue({ data: [], error: null }),
        getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'http://example.com' } })
      })
    },
    auth: {
      signUp: vi.fn().mockResolvedValue({ data: null, error: null }),
      signInWithPassword: vi.fn().mockResolvedValue({ data: null, error: null }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      getUser: vi.fn().mockResolvedValue({ data: null, error: null }),
      getSession: vi.fn().mockResolvedValue({ data: null, error: null }),
      onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } })
    },
    realtime: {
      channel: vi.fn().mockReturnValue({
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn().mockReturnThis(),
        unsubscribe: vi.fn().mockReturnThis()
      })
    }
  }

  return mockClient
}

/**
 * Mock OpenAI client
 */
export const createMockOpenAI = () => ({
  audio: {
    transcriptions: {
      create: vi.fn().mockResolvedValue({
        text: 'This is a test transcription'
      })
    }
  }
})

/**
 * Mock fetch for API calls
 */
export const createMockFetch = () => {
  const mockFetch = vi.fn()
  
  // Default successful responses
  mockFetch.mockImplementation((url) => {
    if (url.includes('github.com/repos')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          full_name: 'test-user/test-repo',
          html_url: 'https://github.com/test-user/test-repo',
          clone_url: 'https://github.com/test-user/test-repo.git',
          default_branch: 'main'
        }),
        text: vi.fn().mockResolvedValue('mock file content')
      })
    }
    
    if (url.includes('github.com') && url.includes('/contents/')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          content: Buffer.from('mock file content').toString('base64'),
          encoding: 'base64'
        }),
        text: vi.fn().mockResolvedValue('mock file content')
      })
    }
    
    if (url.includes('vercel.com')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          url: 'https://test-deployment.vercel.app',
          deploymentId: 'test-deployment-id'
        }),
        text: vi.fn().mockResolvedValue('{}')
      })
    }
    
    // Default response
    return Promise.resolve({
      ok: true,
      status: 200,
      json: vi.fn().mockResolvedValue({}),
      text: vi.fn().mockResolvedValue('{}')
    })
  })
  
  return mockFetch
}

/**
 * Setup global mocks for a test
 */
export const setupGlobalMocks = () => {
  global.fetch = createMockFetch()
  return {
    genAI: createMockGenAI(),
    twilioClient: createMockTwilioClient(),
    supabase: createMockSupabaseClient(),
    openai: createMockOpenAI()
  }
}

/**
 * Reset all mocks
 */
export const resetAllMocks = () => {
  vi.clearAllMocks()
  vi.resetAllMocks()
}