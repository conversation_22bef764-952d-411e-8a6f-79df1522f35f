/**
 * Global test setup for Vitest
 * This file is run before all tests
 */

import { vi } from 'vitest'
import dotenv from 'dotenv'

// Load test environment variables
dotenv.config({ path: '.env.test' })
dotenv.config({ path: '.env.local' })

// Global test configuration
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: console.warn,
  error: console.error,
}

// Mock environment variables for tests that don't need real API calls
if (!process.env.GITHUB_TOKEN) {
  process.env.GITHUB_TOKEN = 'test_github_token'
}
if (!process.env.GOOGLE_API_KEY) {
  process.env.GOOGLE_API_KEY = 'test_google_api_key'
}
if (!process.env.TWILIO_ACCOUNT_SID) {
  process.env.TWILIO_ACCOUNT_SID = 'test_twilio_sid'
}
if (!process.env.TWILIO_AUTH_TOKEN) {
  process.env.TWILIO_AUTH_TOKEN = 'test_twilio_token'
}
if (!process.env.SUPABASE_URL) {
  process.env.SUPABASE_URL = 'https://test.supabase.co'
}
if (!process.env.SUPABASE_ANON_KEY) {
  process.env.SUPABASE_ANON_KEY = 'test_supabase_key'
}
if (!process.env.VERCEL_API_TOKEN) {
  process.env.VERCEL_API_TOKEN = 'test_vercel_token'
}

// Global test timeout
vi.setConfig({ testTimeout: 10000 })