# Testing Framework

This directory contains a comprehensive testing setup for the WhatsApp Website Bot using Vitest as the testing framework.

## 📁 Directory Structure

```
tests/
├── README.md                          # This file
├── vitest.config.js                   # Vitest configuration (in root)
├── run-tests.js                       # Custom test runner script
├── helpers/                           # Test utilities and helpers
│   ├── test-setup.js                  # Global test setup
│   └── mock-clients.js                # Mock implementations
├── fixtures/                          # Test data and fixtures
│   └── test-data.js                   # Reusable test data
├── unit/                              # Unit tests (isolated, fast)
│   ├── lib/                           # Library module tests
│   │   ├── ai-commit-generator.test.js
│   │   ├── intent-classifier.test.js
│   │   ├── utils.test.js
│   │   ├── database.test.js
│   │   ├── github.test.js
│   │   ├── messaging.test.js
│   │   ├── website-generator.test.js
│   │   └── vercel-git.test.js
│   └── api/                           # API handler tests
│       └── whatsapp.test.js
├── integration/                       # Integration tests (with real dependencies)
│   └── whatsapp-workflow.test.js
└── legacy/                            # Original standalone test files
    ├── README.md
    ├── test-ai-commit-generator.js
    ├── test-intent-classifier.js
    └── test-new-repository-description.js
```

## 🚀 Quick Start

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm test
```

### Run Specific Test Suites
```bash
# Unit tests only (fast, isolated)
npm run test:unit

# Integration tests only (slower, real dependencies)
npm run test:integration

# Watch mode for development
npm run test:watch

# Generate coverage report
npm run test:coverage

# Open Vitest UI
npm run test:ui
```

### Custom Test Runner
```bash
# Use the custom test runner for more options
npm run test:runner help
npm run test:runner unit
npm run test:runner all
npm run test:runner check
```

## 🧪 Test Types

### Unit Tests (`tests/unit/`)
- **Fast execution** (< 1 second per test file)
- **Isolated** - No external dependencies
- **Comprehensive mocking** - All external APIs mocked
- **High coverage** - Test edge cases and error conditions
- **Deterministic** - Same results every time

**What's tested:**
- Individual function behavior
- Error handling and edge cases
- Input validation
- Business logic
- Mock interactions

### Integration Tests (`tests/integration/`)
- **Real dependencies** - Actual API calls (in test environments)
- **End-to-end workflows** - Complete user journeys
- **Performance testing** - Response times and load handling
- **Error recovery** - System resilience
- **Environment dependent** - Requires test credentials

**What's tested:**
- Complete WhatsApp message workflows
- Database persistence
- External API integrations
- Error recovery scenarios
- Performance characteristics

## 🛠️ Test Utilities

### Mock Clients (`tests/helpers/mock-clients.js`)
Provides mock implementations for all external services:
- **Google AI (Gemini)** - Mocked content generation
- **Twilio** - Mocked WhatsApp messaging
- **Supabase** - Mocked database operations
- **GitHub API** - Mocked repository operations
- **Vercel API** - Mocked deployment operations
- **Fetch** - Mocked HTTP requests

### Test Data (`tests/fixtures/test-data.js`)
Comprehensive test data including:
- User message examples
- HTML content samples
- API response mocks
- Database record examples
- Environment variable sets

### Test Setup (`tests/helpers/test-setup.js`)
Global test configuration:
- Environment variable mocking
- Console output suppression
- Global test utilities
- Cleanup procedures

## 📊 Coverage Requirements

| Metric | Minimum | Target |
|--------|---------|--------|
| Lines | 80% | 90% |
| Functions | 80% | 90% |
| Branches | 75% | 85% |
| Statements | 80% | 90% |

## 🔧 Configuration

### Vitest Config (`vitest.config.js`)
- **ESM support** - Native ES modules
- **Path aliases** - `@/` for root imports
- **Coverage provider** - C8 for fast coverage
- **Test patterns** - Automatic test discovery
- **Mock handling** - Vi mocking utilities

### Environment Variables
Tests use mocked environment variables by default. For integration tests:

```bash
# Required for integration tests
export RUN_INTEGRATION_TESTS=true
export INTEGRATION_GOOGLE_AI_API_KEY=your_test_key
export INTEGRATION_TWILIO_ACCOUNT_SID=your_test_sid
export INTEGRATION_SUPABASE_URL=your_test_db_url
```

## 🎯 Writing Tests

### Unit Test Example
```javascript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { myFunction } from '@/lib/my-module.js'
import { createMockClient } from '@tests/helpers/mock-clients.js'

describe('MyModule', () => {
  let mockClient

  beforeEach(() => {
    mockClient = createMockClient()
  })

  it('should handle success case', async () => {
    mockClient.method.mockResolvedValue({ success: true })
    
    const result = await myFunction('input', mockClient)
    
    expect(result).toBe(true)
    expect(mockClient.method).toHaveBeenCalledWith('input')
  })

  it('should handle error case', async () => {
    mockClient.method.mockRejectedValue(new Error('API Error'))
    
    const result = await myFunction('input', mockClient)
    
    expect(result).toBe(null)
  })
})
```

### Integration Test Example
```javascript
import { describe, it, expect } from 'vitest'

describe.skipIf(!process.env.RUN_INTEGRATION_TESTS)('Integration Tests', () => {
  it('should work with real dependencies', async () => {
    // Test with real API calls
    const result = await realFunction()
    expect(result).toBeDefined()
  })
})
```

## 🚦 CI/CD Integration

### GitHub Actions (`.github/workflows/test.yml`)
Automated testing on:
- **Push to main/develop**
- **Pull requests**
- **Multiple Node.js versions** (18.x, 20.x)

Pipeline includes:
1. **Linting** - Code style and quality
2. **Unit Tests** - Fast isolated tests
3. **Integration Tests** - End-to-end workflows
4. **Coverage Report** - Code coverage analysis
5. **Security Audit** - Dependency vulnerability scan

### Test Reports
- **Coverage reports** uploaded to Codecov
- **Test results** archived as artifacts
- **PR comments** with coverage summary

## 🐛 Debugging Tests

### Watch Mode
```bash
npm run test:watch
```
- Automatically re-runs tests on file changes
- Interactive filtering and debugging
- Fast feedback loop

### Vitest UI
```bash
npm run test:ui
```
- Web-based test interface
- Visual test results and coverage
- Interactive debugging tools

### Debug Individual Tests
```bash
# Run specific test file
npx vitest run tests/unit/lib/github.test.js

# Run specific test pattern
npx vitest run --grep "should create repository"

# Run with verbose output
npx vitest run --reporter=verbose
```

## 📈 Performance

### Test Execution Times
- **Unit tests**: ~2-5 seconds total
- **Integration tests**: ~30-60 seconds total
- **Coverage generation**: ~10-15 seconds additional

### Optimization Strategies
- **Parallel execution** - Tests run concurrently
- **Smart mocking** - No real API calls in unit tests
- **Efficient setup** - Shared test utilities
- **Selective running** - Only run affected tests

## 🔄 Migration from Legacy Tests

The original standalone test files have been migrated to this new framework:

| Legacy File | New Location | Status |
|-------------|--------------|--------|
| `test-ai-commit-generator.js` | `tests/unit/lib/ai-commit-generator.test.js` | ✅ Migrated |
| `test-intent-classifier.js` | `tests/unit/lib/intent-classifier.test.js` | ✅ Migrated |
| `test-new-repository-description.js` | `tests/unit/lib/github.test.js` | ✅ Integrated |

### Benefits of Migration
- **10x faster execution** - Mocked dependencies
- **Better coverage** - More comprehensive test scenarios
- **CI/CD ready** - Automated testing pipeline
- **Developer friendly** - Watch mode and UI
- **Maintainable** - Structured organization

## 🤝 Contributing

### Adding New Tests
1. **Unit tests** for new functions/modules
2. **Integration tests** for new workflows
3. **Update mocks** for new external dependencies
4. **Add test data** to fixtures as needed

### Test Guidelines
- **Descriptive names** - Clear test descriptions
- **Arrange-Act-Assert** - Structured test organization
- **Mock external dependencies** - Keep unit tests isolated
- **Test edge cases** - Error conditions and boundaries
- **Maintain coverage** - Keep above minimum thresholds

### Running Before Commit
```bash
# Quick check
npm run test:unit

# Full validation
npm run test:runner all
```

## 📚 Resources

- [Vitest Documentation](https://vitest.dev/)
- [Vi Mocking Guide](https://vitest.dev/guide/mocking.html)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [Coverage Reports](./coverage/index.html) (after running coverage)

## 🆘 Troubleshooting

### Common Issues

**Tests not running:**
```bash
# Check environment
npm run test:runner check

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

**Mock issues:**
```bash
# Reset all mocks
import { resetAllMocks } from '@tests/helpers/mock-clients.js'
beforeEach(() => resetAllMocks())
```

**Coverage not generating:**
```bash
# Ensure c8 is installed
npm install --save-dev c8

# Run with explicit coverage
npx vitest run --coverage
```

**Integration tests failing:**
```bash
# Check environment variables
npm run test:runner check

# Run with debug output
DEBUG=* npm run test:integration
```

For more help, check the test runner help:
```bash
npm run test:runner help