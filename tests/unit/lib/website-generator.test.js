/**
 * Unit tests for website generation functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { generateWebsite } from '@/website-generator.ts'
import { createMockGenAI, resetAllMocks } from '@tests/helpers/mock-clients.js'
import { mockUserMessages, mockHtmlContent, mockEnvironmentVars } from '@tests/fixtures/test-data.js'

describe('Website Generator', () => {
  let mockGenAI
  let consoleSpy

  beforeEach(() => {
    mockGenAI = createMockGenAI()
    resetAllMocks()
    consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // Mock environment variables
    vi.stubEnv('GOOGLE_API_KEY', mockEnvironmentVars.complete.GOOGLE_API_KEY)
  })

  afterEach(() => {
    vi.restoreAllMocks()
    vi.unstubAllEnvs()
  })

  describe('generateWebsite', () => {
    it('should generate website successfully', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        text: mockHtmlContent.complex
      })

      const result = await generateWebsite(
        mockUserMessages.newSite.portfolio,
        mockGenAI
      )

      expect(result).toBe(mockHtmlContent.complex)
      expect(mockGenAI.models.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-pro',
        contents: expect.stringContaining(mockUserMessages.newSite.portfolio),
        config: expect.objectContaining({
          temperature: 1.0,
          topP: 0.8,
          maxOutputTokens: 131072
        })
      })
    })

    it('should handle AI generation errors', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('AI service unavailable'))

      await expect(generateWebsite(
        mockUserMessages.newSite.portfolio,
        mockGenAI
      )).rejects.toThrow('AI service unavailable')
    })

    it('should generate different content for different prompts', async () => {
      const testCases = [
        { prompt: mockUserMessages.newSite.portfolio, expected: 'portfolio' },
        { prompt: mockUserMessages.newSite.business, expected: 'business' },
        { prompt: mockUserMessages.newSite.ecommerce, expected: 'ecommerce' }
      ]

      for (const testCase of testCases) {
        mockGenAI.models.generateContent.mockResolvedValue({
          text: `<html><body>${testCase.expected} website</body></html>`
        })

        const result = await generateWebsite(testCase.prompt, mockGenAI)

        expect(result).toContain(testCase.expected)
        expect(result).toContain('<html>')
      }
    })

    it('should handle timeout errors with fallback', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('Gemini API call timed out after 270 seconds'))

      const result = await generateWebsite('create a website', mockGenAI)

      expect(result).toContain('<!DOCTYPE html>')
      expect(result).toContain('Website Coming Soon')
      expect(result).toContain('create a website')
    })

    it('should handle existing project edits', async () => {
      const existingProject = {
        project_name: 'test-project',
        description: 'A test project',
        html_content: '<html><body>Original content</body></html>'
      }

      mockGenAI.models.generateContent.mockResolvedValue({
        text: '<html><body>Updated content</body></html>'
      })

      const result = await generateWebsite(
        'change the color to blue',
        mockGenAI,
        existingProject
      )

      expect(result).toContain('Updated content')
      expect(mockGenAI.models.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-pro',
        contents: expect.stringContaining('change the color to blue'),
        config: expect.objectContaining({
          temperature: 0.7 // Lower temperature for edits
        })
      })
    })

    it('should clean up code block formatting', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        text: '```html\n<html><body>Test</body></html>\n```'
      })

      const result = await generateWebsite('create a website', mockGenAI)

      expect(result).toBe('<html><body>Test</body></html>')
      expect(result).not.toContain('```')
    })

    it('should validate and fix HTML structure', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        text: '<div>Content without proper HTML structure</div>'
      })

      const result = await generateWebsite('create a website', mockGenAI)

      expect(result).toContain('<!DOCTYPE html>')
      expect(result).toContain('<html lang="en">')
      expect(result).toContain('<meta charset="UTF-8">')
      expect(result).toContain('<meta name="viewport"')
    })

    it('should handle truncated responses', async () => {
      const existingProject = {
        project_name: 'test-project',
        description: 'A test project',
        html_content: '<html><body>Original content</body></html>'
      }

      // Simulate truncated response (no closing tags)
      mockGenAI.models.generateContent.mockResolvedValue({
        text: '<html><body>Truncated content'
      })

      const result = await generateWebsite(
        'edit the website',
        mockGenAI,
        existingProject
      )

      // Should fallback to original content
      expect(result).toBe(existingProject.html_content)
    })

    it('should handle very long prompts', async () => {
      const longPrompt = 'create a website '.repeat(1000)
      
      mockGenAI.models.generateContent.mockResolvedValue({
        text: mockHtmlContent.simple
      })

      const result = await generateWebsite(longPrompt, mockGenAI)

      expect(result).toBeTruthy()
      expect(result).toContain('<html>')
    })

    it('should use different models for new vs edit operations', async () => {
      const existingProject = {
        project_name: 'test-project',
        description: 'A test project',
        html_content: '<html><body>Original</body></html>'
      }

      mockGenAI.models.generateContent.mockResolvedValue({
        text: '<html><body>Updated</body></html>'
      })

      // Test edit operation
      await generateWebsite('edit the website', mockGenAI, existingProject)
      
      expect(mockGenAI.models.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-pro',
        contents: expect.any(String),
        config: expect.objectContaining({
          temperature: 0.7 // Lower temperature for edits
        })
      })

      // Test new creation
      await generateWebsite('create new website', mockGenAI)
      
      expect(mockGenAI.models.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-pro',
        contents: expect.any(String),
        config: expect.objectContaining({
          temperature: 1.0 // Higher temperature for new creations
        })
      })
    })
  })
})