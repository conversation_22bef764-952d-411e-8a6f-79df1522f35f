/**
 * Unit tests for AI commit generator
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  generateEnhancedCommitMessage,
  generateAICommitMessage,
  cleanCommitMessage,
  isValidCommitMessage,
  generateFallbackCommitMessage
} from '@/ai-commit-generator.ts'
import { createMockGenAI, resetAllMocks } from '@tests/helpers/mock-clients.js'
import { mockUserMessages, mockHtmlContent, mockAIResponses } from '@tests/fixtures/test-data.js'

describe('AI Commit Generator', () => {
  let mockGenAI

  beforeEach(() => {
    mockGenAI = createMockGenAI()
    resetAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('cleanCommitMessage', () => {
    it('should remove quotes from commit message', () => {
      expect(cleanCommitMessage('"feat: add feature"')).toBe('feat: add feature')
      expect(cleanCommitMessage("'style: update colors'")).toBe('style: update colors')
    })

    it('should remove newlines and extra whitespace', () => {
      expect(cleanCommitMessage('feat: add feature\nwith description')).toBe('feat: add feature')
      expect(cleanCommitMessage('  feat: add feature  ')).toBe('feat: add feature')
    })

    it('should truncate long messages to 72 characters', () => {
      const longMessage = 'feat: this is a very long commit message that exceeds the 72 character limit and should be truncated'
      const result = cleanCommitMessage(longMessage)
      expect(result.length).toBeLessThanOrEqual(72)
      expect(result).toMatch(/\.\.\.$/)
    })

    it('should handle empty or null messages', () => {
      expect(cleanCommitMessage('')).toBe('')
      expect(cleanCommitMessage(null)).toBe('')
      expect(cleanCommitMessage(undefined)).toBe('')
    })

    it('should remove trailing periods', () => {
      expect(cleanCommitMessage('feat: add feature.')).toBe('feat: add feature')
    })
  })

  describe('isValidCommitMessage', () => {
    it('should validate proper conventional commit format', () => {
      expect(isValidCommitMessage('feat: add new feature')).toBe(true)
      expect(isValidCommitMessage('fix: resolve bug')).toBe(true)
      expect(isValidCommitMessage('style: update colors')).toBe(true)
      expect(isValidCommitMessage('refactor: improve code structure')).toBe(true)
    })

    it('should reject invalid commit messages', () => {
      expect(isValidCommitMessage('invalid message')).toBe(false)
      expect(isValidCommitMessage('feat:')).toBe(false)
      expect(isValidCommitMessage('')).toBe(false)
      expect(isValidCommitMessage('a'.repeat(80))).toBe(false)
    })

    it('should reject messages that are too short or too long', () => {
      expect(isValidCommitMessage('feat: a')).toBe(false) // Too short
      expect(isValidCommitMessage('feat: ' + 'a'.repeat(70))).toBe(false) // Too long
    })

    it('should require meaningful content after colon', () => {
      expect(isValidCommitMessage('feat: ab')).toBe(false) // Less than 5 chars after colon
      expect(isValidCommitMessage('feat: abcde')).toBe(true) // Exactly 5 chars after colon
    })
  })

  describe('generateFallbackCommitMessage', () => {
    it('should generate appropriate fallback for new sites', () => {
      const result = generateFallbackCommitMessage('create a portfolio website', false)
      expect(result).toMatch(/^feat:/)
      expect(result).toContain('create website')
    })

    it('should generate appropriate fallback for edits', () => {
      const result = generateFallbackCommitMessage('change background color', true)
      expect(result).toMatch(/^(style|update|refactor|feat|fix):/)
    })

    it('should categorize edit types correctly', () => {
      const colorResult = generateFallbackCommitMessage('change background color', true)
      expect(colorResult).toMatch(/^style:/)

      const addResult = generateFallbackCommitMessage('add contact form', true)
      expect(addResult).toMatch(/^feat:/)

      const fixResult = generateFallbackCommitMessage('fix broken link', true)
      expect(fixResult).toMatch(/^fix:/)
    })

    it('should truncate long user messages', () => {
      const longMessage = 'change the background color to blue and make the text larger and add more content'
      const result = generateFallbackCommitMessage(longMessage, true)
      expect(result.length).toBeLessThanOrEqual(72)
    })
  })

  describe('generateAICommitMessage', () => {
    it('should generate commit message for new website creation', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'feat: create portfolio website with dark theme' }]
          }
        }]
      })

      const result = await generateAICommitMessage({
        userMessage: 'create a portfolio website',
        oldContent: null,
        newContent: mockHtmlContent.complex,
        isEdit: false,
        projectName: 'portfolio-website'
      }, mockGenAI)

      expect(result).toBe('feat: create portfolio website with dark theme')
      expect(mockGenAI.models.generateContent).toHaveBeenCalledOnce()
    })

    it('should generate commit message for website edits', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'style: update background color to blue' }]
          }
        }]
      })

      const result = await generateAICommitMessage({
        userMessage: 'change background color to blue',
        oldContent: mockHtmlContent.simple,
        newContent: mockHtmlContent.complex,
        isEdit: true,
        projectName: 'test-website'
      }, mockGenAI)

      expect(result).toBe('style: update background color to blue')
      expect(mockGenAI.models.generateContent).toHaveBeenCalledOnce()
    })

    it('should handle AI API errors gracefully', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('API Error'))

      const result = await generateAICommitMessage({
        userMessage: 'create a simple website',
        oldContent: null,
        newContent: mockHtmlContent.simple,
        isEdit: false,
        projectName: 'test-site'
      }, mockGenAI)

      // Should fall back to generateFallbackCommitMessage
      expect(result).toMatch(/^feat:/)
      expect(result).toContain('create website')
    })

    it('should handle malformed AI responses', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: []
      })

      const result = await generateAICommitMessage({
        userMessage: 'create a simple website',
        oldContent: null,
        newContent: mockHtmlContent.simple,
        isEdit: false,
        projectName: 'test-site'
      }, mockGenAI)

      // Should fall back to generateFallbackCommitMessage
      expect(result).toMatch(/^feat:/)
      expect(result).toContain('create website')
    })

    it('should clean and validate AI responses', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: '"feat: add new feature with very long description that exceeds normal limits and should be truncated"' }]
          }
        }]
      })

      const result = await generateAICommitMessage({
        userMessage: 'add new feature',
        oldContent: null,
        newContent: mockHtmlContent.simple,
        isEdit: false,
        projectName: 'test-site'
      }, mockGenAI)

      expect(result).not.toMatch(/^"/)
      expect(result.length).toBeLessThanOrEqual(72)
    })
  })

  describe('generateEnhancedCommitMessage', () => {
    it('should use AI-generated message when available', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'feat: create portfolio website with dark theme' }]
          }
        }]
      })

      const result = await generateEnhancedCommitMessage(
        'create a portfolio website',
        false,
        null,
        mockHtmlContent.complex,
        'portfolio-website',
        mockGenAI
      )

      expect(result).toBe('feat: create portfolio website with dark theme')
    })

    it('should fall back to basic message when AI fails', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('AI Error'))

      const result = await generateEnhancedCommitMessage(
        'create a simple website',
        false,
        null,
        mockHtmlContent.simple,
        'test-site',
        mockGenAI
      )

      expect(result).toMatch(/^feat:/)
      expect(result).toContain('create website')
    })

    it('should work without AI client (fallback mode)', async () => {
      const result = await generateEnhancedCommitMessage(
        'change background color',
        true,
        mockHtmlContent.simple,
        mockHtmlContent.complex,
        'test-site',
        null
      )

      expect(result).toMatch(/^(style|update|refactor|feat|fix):/)
    })

    it('should clean and validate the final commit message', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: '"feat: add new feature with very long description that exceeds normal limits"' }]
          }
        }]
      })

      const result = await generateEnhancedCommitMessage(
        'add new feature',
        false,
        null,
        mockHtmlContent.simple,
        'test-site',
        mockGenAI
      )

      expect(result).not.toMatch(/^"/)
      expect(result.length).toBeLessThanOrEqual(72)
    })
  })

  describe('content analysis integration', () => {
    it('should handle style changes in edit mode', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'style: update color scheme' }]
          }
        }]
      })

      const oldContent = '<style>body { color: black; }</style>'
      const newContent = '<style>body { color: blue; }</style>'
      
      const result = await generateEnhancedCommitMessage(
        'change color to blue',
        true,
        oldContent,
        newContent,
        'test-site',
        mockGenAI
      )

      expect(result).toBe('style: update color scheme')
    })

    it('should handle structural changes in edit mode', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'feat: add new section' }]
          }
        }]
      })

      const oldContent = '<body><h1>Title</h1></body>'
      const newContent = '<body><h1>Title</h1><section>New section</section></body>'
      
      const result = await generateEnhancedCommitMessage(
        'add new section',
        true,
        oldContent,
        newContent,
        'test-site',
        mockGenAI
      )

      expect(result).toBe('feat: add new section')
    })

    it('should detect content additions', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        candidates: [{
          content: {
            parts: [{ text: 'feat: expand content with portfolio sections' }]
          }
        }]
      })

      const result = await generateEnhancedCommitMessage(
        'add portfolio sections',
        true,
        mockHtmlContent.simple,
        mockHtmlContent.complex,
        'test-site',
        mockGenAI
      )

      expect(result).toBe('feat: expand content with portfolio sections')
    })
  })
})