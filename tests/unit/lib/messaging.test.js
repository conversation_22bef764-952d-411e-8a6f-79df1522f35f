/**
 * Tests for messaging functionality with summarization
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { sendStatusMessage } from '@/messaging.ts';
import { createMockGenAI, resetAllMocks } from '@tests/helpers/mock-clients.js';

describe('Messaging with Summarization', () => {
  let mockGenAI;
  let mockTwilioClient;

  beforeEach(() => {
    mockGenAI = createMockGenAI();
    mockTwilioClient = {
      messages: {
        create: vi.fn().mockResolvedValue({ sid: 'test-sid' })
      }
    };
    resetAllMocks();
    console.log = vi.fn(); // Mock console.log
    console.error = vi.fn(); // Mock console.error
  });

  it('should send message without summarization for short descriptions', async () => {
    await sendStatusMessage(
      'whatsapp:+1234567890',
      mockTwilioClient,
      'intent_classified',
      {
        intent: 'new_site',
        description: 'Create a simple portfolio'
      },
      mockGenAI
    );

    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      from: `whatsapp:${process.env.TWILIO_PHONE_NUMBER}`,
      to: 'whatsapp:+1234567890',
      body: 'I\'ll create a new website for you: "Create a simple portfolio"'
    });

    // Should not call AI for short descriptions
    expect(mockGenAI.models.generateContent).not.toHaveBeenCalled();
  });

  it('should summarize long user descriptions', async () => {
    const longDescription = 'Create a comprehensive e-commerce website with user authentication, shopping cart functionality, payment processing, inventory management, order tracking, customer reviews, product recommendations, admin dashboard, analytics, and mobile responsive design with modern UI/UX principles';
    
    // Mock AI response
    mockGenAI.models.generateContent.mockResolvedValue({
      text: 'Create e-commerce site with cart, payments, and admin features'
    });

    await sendStatusMessage(
      'whatsapp:+1234567890',
      mockTwilioClient,
      'intent_classified',
      {
        intent: 'new_site',
        description: longDescription
      },
      mockGenAI
    );

    expect(mockGenAI.models.generateContent).toHaveBeenCalledWith({
      model: "gemini-2.0-flash",
      contents: expect.stringContaining('Summarize this user request'),
      config: {
        temperature: 0.3,
        topP: 0.8,
        maxOutputTokens: 50
      }
    });

    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      from: `whatsapp:${process.env.TWILIO_PHONE_NUMBER}`,
      to: 'whatsapp:+1234567890',
      body: 'I\'ll create a new website for you: "Create e-commerce site with cart, payments, and admin features"'
    });
  });

  it('should handle AI summarization errors gracefully', async () => {
    const longDescription = 'A very long description that should be summarized but AI fails'.repeat(10);
    
    // Mock AI failure
    mockGenAI.models.generateContent.mockRejectedValue(new Error('AI service unavailable'));

    await sendStatusMessage(
      'whatsapp:+1234567890',
      mockTwilioClient,
      'intent_classified',
      {
        intent: 'new_site',
        description: longDescription
      },
      mockGenAI
    );

    // Should fallback to truncated original
    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      from: `whatsapp:${process.env.TWILIO_PHONE_NUMBER}`,
      to: 'whatsapp:+1234567890',
      body: expect.stringContaining('...')
    });
  });

  it('should truncate messages that exceed Twilio character limit', async () => {
    const veryLongMessage = 'A'.repeat(2000); // Exceeds 1600 character limit
    
    await sendStatusMessage(
      'whatsapp:+1234567890',
      mockTwilioClient,
      'processing',
      {
        message: veryLongMessage
      },
      mockGenAI
    );

    const sentMessage = mockTwilioClient.messages.create.mock.calls[0][0].body;
    expect(sentMessage.length).toBeLessThanOrEqual(1600);
    expect(sentMessage).toMatch(/\.\.\.$/); // Should end with ...
  });

  it('should work without genAI client (backward compatibility)', async () => {
    await sendStatusMessage(
      'whatsapp:+1234567890',
      mockTwilioClient,
      'website_generated'
    );

    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      from: `whatsapp:${process.env.TWILIO_PHONE_NUMBER}`,
      to: 'whatsapp:+1234567890',
      body: 'Website generated! Setting up version control...'
    });
  });
});