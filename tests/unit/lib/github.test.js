/**
 * Unit tests for GitHub integration functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  createRepository,
  updateRepository,
  getRepositoryContent,
  validateRepositoryAccess,
  checkRepositoryStructure,
  getRepositoryMetadata,
  importRepositoryData,
  generateCommitMessage
} from '../../../lib/github.ts'
import { resetAllMocks } from '../../helpers/mock-clients.js'
import { mockGitHubData } from '../../fixtures/test-data.js'

// Mock fetch globally
global.fetch = vi.fn()

// Mock the AI commit generator
vi.mock('../../../lib/ai-commit-generator.ts', () => ({
  generateEnhancedCommitMessage: vi.fn()
}))

describe('GitHub Integration', () => {
  const mockGitHubToken = 'test-github-token'
  const mockProjectName = 'test-project'
  const mockDescription = 'A test project'
  const mockHtmlContent = '<html><body>Test</body></html>'

  beforeEach(() => {
    resetAllMocks()
    vi.clearAllMocks()
    
    // Set up environment variables
    process.env.GITHUB_TOKEN = mockGitHubToken
    delete process.env.GITHUB_USERNAME
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createRepository', () => {
    it('should create a new GitHub repository successfully', async () => {
      // Mock repository creation
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock file creation calls (index.html, README.md, .gitignore, vercel.json)
      fetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ commit: { sha: 'abc123' } })
      })

      const result = await createRepository(mockProjectName, mockDescription, mockHtmlContent)

      expect(result).toEqual({
        repoUrl: mockGitHubData.repository.html_url,
        repoName: mockGitHubData.repository.full_name,
        cloneUrl: mockGitHubData.repository.clone_url,
        defaultBranch: 'main'
      })

      expect(fetch).toHaveBeenCalledWith(
        'https://api.github.com/user/repos',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockGitHubToken}`
          })
        })
      )
    })

    it('should generate AI repository description when genAI is provided', async () => {
      const mockGenAI = {
        models: {
          generateContent: vi.fn().mockResolvedValue({
            candidates: [{
              content: {
                parts: [{ text: 'AI-generated repository description' }]
              }
            }]
          })
        }
      }

      // Mock repository creation
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock file creation calls
      fetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ commit: { sha: 'abc123' } })
      })

      const options = {
        genAI: mockGenAI,
        userMessage: 'Create a portfolio website'
      }

      await createRepository(mockProjectName, mockDescription, mockHtmlContent, options)

      expect(mockGenAI.models.generateContent).toHaveBeenCalled()
    })

    it('should handle repository creation errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: vi.fn().mockResolvedValue({
          message: 'Repository creation failed',
          errors: [{ field: 'name', message: 'already exists' }]
        })
      })

      await expect(createRepository(mockProjectName, mockDescription, mockHtmlContent))
        .rejects.toThrow('GitHub API error')
    })

    it('should handle repository name conflicts by adding timestamp', async () => {
      // First call fails with name conflict
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: vi.fn().mockResolvedValue({
          message: 'name already exists',
          errors: [{ field: 'name', message: 'already exists' }]
        })
      })

      // Second call succeeds with timestamped name
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          ...mockGitHubData.repository,
          name: `${mockProjectName}-123456`
        })
      })

      // Mock file creation calls
      fetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ commit: { sha: 'abc123' } })
      })

      const result = await createRepository(mockProjectName, mockDescription, mockHtmlContent)

      expect(result.repoName).toContain('testuser/test-repo')
      expect(fetch).toHaveBeenCalledTimes(6) // 2 repo creation + 4 file creation
    })

    it('should truncate long descriptions', async () => {
      const longDescription = 'A'.repeat(400) // 400 characters

      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      fetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ commit: { sha: 'abc123' } })
      })

      await createRepository(mockProjectName, longDescription, mockHtmlContent)

      const createRepoCall = fetch.mock.calls[0]
      const requestBody = JSON.parse(createRepoCall[1].body)
      
      expect(requestBody.description.length).toBeLessThanOrEqual(350)
      expect(requestBody.description).toContain('...')
    })

    it('should require GITHUB_TOKEN', async () => {
      delete process.env.GITHUB_TOKEN

      await expect(createRepository(mockProjectName, mockDescription, mockHtmlContent))
        .rejects.toThrow('GITHUB_TOKEN environment variable is required')
    })
  })

  describe('updateRepository', () => {
    it('should update repository content successfully', async () => {
      const repoName = 'user/test-repo'
      const commitMessage = 'Update website content'

      // Mock getting current file
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'current-sha-123',
          content: Buffer.from('<html><body>Old</body></html>').toString('base64')
        })
      })

      // Mock updating file
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          commit: {
            sha: 'new-sha-456',
            html_url: 'https://github.com/user/test-repo/commit/new-sha-456'
          }
        })
      })

      const result = await updateRepository(repoName, mockHtmlContent, commitMessage)

      expect(result).toEqual({
        commitSha: 'new-sha-456',
        commitUrl: 'https://github.com/user/test-repo/commit/new-sha-456'
      })

      expect(fetch).toHaveBeenCalledTimes(2)
    })

    it('should generate AI commit message when options provided', async () => {
      const { generateEnhancedCommitMessage } = await import('../../../lib/ai-commit-generator.ts')
      generateEnhancedCommitMessage.mockResolvedValue('AI-generated commit message')

      const repoName = 'user/test-repo'
      const commitMessage = 'Basic commit message'
      const options = {
        genAI: { models: {} },
        userMessage: 'Change the background color',
        projectName: 'test-project',
        oldContent: '<html><body>Old</body></html>'
      }

      // Mock getting current file
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'current-sha-123',
          content: Buffer.from(options.oldContent).toString('base64')
        })
      })

      // Mock updating file
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          commit: {
            sha: 'new-sha-456',
            html_url: 'https://github.com/user/test-repo/commit/new-sha-456'
          }
        })
      })

      await updateRepository(repoName, mockHtmlContent, commitMessage, options)

      expect(generateEnhancedCommitMessage).toHaveBeenCalledWith(
        options.userMessage,
        true, // isEdit
        options.oldContent,
        mockHtmlContent,
        options.projectName,
        options.genAI
      )
    })

    it('should handle file update errors', async () => {
      const repoName = 'user/test-repo'

      // Mock getting current file
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'current-sha-123',
          content: Buffer.from('<html></html>').toString('base64')
        })
      })

      // Mock failed file update
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: vi.fn().mockResolvedValue({
          message: 'Conflict'
        })
      })

      await expect(updateRepository(repoName, mockHtmlContent, 'test commit'))
        .rejects.toThrow('Failed to update file')
    })

    it('should handle missing file errors', async () => {
      const repoName = 'user/test-repo'

      // Mock file not found
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      await expect(updateRepository(repoName, mockHtmlContent, 'test commit'))
        .rejects.toThrow('Failed to get file')
    })
  })

  describe('getRepositoryContent', () => {
    it('should get repository file content', async () => {
      const repoName = 'user/test-repo'
      const expectedContent = '<html><body>Content</body></html>'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          content: Buffer.from(expectedContent).toString('base64'),
          sha: 'file-sha-123'
        })
      })

      const result = await getRepositoryContent(repoName)

      expect(result).toBe(expectedContent)
      expect(fetch).toHaveBeenCalledWith(
        `https://api.github.com/repos/${repoName}/contents/index.html`,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockGitHubToken}`
          })
        })
      )
    })

    it('should get content from custom file path', async () => {
      const repoName = 'user/test-repo'
      const filePath = 'custom.html'
      const expectedContent = '<html><body>Custom</body></html>'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          content: Buffer.from(expectedContent).toString('base64'),
          sha: 'file-sha-123'
        })
      })

      const result = await getRepositoryContent(repoName, filePath)

      expect(result).toBe(expectedContent)
      expect(fetch).toHaveBeenCalledWith(
        `https://api.github.com/repos/${repoName}/contents/${filePath}`,
        expect.any(Object)
      )
    })

    it('should handle file not found errors', async () => {
      const repoName = 'user/test-repo'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      await expect(getRepositoryContent(repoName))
        .rejects.toThrow('Failed to get file')
    })
  })

  describe('validateRepositoryAccess', () => {
    it('should validate accessible repository', async () => {
      const repoName = 'user/test-repo'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      const result = await validateRepositoryAccess(repoName)

      expect(result.isValid).toBe(true)
      expect(result.repoData).toEqual({
        fullName: mockGitHubData.repository.full_name,
        description: mockGitHubData.repository.description,
        htmlUrl: mockGitHubData.repository.html_url,
        cloneUrl: mockGitHubData.repository.clone_url,
        defaultBranch: 'main',
        createdAt: mockGitHubData.repository.created_at,
        updatedAt: mockGitHubData.repository.updated_at
      })
    })

    it('should handle repository not found', async () => {
      const repoName = 'user/nonexistent-repo'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      const result = await validateRepositoryAccess(repoName)

      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Repository not found')
      expect(result.message).toContain('does not exist or is not accessible')
    })

    it('should handle access denied', async () => {
      const repoName = 'private/repo'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: vi.fn().mockResolvedValue({
          message: 'Forbidden'
        })
      })

      const result = await validateRepositoryAccess(repoName)

      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Access denied')
      expect(result.message).toContain('Access denied')
    })

    it('should handle network errors', async () => {
      const repoName = 'user/test-repo'

      fetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await validateRepositoryAccess(repoName)

      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Network error')
    })
  })

  describe('checkRepositoryStructure', () => {
    it('should validate repository with index.html', async () => {
      const repoName = 'user/test-repo'

      // Mock index.html exists
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'index-sha-123',
          size: 1024
        })
      })

      // Mock optional files (README.md exists, vercel.json doesn't)
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'readme-sha-456'
        })
      })

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      const result = await checkRepositoryStructure(repoName)

      expect(result.isValid).toBe(true)
      expect(result.structure).toEqual({
        hasIndexHtml: true,
        indexHtmlSha: 'index-sha-123',
        optionalFiles: ['README.md']
      })
    })

    it('should fail validation when index.html is missing', async () => {
      const repoName = 'user/test-repo'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      const result = await checkRepositoryStructure(repoName)

      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Missing index.html')
      expect(result.message).toContain('does not contain an index.html file')
    })

    it('should handle API errors gracefully', async () => {
      const repoName = 'user/test-repo'

      fetch.mockRejectedValueOnce(new Error('API error'))

      const result = await checkRepositoryStructure(repoName)

      expect(result.isValid).toBe(false)
      expect(result.error).toBe('Structure check failed')
    })
  })

  describe('getRepositoryMetadata', () => {
    it('should get repository metadata with latest commit', async () => {
      const repoName = 'user/test-repo'

      // Mock repository info
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock commits info
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue([
          { sha: 'latest-commit-sha' }
        ])
      })

      const result = await getRepositoryMetadata(repoName)

      expect(result).toEqual({
        fullName: mockGitHubData.repository.full_name,
        name: mockGitHubData.repository.name,
        description: mockGitHubData.repository.description,
        htmlUrl: mockGitHubData.repository.html_url,
        cloneUrl: mockGitHubData.repository.clone_url,
        defaultBranch: 'main',
        createdAt: mockGitHubData.repository.created_at,
        updatedAt: mockGitHubData.repository.updated_at,
        latestCommitSha: 'latest-commit-sha'
      })
    })

    it('should handle repository without commits', async () => {
      const repoName = 'user/empty-repo'

      // Mock repository info
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock empty commits
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue([])
      })

      const result = await getRepositoryMetadata(repoName)

      expect(result.latestCommitSha).toBeNull()
    })

    it('should handle commits API errors gracefully', async () => {
      const repoName = 'user/test-repo'

      // Mock repository info
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock commits API error
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 403
      })

      const result = await getRepositoryMetadata(repoName)

      expect(result.latestCommitSha).toBeNull()
    })
  })

  describe('importRepositoryData', () => {
    it('should import repository data successfully', async () => {
      const repoName = 'user/test-repo'

      // Mock validation calls
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      // Mock structure check
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          sha: 'index-sha-123',
          size: 1024
        })
      })

      // Mock optional files check (README.md)
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      // Mock optional files check (vercel.json)
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({
          message: 'Not Found'
        })
      })

      // Mock metadata calls
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue(mockGitHubData.repository)
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue([
          { sha: 'latest-commit-sha' }
        ])
      })

      // Mock content retrieval
      const htmlContent = '<html><body>Imported</body></html>'
      fetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          content: Buffer.from(htmlContent).toString('base64'),
          sha: 'content-sha-123'
        })
      })

      const result = await importRepositoryData(repoName)

      expect(result).toEqual({
        projectName: mockGitHubData.repository.name.toLowerCase(),
        description: mockGitHubData.repository.description,
        htmlContent,
        githubRepoUrl: mockGitHubData.repository.html_url,
        githubRepoName: mockGitHubData.repository.full_name,
        lastCommitSha: 'latest-commit-sha',
        url: null
      })
    })

    it('should handle import validation failures', async () => {
      const repoName = 'user/invalid-repo'

      // Mock validation failure
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      await expect(importRepositoryData(repoName))
        .rejects.toThrow()
    })
  })

  describe('generateCommitMessage', () => {
    it('should generate commit message for new creation', () => {
      const userMessage = 'Create a portfolio website'
      const result = generateCommitMessage(userMessage, false)

      expect(result).toBe('feat: Initial website creation - Create a portfolio website')
    })

    it('should generate commit message for edits', () => {
      const userMessage = 'Change the background color to blue'
      const result = generateCommitMessage(userMessage, true)

      expect(result).toBe('style: Change the background color to blue')
    })

    it('should categorize edit types correctly', () => {
      const testCases = [
        { message: 'Add a new section', expected: 'feature:' },
        { message: 'Remove the footer', expected: 'content:' },
        { message: 'Fix the navigation', expected: 'bugfix:' },
        { message: 'Change the header layout', expected: 'update:' },
        { message: 'Update the content', expected: 'update:' },
        { message: 'Change the background color', expected: 'style:' },
        { message: 'Modify the font size', expected: 'style:' }
      ]

      testCases.forEach(({ message, expected }) => {
        const result = generateCommitMessage(message, true)
        expect(result).toContain(expected)
      })
    })

    it('should truncate long messages', () => {
      const longMessage = 'A'.repeat(100)
      const result = generateCommitMessage(longMessage, true)

      expect(result.length).toBeLessThanOrEqual(71) // category + truncated message + colon + space
      expect(result).toContain('...')
    })

    it('should truncate long initial creation messages', () => {
      const longMessage = 'A'.repeat(100)
      const result = generateCommitMessage(longMessage, false)

      expect(result).toContain('feat: Initial website creation -')
      expect(result).toContain('...')
    })
  })

  describe('error handling', () => {
    it('should handle authentication errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: vi.fn().mockResolvedValue({
          message: 'Bad credentials'
        })
      })

      await expect(createRepository(mockProjectName, mockDescription, mockHtmlContent))
        .rejects.toThrow('GitHub API error: 401')
    })

    it('should handle rate limiting', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: vi.fn().mockResolvedValue({
          message: 'API rate limit exceeded'
        })
      })

      await expect(createRepository(mockProjectName, mockDescription, mockHtmlContent))
        .rejects.toThrow('GitHub API error: 429')
    })
  })
})