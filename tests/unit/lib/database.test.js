/**
 * Unit tests for database functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createMockSupabaseClient, resetAllMocks } from '../../helpers/mock-clients.js'
import { mockSupabaseData } from '../../fixtures/test-data.js'

// Create mock Supabase client at module level
const mockSupabase = createMockSupabaseClient()

// Mock Supabase at the top level
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(() => mockSupabase)
}))

describe('Database', () => {
  let mockChain
  let databaseModule

  beforeEach(async () => {
    resetAllMocks()
    
    // Set up environment variables that the database module needs
    process.env.SUPABASE_URL = 'https://test.supabase.co'
    process.env.SUPABASE_ANON_KEY = 'test-key'
    
    // Create a single mock chain that will be returned by all from() calls
    mockChain = {
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      neq: vi.fn().mockReturnThis(),
      gt: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lt: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      like: vi.fn().mockReturnThis(),
      ilike: vi.fn().mockReturnThis(),
      is: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      contains: vi.fn().mockReturnThis(),
      containedBy: vi.fn().mockReturnThis(),
      rangeGt: vi.fn().mockReturnThis(),
      rangeGte: vi.fn().mockReturnThis(),
      rangeLt: vi.fn().mockReturnThis(),
      rangeLte: vi.fn().mockReturnThis(),
      rangeAdjacent: vi.fn().mockReturnThis(),
      overlaps: vi.fn().mockReturnThis(),
      textSearch: vi.fn().mockReturnThis(),
      match: vi.fn().mockReturnThis(),
      not: vi.fn().mockReturnThis(),
      or: vi.fn().mockReturnThis(),
      filter: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      returns: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
      csv: vi.fn().mockResolvedValue({ data: '', error: null }),
      geojson: vi.fn().mockResolvedValue({ data: {}, error: null }),
      explain: vi.fn().mockResolvedValue({ data: '', error: null }),
      rollback: vi.fn().mockResolvedValue({ data: null, error: null }),
      then: vi.fn().mockResolvedValue({ data: [], error: null })
    }
    
    // Make mockSupabase.from() always return the same chain
    mockSupabase.from.mockReturnValue(mockChain)
    
    // Import database module fresh each time
    databaseModule = await import('../../../lib/database.ts')
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('getLastProject', () => {
    it('should get the last project for a user', async () => {
      // Set up the mock chain to return test data
      mockChain.single.mockResolvedValue({ 
        data: mockSupabaseData.userProject, 
        error: null 
      })

      const result = await databaseModule.getLastProject('whatsapp:+1234567890')

      expect(result).toEqual(mockSupabaseData.userProject)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
      expect(mockChain.select).toHaveBeenCalledWith('*')
      expect(mockChain.eq).toHaveBeenCalledWith('phone_number', '+1234567890')
      expect(mockChain.order).toHaveBeenCalledWith('created_at', { ascending: false })
      expect(mockChain.limit).toHaveBeenCalledWith(1)
    })

    it('should return null when no projects exist', async () => {
      // Mock the PGRST116 error (no rows returned)
      mockChain.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'PGRST116' } 
      })

      const result = await databaseModule.getLastProject('whatsapp:+1234567890')

      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      mockChain.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'SOME_ERROR', message: 'Database error' } 
      })

      const result = await databaseModule.getLastProject('whatsapp:+1234567890')

      expect(result).toBeNull()
    })

    it('should normalize phone numbers by removing whatsapp prefix', async () => {
      mockChain.single.mockResolvedValue({ 
        data: mockSupabaseData.userProject, 
        error: null 
      })

      await databaseModule.getLastProject('whatsapp:+1234567890')

      // Check that the normalized phone number was used
      expect(mockChain.eq).toHaveBeenCalledWith('phone_number', '+1234567890')
    })
  })

  describe('saveProject', () => {
    it('should save a new project successfully', async () => {
      const projectData = {
        projectName: 'Test Project',
        description: 'A test project',
        url: 'https://test.vercel.app',
        htmlContent: '<html><body>Test</body></html>',
        githubRepoUrl: 'https://github.com/user/test',
        githubRepoName: 'user/test',
        lastCommitSha: 'abc123'
      }

      // Create separate chains for update and insert operations
      const updateChain = {
        ...mockChain,
        eq: vi.fn().mockResolvedValue({ data: null, error: null })
      }
      
      const insertChain = {
        ...mockChain,
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: 123, ...projectData },
          error: null
        })
      }

      // Mock from() to return different chains for different calls
      let callCount = 0
      mockSupabase.from.mockImplementation(() => {
        callCount++
        return callCount === 1 ? updateChain : insertChain
      })

      const result = await databaseModule.saveProject('whatsapp:+1234567890', projectData)

      expect(result).toBeTruthy()
      expect(result.id).toBe(123)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
    })

    it('should handle save errors gracefully', async () => {
      const projectData = {
        projectName: 'Test Project',
        description: 'A test project',
        url: 'https://test.vercel.app',
        htmlContent: '<html><body>Test</body></html>'
      }

      // Create separate chains for update and insert operations
      const updateChain = {
        ...mockChain,
        eq: vi.fn().mockResolvedValue({ data: null, error: null })
      }
      
      const insertChain = {
        ...mockChain,
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Insert failed' }
        })
      }

      // Mock from() to return different chains for different calls
      let callCount = 0
      mockSupabase.from.mockImplementation(() => {
        callCount++
        return callCount === 1 ? updateChain : insertChain
      })

      const result = await databaseModule.saveProject('whatsapp:+1234567890', projectData)

      expect(result).toBeNull()
    })

    it('should mark previous projects as inactive', async () => {
      const projectData = {
        projectName: 'Test Project',
        description: 'A test project',
        url: 'https://test.vercel.app',
        htmlContent: '<html><body>Test</body></html>'
      }

      // Create separate chains for update and insert operations
      const updateChain = {
        ...mockChain,
        eq: vi.fn().mockResolvedValue({ data: null, error: null })
      }
      
      const insertChain = {
        ...mockChain,
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: 123 },
          error: null
        })
      }

      // Mock from() to return different chains for different calls
      let callCount = 0
      mockSupabase.from.mockImplementation(() => {
        callCount++
        return callCount === 1 ? updateChain : insertChain
      })

      await databaseModule.saveProject('whatsapp:+1234567890', projectData)

      expect(updateChain.eq).toHaveBeenCalledWith('phone_number', '+1234567890')
    })
  })

  describe('updateProject', () => {
    it('should update an existing project successfully', async () => {
      const updateData = {
        htmlContent: '<html><body>Updated</body></html>',
        url: 'https://updated.vercel.app'
      }

      mockChain.single.mockResolvedValue({ 
        data: { id: 123, ...updateData }, 
        error: null 
      })

      const result = await databaseModule.updateProject('whatsapp:+1234567890', updateData)

      expect(result).toBeTruthy()
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
      expect(mockChain.eq).toHaveBeenCalledWith('phone_number', '+1234567890')
      expect(mockChain.eq).toHaveBeenCalledWith('is_active', true)
    })

    it('should handle update errors gracefully', async () => {
      const updateData = {
        htmlContent: '<html><body>Updated</body></html>',
        url: 'https://updated.vercel.app'
      }

      mockChain.single.mockResolvedValue({ 
        data: null, 
        error: { message: 'Update failed' } 
      })

      const result = await databaseModule.updateProject('whatsapp:+1234567890', updateData)

      expect(result).toBeNull()
    })

    it('should include GitHub fields when provided', async () => {
      const updateData = {
        htmlContent: '<html><body>Updated</body></html>',
        url: 'https://updated.vercel.app',
        githubRepoUrl: 'https://github.com/user/updated',
        githubRepoName: 'user/updated',
        lastCommitSha: 'def456'
      }

      mockChain.single.mockResolvedValue({ 
        data: { id: 123, ...updateData }, 
        error: null 
      })

      await databaseModule.updateProject('whatsapp:+1234567890', updateData)

      // Verify that the update was called with GitHub fields
      expect(mockChain.update).toHaveBeenCalled()
      const updateCall = mockChain.update.mock.calls[0][0]
      expect(updateCall).toHaveProperty('github_repo_url')
      expect(updateCall).toHaveProperty('github_repo_name')
      expect(updateCall).toHaveProperty('last_commit_sha')
    })
  })

  describe('getUserProjects', () => {
    it('should get user project history', async () => {
      const mockProjects = [
        {
          project_name: 'project1',
          description: 'First project',
          url: 'https://project1.vercel.app',
          created_at: '2023-01-01T00:00:00Z'
        },
        {
          project_name: 'project2',
          description: 'Second project',
          url: 'https://project2.vercel.app',
          created_at: '2023-01-02T00:00:00Z'
        }
      ]

      // Create a chain that resolves to the projects data
      const projectsChain = {
        ...mockChain,
        limit: vi.fn().mockResolvedValue({ data: mockProjects, error: null })
      }
      
      mockSupabase.from.mockReturnValue(projectsChain)

      const result = await databaseModule.getUserProjects('whatsapp:+1234567890')

      expect(result).toEqual(mockProjects)
      expect(result).toHaveLength(2)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
    })

    it('should handle empty project history', async () => {
      const emptyChain = {
        ...mockChain,
        limit: vi.fn().mockResolvedValue({ data: [], error: null })
      }
      
      mockSupabase.from.mockReturnValue(emptyChain)

      const result = await databaseModule.getUserProjects('whatsapp:+1234567890')

      expect(result).toEqual([])
      expect(result).toHaveLength(0)
    })

    it('should handle database errors gracefully', async () => {
      const errorChain = {
        ...mockChain,
        limit: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database error' }
        })
      }
      
      mockSupabase.from.mockReturnValue(errorChain)

      const result = await databaseModule.getUserProjects('whatsapp:+1234567890')

      expect(result).toEqual([])
    })

    it('should respect the limit parameter', async () => {
      const limitChain = {
        ...mockChain,
        limit: vi.fn().mockResolvedValue({ data: [], error: null })
      }
      
      mockSupabase.from.mockReturnValue(limitChain)

      await databaseModule.getUserProjects('whatsapp:+1234567890', 5)

      expect(limitChain.limit).toHaveBeenCalledWith(5)
    })
  })

  describe('findProjectByRepo', () => {
    it('should find existing project by repository name', async () => {
      mockChain.single.mockResolvedValue({ 
        data: mockSupabaseData.userProject, 
        error: null 
      })

      const result = await databaseModule.findProjectByRepo('whatsapp:+1234567890', 'user/test-repo')

      expect(result).toEqual(mockSupabaseData.userProject)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
      expect(mockChain.select).toHaveBeenCalledWith('*')
      expect(mockChain.eq).toHaveBeenCalledWith('phone_number', '+1234567890')
      expect(mockChain.eq).toHaveBeenCalledWith('github_repo_name', 'user/test-repo')
    })

    it('should return null when no project found', async () => {
      mockChain.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'PGRST116' } 
      })

      const result = await databaseModule.findProjectByRepo('whatsapp:+1234567890', 'user/nonexistent')

      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      mockChain.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'SOME_ERROR', message: 'Database error' } 
      })

      const result = await databaseModule.findProjectByRepo('whatsapp:+1234567890', 'user/test-repo')

      expect(result).toBeNull()
    })
  })

  describe('importProject', () => {
    it('should import a new project successfully', async () => {
      const importData = mockSupabaseData.importData

      // Create a comprehensive mock chain that handles all the chained calls
      const comprehensiveMockChain = {
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockSupabaseData.savedProject, error: null })
      }

      // Mock the Supabase client to always return the comprehensive chain
      mockSupabase.from.mockReturnValue(comprehensiveMockChain)

      // Create separate chains for different operations
      let callCount = 0
      mockSupabase.from.mockImplementation((table) => {
        callCount++
        if (callCount === 1) {
          // First call: findProjectByRepo - return null (no existing project)
          return {
            ...comprehensiveMockChain,
            single: vi.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
          }
        } else if (callCount === 2) {
          // Second call: mark previous projects inactive
          return {
            ...comprehensiveMockChain,
            eq: vi.fn().mockResolvedValue({ data: null, error: null })
          }
        } else {
          // Third call: insert new project
          return {
            ...comprehensiveMockChain,
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockSupabaseData.savedProject, error: null })
          }
        }
      })

      const result = await databaseModule.importProject('+1234567890', importData)

      expect(result).toEqual(mockSupabaseData.savedProject)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
    }, 15000)

    it('should update existing imported project', async () => {
      const importData = mockSupabaseData.importData

      // Create a comprehensive mock chain that handles all the chained calls
      const comprehensiveMockChain = {
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: { id: 123, updated: true }, error: null })
      }

      // Mock the Supabase client to always return the comprehensive chain
      mockSupabase.from.mockReturnValue(comprehensiveMockChain)

      // Create separate chains for different operations
      let callCount = 0
      mockSupabase.from.mockImplementation((table) => {
        callCount++
        if (callCount === 1) {
          // First call: findProjectByRepo - return existing project
          return {
            ...comprehensiveMockChain,
            single: vi.fn().mockResolvedValue({ data: mockSupabaseData.userProject, error: null })
          }
        } else {
          // Second call: updateProject - return updated result
          return {
            ...comprehensiveMockChain,
            single: vi.fn().mockResolvedValue({ data: { id: 123, updated: true }, error: null })
          }
        }
      })

      const result = await databaseModule.importProject('+1234567890', importData)

      expect(result).toEqual({ id: 123, updated: true })
      expect(mockSupabase.from).toHaveBeenCalledWith('user_projects')
    }, 15000)

    it('should handle import errors gracefully', async () => {
      const importData = mockSupabaseData.importData

      // Create a comprehensive mock chain that handles all the chained calls
      const comprehensiveMockChain = {
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: null, error: { message: 'Import failed' } })
      }

      // Mock the Supabase client to always return the comprehensive chain
      mockSupabase.from.mockReturnValue(comprehensiveMockChain)

      // Create separate chains for different operations
      let callCount = 0
      mockSupabase.from.mockImplementation((table) => {
        callCount++
        if (callCount === 1) {
          // First call: findProjectByRepo - return null (no existing project)
          return {
            ...comprehensiveMockChain,
            single: vi.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
          }
        } else if (callCount === 2) {
          // Second call: mark previous projects inactive
          return {
            ...comprehensiveMockChain,
            eq: vi.fn().mockResolvedValue({ data: null, error: null })
          }
        } else {
          // Third call: insert new project - this will fail
          return {
            ...comprehensiveMockChain,
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: null, error: { message: 'Import failed' } })
          }
        }
      })

      const result = await databaseModule.importProject('+1234567890', importData)

      expect(result).toBeNull()
    }, 15000)
  })

  describe('initializeTables', () => {
    it('should return true indicating tables can be initialized', async () => {
      const result = await databaseModule.initializeTables()

      expect(result).toBe(true)
    })

    it('should handle initialization errors gracefully', async () => {
      // Mock an error in the function
      const originalConsoleLog = console.log
      console.log = vi.fn().mockImplementation(() => {
        throw new Error('Console error')
      })

      const result = await databaseModule.initializeTables()

      expect(result).toBe(false)
      console.log = originalConsoleLog
    })
  })

  describe('phone number normalization', () => {
    it('should normalize phone numbers consistently across functions', async () => {
      const phoneWithPrefix = 'whatsapp:+1234567890'
      const normalizedPhone = '+1234567890'

      // Test getLastProject normalization
      const getLastProjectChain = {
        ...mockChain,
        single: vi.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
      }
      
      mockSupabase.from.mockReturnValue(getLastProjectChain)
      await databaseModule.getLastProject(phoneWithPrefix)
      expect(getLastProjectChain.eq).toHaveBeenCalledWith('phone_number', normalizedPhone)

      // Reset mocks for getUserProjects test
      vi.clearAllMocks()
      
      const getUserProjectsChain = {
        ...mockChain,
        limit: vi.fn().mockResolvedValue({ data: [], error: null })
      }
      
      mockSupabase.from.mockReturnValue(getUserProjectsChain)
      await databaseModule.getUserProjects(phoneWithPrefix)
      expect(getUserProjectsChain.eq).toHaveBeenCalledWith('phone_number', normalizedPhone)
    })
  })
})