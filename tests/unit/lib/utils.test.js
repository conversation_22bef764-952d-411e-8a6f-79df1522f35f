import { describe, it, expect, vi, beforeEach } from 'vitest'
import { 
  slugify, 
  generateProjectName, 
  createTimeoutPromise,
  generateTimeoutFallbackHTML,
  logMainProcessDiagnostics,
  validateWebsiteCode
} from '../../../lib/utils.ts'
import { mockGenAI, mockUserMessages, mockHtmlContent, mockEnvironmentVars } from '../../fixtures/test-data.js'

describe('Utils', () => {
  let consoleSpy

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.clearAllMocks()
  })

  describe('slugify', () => {
    it('should convert text to URL-friendly slug', () => {
      expect(slugify('Hello World')).toBe('hello-world')
      expect(slugify('My Awesome Website!')).toBe('my-awesome-website')
      expect(slugify('E-commerce Site!')).toBe('e-commerce-site')
      expect(slugify('<EMAIL>')).toBe('user-domain-com')
    })

    it('should handle special characters', () => {
      expect(slugify('Test & Development')).toBe('test-development')
      expect(slugify('Price: $99.99')).toBe('price-99-99')
      expect(slugify('100% Success!')).toBe('100-success')
    })

    it('should handle multiple spaces and dashes', () => {
      expect(slugify('Multiple   Spaces')).toBe('multiple-spaces')
      expect(slugify('Already-Has-Dashes')).toBe('already-has-dashes')
      expect(slugify('Mixed   Spaces--And-Dashes')).toBe('mixed-spaces-and-dashes')
    })

    it('should truncate long strings', () => {
      const longString = 'This is a very long string that should be truncated to fifty characters maximum'
      const result = slugify(longString)
      expect(result.length).toBeLessThanOrEqual(50)
      expect(result).toBe('this-is-a-very-long-string-that-should-be-truncate')
    })

    it('should handle empty strings', () => {
      expect(slugify('')).toBe('')
      expect(slugify('   ')).toBe('')
    })
  })

  describe('generateProjectName', () => {
    it('should generate project name using AI', async () => {
      // Mock the AI to return a specific response
      mockGenAI.models.generateContent.mockResolvedValue({
        text: 'portfolio-website'
      })

      const result = await generateProjectName(mockUserMessages.newSite.portfolio, mockGenAI)
      
      expect(result).toBe('portfolio-website')
      expect(mockGenAI.models.generateContent).toHaveBeenCalledOnce()
    })

    it('should fall back to timestamp when AI fails', async () => {
      // Mock the AI to throw an error
      mockGenAI.models.generateContent.mockRejectedValue(new Error('API Error'))

      const result = await generateProjectName('My Awesome Portfolio Site', mockGenAI)
      
      expect(result).toMatch(/^whatsite-\d+$/)
    })

    it('should handle AI responses that need cleaning', async () => {
      // Mock the AI to return a response that needs slugification
      mockGenAI.models.generateContent.mockResolvedValue({
        text: 'Portfolio Website!'
      })

      const result = await generateProjectName(mockUserMessages.newSite.portfolio, mockGenAI)
      
      expect(result).toBe('portfolio-website')
    })

    it('should work without AI client', async () => {
      const result = await generateProjectName('E-commerce Store', null)
      
      expect(result).toMatch(/^whatsite-\d+$/)
    })

    it('should handle empty or invalid prompts', async () => {
      const results = await Promise.all([
        generateProjectName('', mockGenAI),
        generateProjectName(null, mockGenAI),
        generateProjectName(undefined, mockGenAI)
      ])
      
      results.forEach(result => {
        expect(result).toMatch(/^whatsite-\d+$/) // Should generate fallback name
      })
    })

    it('should generate unique fallback names', async () => {
      const result1 = await generateProjectName('', null)
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1))
      const result2 = await generateProjectName('', null)
      
      expect(result1).not.toBe(result2)
      expect(result1).toMatch(/^whatsite-\d+$/)
      expect(result2).toMatch(/^whatsite-\d+$/)
    })
  })

  describe('createTimeoutPromise', () => {
    it('should create a promise that rejects after timeout', async () => {
      const timeoutPromise = createTimeoutPromise(100, 'Test timeout')
      
      await expect(timeoutPromise).rejects.toThrow('Test timeout after 0.1 seconds')
    })

    it('should use default message when none provided', async () => {
      const timeoutPromise = createTimeoutPromise(50)
      
      await expect(timeoutPromise).rejects.toThrow('Operation timed out after 0.05 seconds')
    })
  })

  describe('generateTimeoutFallbackHTML', () => {
    it('should generate fallback HTML with user prompt', () => {
      const html = generateTimeoutFallbackHTML(mockUserMessages.newSite.portfolio, 'portfolio-site')
      
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('portfolio-site')
      expect(html).toContain(mockUserMessages.newSite.portfolio)
      expect(html).toContain('taking longer than expected')
    })

    it('should include helpful messaging about delays', () => {
      const html = generateTimeoutFallbackHTML('test', 'test')
      
      expect(html.toLowerCase()).toContain('taking longer than expected')
      expect(html.toLowerCase()).toContain('check back soon')
    })

    it('should be valid HTML structure', () => {
      const html = generateTimeoutFallbackHTML('test', 'test-project')
      
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('<html lang="en">')
      expect(html).toContain('</html>')
      expect(html).toContain('<head>')
      expect(html).toContain('</head>')
      expect(html).toContain('<body>')
      expect(html).toContain('</body>')
    })
  })

  describe('logMainProcessDiagnostics', () => {
    it('should log environment variable status', () => {
      logMainProcessDiagnostics('test', 'test', mockEnvironmentVars.complete)
      
      expect(consoleSpy).toHaveBeenCalledWith('=== MAIN PROCESS DIAGNOSTICS ===')
      expect(consoleSpy).toHaveBeenCalledWith('Starting website generation for project: test')
      expect(consoleSpy).toHaveBeenCalledWith('User prompt: "test"')
      expect(consoleSpy).toHaveBeenCalledWith('Environment variables check:', mockEnvironmentVars.complete)
    })
  })

  describe('validateWebsiteCode', () => {
    it('should log validation for valid HTML', () => {
      validateWebsiteCode(mockHtmlContent.complex)
      
      expect(consoleSpy).toHaveBeenCalledWith(`Generated website code length: ${mockHtmlContent.complex.length} characters`)
    })

    it('should detect and log HTML structure issues', () => {
      validateWebsiteCode(mockHtmlContent.invalid)
      
      expect(consoleSpy).toHaveBeenCalledWith(`Generated website code length: ${mockHtmlContent.invalid.length} characters`)
      expect(consoleSpy).toHaveBeenCalledWith('WARNING: Generated website code is suspiciously short!')
    })

    it('should handle very long HTML content', () => {
      const longHtml = 'a'.repeat(80000)
      validateWebsiteCode(longHtml)
      
      expect(consoleSpy).toHaveBeenCalledWith('Generated website code length: 80000 characters')
    })
  })

  describe('error handling and edge cases', () => {
    it('should handle null and undefined inputs gracefully', async () => {
      // These should not throw errors
      expect(() => slugify('')).not.toThrow()
      expect(() => validateWebsiteCode('')).not.toThrow()
      expect(() => generateTimeoutFallbackHTML('', '')).not.toThrow()
      expect(() => logMainProcessDiagnostics('', '', {})).not.toThrow()
      
      // Async functions should handle null/undefined
      await expect(generateProjectName('', null)).resolves.toMatch(/^whatsite-\d+$/)
      await expect(createTimeoutPromise(1)).rejects.toThrow()
    })
  })
})