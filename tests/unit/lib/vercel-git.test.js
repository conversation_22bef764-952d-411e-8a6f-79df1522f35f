/**
 * Unit tests for Vercel Git integration functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  connectGitToVercel,
  triggerGitDeployment,
  getProjectDeployment,
  fallbackDirectDeployment,
  validateGitIntegrationConfig
} from '../../../lib/vercel-git.ts'
import { resetAllMocks } from '../../helpers/mock-clients.js'

// Mock fetch globally
global.fetch = vi.fn()

// Mock the deployment module
vi.mock('../../../lib/deployment.ts', () => ({
  deployToVercel: vi.fn()
}))

describe('Vercel Git Integration', () => {
  const mockVercelToken = 'test-vercel-token'
  const mockProjectName = 'test-project'
  const mockRepoName = 'user/test-repo'

  beforeEach(() => {
    resetAllMocks()
    vi.clearAllMocks()
    
    // Set up environment variables
    process.env.VERCEL_API_TOKEN = mockVercelToken
    process.env.GITHUB_TOKEN = 'test-github-token'
    delete process.env.VERCEL_TEAM_ID
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('connectGitToVercel', () => {
    it('should connect a new repository to Vercel successfully', async () => {
      // Mock getVercelProject (project doesn't exist)
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: () => Promise.resolve({ message: 'Not Found' })
      })

      // Mock createVercelProject
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'project-123',
          name: mockProjectName,
          accountId: 'account-456'
        })
      })

      // Mock triggerGitDeployment
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'test-project-abc123.vercel.app',
          readyState: 'READY'
        })
      })

      const result = await connectGitToVercel(mockRepoName, mockProjectName)

      expect(result).toEqual({
        projectId: 'project-123',
        projectName: mockProjectName,
        deploymentUrl: 'https://test-project-abc123.vercel.app',
        dashboardUrl: 'https://vercel.com/account-456/test-project',
        isExisting: false
      })

      expect(fetch).toHaveBeenCalledTimes(3)
    })

    it('should use existing Vercel project if already connected to same repo', async () => {
      const existingProject = {
        id: 'existing-123',
        name: mockProjectName,
        accountId: 'account-456',
        link: {
          repo: mockRepoName
        }
      }

      // Mock getVercelProject (project exists and is connected)
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(existingProject)
      })

      // Mock getLatestDeployment
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          deployments: [{
            url: 'existing-deployment.vercel.app'
          }]
        })
      })

      const result = await connectGitToVercel(mockRepoName, mockProjectName)

      expect(result).toEqual({
        projectId: 'existing-123',
        projectName: mockProjectName,
        deploymentUrl: 'https://existing-deployment.vercel.app',
        dashboardUrl: 'https://vercel.com/account-456/test-project',
        isExisting: true
      })

      expect(fetch).toHaveBeenCalledTimes(2)
    })

    it('should update existing project if connected to different repo', async () => {
      const existingProject = {
        id: 'existing-123',
        name: mockProjectName,
        accountId: 'account-456',
        link: {
          repo: 'different/repo'
        }
      }

      // Mock getVercelProject (project exists but different repo)
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(existingProject)
      })

      // Mock updateVercelProjectRepo
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(existingProject)
      })

      // Mock triggerGitDeployment
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'updated-deployment.vercel.app',
          readyState: 'READY'
        })
      })

      const result = await connectGitToVercel(mockRepoName, mockProjectName)

      expect(result).toEqual({
        projectId: 'existing-123',
        projectName: mockProjectName,
        deploymentUrl: 'https://updated-deployment.vercel.app',
        dashboardUrl: 'https://vercel.com/account-456/test-project',
        isExisting: true
      })

      expect(fetch).toHaveBeenCalledTimes(3)
    })

    it('should handle Vercel API errors', async () => {
      // Mock failed project creation
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: () => Promise.resolve({
          error: { message: 'Validation failed' }
        })
      })

      await expect(connectGitToVercel(mockRepoName, mockProjectName))
        .rejects.toThrow('Vercel project creation failed')
    })

    it('should include team ID in requests when provided', async () => {
      process.env.VERCEL_TEAM_ID = 'team-123'

      // Mock getVercelProject with team ID
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      // Mock createVercelProject with team ID
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'project-123',
          name: mockProjectName,
          accountId: 'account-456'
        })
      })

      // Mock triggerGitDeployment with team ID
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'test-project-abc123.vercel.app',
          readyState: 'READY'
        })
      })

      await connectGitToVercel(mockRepoName, mockProjectName)

      // Verify team ID was included in URL
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('teamId=team-123'),
        expect.any(Object)
      )
    })
  })

  describe('triggerGitDeployment', () => {
    it('should trigger a Git deployment successfully', async () => {
      const projectId = 'project-123'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'new-deployment.vercel.app',
          readyState: 'READY'
        })
      })

      const result = await triggerGitDeployment(projectId)

      expect(result).toEqual({
        deploymentId: 'deployment-789',
        url: 'new-deployment.vercel.app',
        state: 'READY'
      })

      expect(fetch).toHaveBeenCalledWith(
        'https://api.vercel.com/v13/deployments',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockVercelToken}`
          })
        })
      )
    })

    it('should handle deployment errors', async () => {
      const projectId = 'project-123'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: { message: 'Bad request' }
        })
      })

      await expect(triggerGitDeployment(projectId))
        .rejects.toThrow('Vercel deployment failed')
    })

    it('should require VERCEL_API_TOKEN', async () => {
      delete process.env.VERCEL_API_TOKEN

      await expect(triggerGitDeployment('project-123'))
        .rejects.toThrow('VERCEL_API_TOKEN environment variable is required')
    })
  })

  describe('getProjectDeployment', () => {
    it('should get project deployment information', async () => {
      const projectId = 'project-123'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          deployments: [{
            id: 'deployment-789',
            url: 'current-deployment.vercel.app',
            readyState: 'READY',
            createdAt: '2023-01-01T00:00:00Z'
          }]
        })
      })

      const result = await getProjectDeployment(projectId)

      expect(result).toEqual({
        deploymentId: 'deployment-789',
        url: 'https://current-deployment.vercel.app',
        state: 'READY',
        createdAt: '2023-01-01T00:00:00Z'
      })
    })

    it('should handle no deployments found', async () => {
      const projectId = 'project-123'

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          deployments: []
        })
      })

      await expect(getProjectDeployment(projectId))
        .rejects.toThrow('No deployments found for project')
    })

    it('should handle API errors', async () => {
      const projectId = 'project-123'

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: () => Promise.resolve({
          error: { message: 'Project not found' }
        })
      })

      await expect(getProjectDeployment(projectId))
        .rejects.toThrow('Failed to get deployments')
    })
  })

  describe('fallbackDirectDeployment', () => {
    it('should use direct deployment as fallback', async () => {
      const htmlContent = '<html><body>Test</body></html>'
      const projectName = 'fallback-project'

      const { deployToVercel } = await import('../../../lib/deployment.ts')
      deployToVercel.mockResolvedValue('https://fallback-deployment.vercel.app')

      const result = await fallbackDirectDeployment(htmlContent, projectName)

      expect(result).toBe('https://fallback-deployment.vercel.app')
      expect(deployToVercel).toHaveBeenCalledWith(
        htmlContent,
        mockVercelToken,
        projectName
      )
    })

    it('should handle fallback deployment errors', async () => {
      const htmlContent = '<html><body>Test</body></html>'
      const projectName = 'fallback-project'

      const { deployToVercel } = await import('../../../lib/deployment.ts')
      deployToVercel.mockRejectedValue(new Error('Deployment failed'))

      await expect(fallbackDirectDeployment(htmlContent, projectName))
        .rejects.toThrow('Deployment failed')
    })

    it('should require VERCEL_API_TOKEN for fallback', async () => {
      delete process.env.VERCEL_API_TOKEN

      await expect(fallbackDirectDeployment('<html></html>', 'test'))
        .rejects.toThrow('VERCEL_API_TOKEN environment variable is required')
    })
  })

  describe('validateGitIntegrationConfig', () => {
    it('should validate that all required environment variables are set', () => {
      process.env.VERCEL_API_TOKEN = 'test-token'
      process.env.GITHUB_TOKEN = 'test-github-token'

      const result = validateGitIntegrationConfig()

      expect(result).toEqual({
        isValid: true,
        missingVars: [],
        message: 'All required environment variables are set'
      })
    })

    it('should detect missing VERCEL_API_TOKEN', () => {
      delete process.env.VERCEL_API_TOKEN
      process.env.GITHUB_TOKEN = 'test-github-token'

      const result = validateGitIntegrationConfig()

      expect(result).toEqual({
        isValid: false,
        missingVars: ['VERCEL_API_TOKEN'],
        message: 'Missing required environment variables: VERCEL_API_TOKEN'
      })
    })

    it('should detect missing GITHUB_TOKEN', () => {
      process.env.VERCEL_API_TOKEN = 'test-token'
      delete process.env.GITHUB_TOKEN

      const result = validateGitIntegrationConfig()

      expect(result).toEqual({
        isValid: false,
        missingVars: ['GITHUB_TOKEN'],
        message: 'Missing required environment variables: GITHUB_TOKEN'
      })
    })

    it('should detect multiple missing environment variables', () => {
      delete process.env.VERCEL_API_TOKEN
      delete process.env.GITHUB_TOKEN

      const result = validateGitIntegrationConfig()

      expect(result).toEqual({
        isValid: false,
        missingVars: ['VERCEL_API_TOKEN', 'GITHUB_TOKEN'],
        message: 'Missing required environment variables: VERCEL_API_TOKEN, GITHUB_TOKEN'
      })
    })

    it('should treat empty strings as missing', () => {
      process.env.VERCEL_API_TOKEN = ''
      process.env.GITHUB_TOKEN = '   '

      const result = validateGitIntegrationConfig()

      expect(result).toEqual({
        isValid: false,
        missingVars: ['VERCEL_API_TOKEN', 'GITHUB_TOKEN'],
        message: 'Missing required environment variables: VERCEL_API_TOKEN, GITHUB_TOKEN'
      })
    })
  })

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      fetch.mockRejectedValue(new Error('Network error'))

      await expect(connectGitToVercel(mockRepoName, mockProjectName))
        .rejects.toThrow('Network error')
    })

    it('should handle malformed JSON responses', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.reject(new Error('Invalid JSON'))
      })

      await expect(connectGitToVercel(mockRepoName, mockProjectName))
        .rejects.toThrow()
    })
  })

  describe('team ID handling', () => {
    it('should work without team ID', async () => {
      delete process.env.VERCEL_TEAM_ID

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'project-123',
          name: mockProjectName,
          accountId: 'account-456'
        })
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'test-deployment.vercel.app',
          readyState: 'READY'
        })
      })

      const result = await connectGitToVercel(mockRepoName, mockProjectName)

      expect(result.projectId).toBe('project-123')
    })

    it('should handle empty team ID', async () => {
      process.env.VERCEL_TEAM_ID = ''

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'project-123',
          name: mockProjectName,
          accountId: 'account-456'
        })
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'deployment-789',
          url: 'test-deployment.vercel.app',
          readyState: 'READY'
        })
      })

      await connectGitToVercel(mockRepoName, mockProjectName)

      // Should not include teamId in URLs when empty
      const calls = fetch.mock.calls
      calls.forEach(call => {
        if (typeof call[0] === 'string') {
          expect(call[0]).not.toContain('teamId=')
        }
      })
    })
  })
})