/**
 * Unit tests for intent classifier
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  extractRepositoryInfo,
  classifyIntent,
  containsEditKeywords
} from '@/intent-classifier.ts'
import { createMockGenAI, resetAllMocks } from '@tests/helpers/mock-clients.js'
import { mockUserMessages, mockSupabaseData } from '@tests/fixtures/test-data.js'

describe('Intent Classifier', () => {
  let mockGenAI

  beforeEach(() => {
    mockGenAI = createMockGenAI()
    resetAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('extractRepositoryInfo', () => {
    it('should extract GitHub repository info from full URLs', () => {
      const testCases = [
        {
          input: 'https://github.com/user/repo',
          expected: { 
            type: 'url',
            fullUrl: 'https://github.com/user/repo',
            repoName: 'user/repo',
            owner: 'user', 
            repo: 'repo'
          }
        },
        {
          input: 'https://github.com/testuser/my-awesome-project',
          expected: { 
            type: 'url',
            fullUrl: 'https://github.com/testuser/my-awesome-project',
            repoName: 'testuser/my-awesome-project',
            owner: 'testuser', 
            repo: 'my-awesome-project'
          }
        },
        {
          input: 'https://github.com/org-name/project-name',
          expected: { 
            type: 'url',
            fullUrl: 'https://github.com/org-name/project-name',
            repoName: 'org-name/project-name',
            owner: 'org-name', 
            repo: 'project-name'
          }
        }
      ]

      testCases.forEach(({ input, expected }) => {
        const result = extractRepositoryInfo(input)
        expect(result).toEqual(expected)
      })
    })

    it('should extract GitHub repository info from short URLs', () => {
      const testCases = [
        {
          input: 'github.com/user/repo',
          expected: { 
            type: 'url',
            fullUrl: 'https://github.com/user/repo',
            repoName: 'user/repo',
            owner: 'user', 
            repo: 'repo'
          }
        },
        {
          input: 'Work on github.com/testuser/project',
          expected: { 
            type: 'url',
            fullUrl: 'https://github.com/testuser/project',
            repoName: 'testuser/project',
            owner: 'testuser', 
            repo: 'project'
          }
        }
      ]

      testCases.forEach(({ input, expected }) => {
        const result = extractRepositoryInfo(input)
        expect(result).toEqual(expected)
      })
    })

    it('should extract repository info from messages with additional text', () => {
      const result = extractRepositoryInfo('Please work on https://github.com/user/repo and add a contact form')
      expect(result).toEqual({
        type: 'url',
        fullUrl: 'https://github.com/user/repo',
        repoName: 'user/repo',
        owner: 'user',
        repo: 'repo'
      })
    })

    it('should extract repository info from repo name patterns', () => {
      const testCases = [
        {
          input: 'work on my-project repo',
          expected: {
            type: 'name',
            repoIdentifier: 'my-project',
            fullUrl: null,
            repoName: null
          }
        },
        {
          input: 'edit awesome-site repository',
          expected: {
            type: 'name',
            repoIdentifier: 'awesome-site',
            fullUrl: null,
            repoName: null
          }
        },
        {
          input: 'import portfolio repo',
          expected: {
            type: 'name',
            repoIdentifier: 'portfolio',
            fullUrl: null,
            repoName: null
          }
        },
        {
          input: 'continue working on blog-site',
          expected: {
            type: 'name',
            repoIdentifier: 'blog-site',
            fullUrl: null,
            repoName: null
          }
        }
      ]

      testCases.forEach(({ input, expected }) => {
        const result = extractRepositoryInfo(input)
        expect(result).toEqual(expected)
      })
    })

    it('should return null for messages without GitHub URLs or repo patterns', () => {
      const testCases = [
        'Create a new website',
        'Change the background color',
        'https://example.com/some-page',
        'gitlab.com/user/repo',
        'github.io/user/repo'
      ]

      testCases.forEach(input => {
        const result = extractRepositoryInfo(input)
        expect(result).toBeNull()
      })
    })

    it('should handle edge cases', () => {
      expect(extractRepositoryInfo('')).toBeNull()
      expect(extractRepositoryInfo('github.com')).toBeNull()
      expect(extractRepositoryInfo('https://github.com')).toBeNull()
      expect(extractRepositoryInfo('https://github.com/user')).toBeNull()
    })
  })

  describe('containsEditKeywords', () => {
    it('should detect edit keywords', () => {
      const editMessages = [
        'change the background color',
        'update the header',
        'modify the layout',
        'fix the broken link',
        'remove the footer',
        'add a contact form',
        'edit the content',
        'improve the design',
        'my site needs work',
        'the site looks bad',
        'current site is broken',
        'this site needs updates'
      ]

      editMessages.forEach(message => {
        expect(containsEditKeywords(message)).toBe(true)
      })
    })

    it('should not detect edit keywords in new site requests', () => {
      const newSiteMessages = [
        'create a portfolio website',
        'build an e-commerce site',
        'make a blog for travel',
        'generate a restaurant website',
        'develop a landing page'
      ]

      newSiteMessages.forEach(message => {
        expect(containsEditKeywords(message)).toBe(false)
      })
    })

    it('should handle case insensitive matching', () => {
      expect(containsEditKeywords('CHANGE the color')).toBe(true)
      expect(containsEditKeywords('Update The Header')).toBe(true)
      expect(containsEditKeywords('ADD a section')).toBe(true)
    })
  })

  describe('classifyIntent', () => {
    it('should classify repository import intent from URL', async () => {
      const message = 'Work on https://github.com/user/repo'
      const result = await classifyIntent(message, mockGenAI)

      expect(result.intent).toBe('import_repo')
      expect(result.repositoryInfo).toEqual({
        type: 'url',
        fullUrl: 'https://github.com/user/repo',
        repoName: 'user/repo',
        owner: 'user',
        repo: 'repo'
      })
      expect(result.confidence).toBe(0.95)
    })

    it('should classify repository import intent from repo name pattern', async () => {
      const message = 'work on my-project repo'
      const result = await classifyIntent(message, mockGenAI)

      expect(result.intent).toBe('import_repo')
      expect(result.repositoryInfo).toEqual({
        type: 'name',
        repoIdentifier: 'my-project',
        fullUrl: null,
        repoName: null
      })
      expect(result.confidence).toBe(0.95)
    })

    it('should use AI classification for non-repo messages', async () => {
      // Mock AI response
      mockGenAI.models.generateContent.mockResolvedValue({
        text: JSON.stringify({
          intent: 'edit_site',
          confidence: 0.8,
          reasoning: 'User wants to modify existing site'
        })
      })

      const message = 'change the background color'
      const lastProject = mockSupabaseData.userProject
      
      const result = await classifyIntent(message, mockGenAI, lastProject)

      expect(result.intent).toBe('edit_site')
      expect(result.confidence).toBe(0.8)
      expect(mockGenAI.models.generateContent).toHaveBeenCalled()
    })

    it('should override edit_site to new_site when no last project exists', async () => {
      // Mock AI response that suggests edit_site
      mockGenAI.models.generateContent.mockResolvedValue({
        text: JSON.stringify({
          intent: 'edit_site',
          confidence: 0.7,
          reasoning: 'User wants to edit'
        })
      })

      const message = 'change the background color'
      const result = await classifyIntent(message, mockGenAI, null)

      expect(result.intent).toBe('new_site')
      expect(result.reasoning).toBe('No previous project exists, treating as new site request')
    })

    it('should handle AI classification errors gracefully', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('AI Error'))

      const message = 'work on something'
      const lastProject = mockSupabaseData.userProject
      
      const result = await classifyIntent(message, mockGenAI, lastProject)

      // Should fall back to rule-based classification
      expect(result.intent).toBe('edit_site') // Because user has a project
      expect(result.confidence).toBe(0.5)
      expect(result.reasoning).toBe('Fallback classification due to error')
    })

    it('should fall back to new_site when no project and AI fails', async () => {
      mockGenAI.models.generateContent.mockRejectedValue(new Error('AI Error'))

      const message = 'create something'
      const result = await classifyIntent(message, mockGenAI, null)

      expect(result.intent).toBe('new_site')
      expect(result.confidence).toBe(0.5)
      expect(result.reasoning).toBe('Fallback classification due to error')
    })

    it('should handle malformed AI responses', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        text: 'invalid json response'
      })

      const message = 'ambiguous message'
      const result = await classifyIntent(message, mockGenAI, null)

      // Should fall back to rule-based classification
      expect(result.intent).toBe('new_site')
      expect(result.confidence).toBe(0.5)
      expect(result.reasoning).toBe('Fallback classification due to error')
    })

    it('should clean up markdown code blocks from AI response', async () => {
      mockGenAI.models.generateContent.mockResolvedValue({
        text: '```json\n{"intent": "new_site", "confidence": 0.9, "reasoning": "User wants new site"}\n```'
      })

      const message = 'create a website'
      const result = await classifyIntent(message, mockGenAI, null)

      expect(result.intent).toBe('new_site')
      expect(result.confidence).toBe(0.9)
    })

    it('should work without AI client', async () => {
      const message = 'create a new website'
      const result = await classifyIntent(message, null, null)

      expect(result.intent).toBe('new_site')
      expect(result.confidence).toBe(0.5)
      expect(result.reasoning).toBe('Fallback: Default classification')
    })
  })

  describe('edge cases and error handling', () => {
    it('should handle empty or null messages', async () => {
      const emptyResults = await Promise.all([
        classifyIntent('', mockGenAI),
        classifyIntent(null, mockGenAI),
        classifyIntent(undefined, mockGenAI)
      ])

      emptyResults.forEach(result => {
        expect(['new_site', 'edit_site']).toContain(result.intent)
        expect(result).toHaveProperty('confidence')
      })
    })

    it('should handle very long messages', async () => {
      // Mock AI response for long message
      mockGenAI.models.generateContent.mockResolvedValue({
        text: JSON.stringify({
          intent: 'new_site',
          confidence: 0.8,
          reasoning: 'User wants to create website'
        })
      })

      const longMessage = 'create a website ' + 'with lots of content '.repeat(100)
      const result = await classifyIntent(longMessage, mockGenAI)

      expect(result.intent).toBe('new_site')
      expect(result).toHaveProperty('confidence')
    })

    it('should handle special characters in messages', async () => {
      // Mock AI response for special characters
      mockGenAI.models.generateContent.mockResolvedValue({
        text: JSON.stringify({
          intent: 'new_site',
          confidence: 0.8,
          reasoning: 'User wants to create website'
        })
      })

      const specialMessage = 'créate a websité with émojis 🚀 and spëcial chars!'
      const result = await classifyIntent(specialMessage, mockGenAI)

      expect(result.intent).toBe('new_site')
      expect(result).toHaveProperty('confidence')
    })
  })
})