/**
 * Unit tests for WhatsApp API handler
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import handler from '../../../api/whatsapp.ts'
import { createMockTwilioClient, createMockOpenAI, createMockGenAI, createMockSupabaseClient, resetAllMocks } from '../../helpers/mock-clients.js'
import { mockSupabaseData, mockWhatsAppData } from '../../fixtures/test-data.js'

// Mock all the dependencies
vi.mock('../../../lib/config.ts', () => ({
  initializeClients: vi.fn(),
  validateEnvironment: vi.fn()
}))

vi.mock('../../../lib/transcription.ts', () => ({
  transcribeAudio: vi.fn()
}))

vi.mock('../../../lib/website-generator.ts', () => ({
  generateWebsite: vi.fn()
}))

vi.mock('../../../lib/deployment.ts', () => ({
  deployToVercel: vi.fn()
}))

vi.mock('../../../lib/messaging.ts', () => ({
  sendWhatsappMessage: vi.fn(),
  sendStatusMessage: vi.fn()
}))

vi.mock('../../../lib/intent-classifier.ts', () => ({
  classifyIntent: vi.fn()
}))

vi.mock('../../../lib/database.ts', () => ({
  getLastProject: vi.fn(),
  saveProject: vi.fn(),
  updateProject: vi.fn(),
  findProjectByRepo: vi.fn(),
  importProject: vi.fn()
}))

vi.mock('../../../lib/utils.ts', () => ({
  generateProjectName: vi.fn(),
  createTimeoutPromise: vi.fn(),
  generateTimeoutFallbackHTML: vi.fn(),
  logMainProcessDiagnostics: vi.fn(),
  validateWebsiteCode: vi.fn()
}))

vi.mock('../../../lib/github.ts', () => ({
  createRepository: vi.fn(),
  updateRepository: vi.fn(),
  getRepositoryContent: vi.fn(),
  generateCommitMessage: vi.fn(),
  validateRepositoryAccess: vi.fn(),
  checkRepositoryStructure: vi.fn(),
  getRepositoryMetadata: vi.fn(),
  importRepositoryData: vi.fn()
}))

vi.mock('../../../lib/vercel-git.ts', () => ({
  connectGitToVercel: vi.fn(),
  fallbackDirectDeployment: vi.fn(),
  validateGitIntegrationConfig: vi.fn()
}))

vi.mock('formidable', () => ({
  default: vi.fn(() => ({
    parse: vi.fn()
  }))
}))

describe('WhatsApp API Handler', () => {
  let mockReq, mockRes
  let mockTwilio, mockOpenAI, mockGenAI, mockSupabase

  beforeEach(async () => {
    resetAllMocks()
    vi.clearAllMocks()

    // Create mock clients
    mockTwilio = createMockTwilioClient()
    mockOpenAI = createMockOpenAI()
    mockGenAI = createMockGenAI()
    mockSupabase = createMockSupabaseClient()

    // Mock request and response objects
    mockReq = {
      method: 'POST',
      body: {}
    }

    mockRes = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis()
    }

    // Set up default mocks
    const { validateEnvironment, initializeClients } = await import('../../../lib/config.ts')
    validateEnvironment.mockReturnValue({
      isValid: true,
      missing: [],
      envVars: {}
    })
    initializeClients.mockReturnValue({
      twilioClient: mockTwilio,
      openai: mockOpenAI,
      genAI: mockGenAI,
      vercelToken: 'test-vercel-token'
    })

    // Mock formidable
    const formidable = (await import('formidable')).default
    formidable.mockReturnValue({
      parse: vi.fn().mockResolvedValue([
        mockWhatsAppData.textMessage.fields,
        {}
      ])
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('POST requests', () => {
    it('should handle text message for new website creation', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject, saveProject } = await import('../../../lib/database.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { generateProjectName, createTimeoutPromise } = await import('../../../lib/utils.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      // Mock intent classification for new site
      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create a portfolio website'
      })

      getLastProject.mockResolvedValue(null)
      generateProjectName.mockResolvedValue('portfolio-website')
      createTimeoutPromise.mockReturnValue(new Promise(() => {})) // Never resolves
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>Portfolio</body></html>')
      deployToVercel.mockResolvedValue('https://portfolio-website.vercel.app')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(200)
      expect(mockRes.send).toHaveBeenCalledWith('Success')
      expect(classifyIntent).toHaveBeenCalled()
      expect(generateWebsite).toHaveBeenCalled()
      expect(deployToVercel).toHaveBeenCalled()
      expect(saveProject).toHaveBeenCalled()
    })

    it('should handle text message for editing existing website', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject, updateProject } = await import('../../../lib/database.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { createTimeoutPromise } = await import('../../../lib/utils.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      // Mock intent classification for edit
      classifyIntent.mockResolvedValue({
        intent: 'edit_site',
        description: 'Change the background color'
      })

      getLastProject.mockResolvedValue(mockSupabaseData.userProject)
      createTimeoutPromise.mockReturnValue(new Promise(() => {})) // Never resolves
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>Updated</body></html>')
      deployToVercel.mockResolvedValue('https://updated-website.vercel.app')
      updateProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(200)
      expect(mockRes.send).toHaveBeenCalledWith('Success')
      expect(getLastProject).toHaveBeenCalled()
      expect(generateWebsite).toHaveBeenCalled()
      expect(updateProject).toHaveBeenCalled()
    })

    it('should handle audio message transcription', async () => {
      const formidable = (await import('formidable')).default
      const { transcribeAudio } = await import('../../../lib/transcription.ts')
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { generateProjectName } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { saveProject } = await import('../../../lib/database.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      // Mock audio message
      formidable.mockReturnValue({
        parse: vi.fn().mockResolvedValue([
          mockWhatsAppData.audioMessage.fields,
          {}
        ])
      })

      transcribeAudio.mockResolvedValue('Create a business website')
      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create a business website'
      })
      generateProjectName.mockResolvedValue('business-website')
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>Business</body></html>')
      deployToVercel.mockResolvedValue('https://business-website.vercel.app')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(transcribeAudio).toHaveBeenCalledWith(
        mockWhatsAppData.audioMessage.fields.MediaUrl0[0],
        mockOpenAI
      )
      expect(mockRes.status).toHaveBeenCalledWith(200)
    })

    it('should handle repository import workflow', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { findProjectByRepo, importProject } = await import('../../../lib/database.ts')
      const { validateRepositoryAccess, checkRepositoryStructure, importRepositoryData } = await import('../../../lib/github.ts')
      const { validateGitIntegrationConfig, connectGitToVercel } = await import('../../../lib/vercel-git.ts')

      // Mock intent classification for repo import
      classifyIntent.mockResolvedValue({
        intent: 'import_repo',
        repositoryInfo: {
          owner: 'user',
          repo: 'test-repo',
          fullUrl: 'https://github.com/user/test-repo'
        }
      })

      findProjectByRepo.mockResolvedValue(null) // No existing project
      validateRepositoryAccess.mockResolvedValue({ isValid: true })
      checkRepositoryStructure.mockResolvedValue({ isValid: true })
      importRepositoryData.mockResolvedValue({
        projectName: 'test-repo',
        description: 'Imported repository',
        htmlContent: '<html><body>Imported</body></html>',
        githubRepoUrl: 'https://github.com/user/test-repo',
        githubRepoName: 'user/test-repo',
        lastCommitSha: 'abc123'
      })
      validateGitIntegrationConfig.mockReturnValue({ isValid: true })
      connectGitToVercel.mockResolvedValue({
        deploymentUrl: 'https://test-repo.vercel.app'
      })
      importProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(validateRepositoryAccess).toHaveBeenCalled()
      expect(checkRepositoryStructure).toHaveBeenCalled()
      expect(importRepositoryData).toHaveBeenCalled()
      expect(connectGitToVercel).toHaveBeenCalled()
      expect(importProject).toHaveBeenCalled()
      expect(mockRes.status).toHaveBeenCalledWith(200)
    })

    it('should handle environment validation errors', async () => {
      const { validateEnvironment } = await import('../../../lib/config.ts')
      
      validateEnvironment.mockReturnValue({
        isValid: false,
        missing: ['TWILIO_ACCOUNT_SID', 'OPENAI_API_KEY']
      })

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Server configuration error')
    })

    it('should handle processing errors gracefully', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { generateProjectName } = await import('../../../lib/utils.ts')
      const { generateTimeoutFallbackHTML } = await import('../../../lib/utils.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { saveProject } = await import('../../../lib/database.ts')

      classifyIntent.mockRejectedValue(new Error('Classification failed'))
      generateProjectName.mockResolvedValue('fallback-project')
      generateTimeoutFallbackHTML.mockReturnValue('<html><body>Error</body></html>')
      deployToVercel.mockResolvedValue('https://fallback.vercel.app')
      saveProject.mockResolvedValue(null)

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Error')
    })

    it('should handle timeout scenarios', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject } = await import('../../../lib/database.ts')
      const { generateProjectName, createTimeoutPromise, generateTimeoutFallbackHTML } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { saveProject } = await import('../../../lib/database.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create a complex website'
      })

      getLastProject.mockResolvedValue(null)
      generateProjectName.mockResolvedValue('complex-website')
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      
      // Mock timeout promise that rejects after a short delay
      const timeoutError = new Error('Process timed out')
      createTimeoutPromise.mockReturnValue(
        new Promise((_, reject) => setTimeout(() => reject(timeoutError), 100))
      )
      
      // Mock generateWebsite to take longer than timeout
      generateWebsite.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve('<html></html>'), 200))
      )

      generateTimeoutFallbackHTML.mockReturnValue('<html><body>Timeout</body></html>')
      deployToVercel.mockResolvedValue('https://timeout-fallback.vercel.app')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(generateTimeoutFallbackHTML).toHaveBeenCalled()
      expect(mockRes.status).toHaveBeenCalledWith(200)
    })

    it('should handle edit request when no previous project exists', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject, saveProject } = await import('../../../lib/database.ts')
      const { generateProjectName, createTimeoutPromise } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      classifyIntent.mockResolvedValue({
        intent: 'edit_site',
        description: 'Change the colors'
      })

      getLastProject.mockResolvedValue(null) // No previous project
      generateProjectName.mockResolvedValue('new-website')
      createTimeoutPromise.mockReturnValue(new Promise(() => {})) // Never resolves
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>New</body></html>')
      deployToVercel.mockResolvedValue('https://new-website.vercel.app')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      // Should create new site instead of editing
      expect(generateWebsite).toHaveBeenCalled()
      expect(saveProject).toHaveBeenCalled()
      expect(mockRes.status).toHaveBeenCalledWith(200)
    })

    it('should handle GitHub integration when available', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject } = await import('../../../lib/database.ts')
      const { generateProjectName, createTimeoutPromise } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { createRepository } = await import('../../../lib/github.ts')
      const { connectGitToVercel, validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')
      const { saveProject } = await import('../../../lib/database.ts')

      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create a portfolio'
      })

      getLastProject.mockResolvedValue(null)
      generateProjectName.mockResolvedValue('portfolio')
      createTimeoutPromise.mockReturnValue(new Promise(() => {})) // Never resolves
      validateGitIntegrationConfig.mockReturnValue({ isValid: true })
      generateWebsite.mockResolvedValue('<html><body>Portfolio</body></html>')
      createRepository.mockResolvedValue({
        repoUrl: 'https://github.com/user/portfolio',
        repoName: 'user/portfolio'
      })
      connectGitToVercel.mockResolvedValue({
        deploymentUrl: 'https://portfolio.vercel.app'
      })
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(createRepository).toHaveBeenCalled()
      expect(connectGitToVercel).toHaveBeenCalled()
      expect(saveProject).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          githubRepoUrl: 'https://github.com/user/portfolio',
          githubRepoName: 'user/portfolio'
        })
      )
      expect(mockRes.status).toHaveBeenCalledWith(200)
    })
  })

  describe('GET requests', () => {
    it('should handle GET requests for webhook verification', async () => {
      mockReq.method = 'GET'

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(200)
      expect(mockRes.send).toHaveBeenCalledWith('Ready to receive WhatsApp webhooks.')
    })
  })

  describe('error scenarios', () => {
    it('should handle formidable parsing errors', async () => {
      const formidable = (await import('formidable')).default
      
      formidable.mockReturnValue({
        parse: vi.fn().mockRejectedValue(new Error('Parse error'))
      })

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Error')
    })

    it('should handle missing required fields in request', async () => {
      const formidable = (await import('formidable')).default
      
      formidable.mockReturnValue({
        parse: vi.fn().mockResolvedValue([
          {}, // Empty fields
          {}
        ])
      })

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Error')
    })

    it('should handle database connection errors', async () => {
      const { getLastProject } = await import('../../../lib/database.ts')
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')

      classifyIntent.mockResolvedValue({
        intent: 'edit_site',
        description: 'Edit website'
      })

      getLastProject.mockRejectedValue(new Error('Database connection failed'))

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Error')
    })

    it('should handle AI service errors', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')

      classifyIntent.mockRejectedValue(new Error('AI service unavailable'))

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.send).toHaveBeenCalledWith('Error')
    })

    it('should handle deployment service errors', async () => {
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { getLastProject } = await import('../../../lib/database.ts')
      const { generateProjectName, createTimeoutPromise, generateTimeoutFallbackHTML } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { saveProject } = await import('../../../lib/database.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create website'
      })

      getLastProject.mockResolvedValue(null)
      generateProjectName.mockResolvedValue('test-website')
      createTimeoutPromise.mockReturnValue(new Promise(() => {})) // Never resolves
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>Test</body></html>')
      
      // First call fails, second call (for fallback) succeeds
      deployToVercel
        .mockRejectedValueOnce(new Error('Deployment failed'))
        .mockResolvedValueOnce('https://fallback.vercel.app')
      
      generateTimeoutFallbackHTML.mockReturnValue('<html><body>Fallback</body></html>')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(mockRes.status).toHaveBeenCalledWith(200)
      expect(mockRes.send).toHaveBeenCalledWith('Success')
    })
  })

  describe('status messages', () => {
    it('should send appropriate status messages during workflow', async () => {
      const { sendStatusMessage } = await import('../../../lib/messaging.ts')
      const { classifyIntent } = await import('../../../lib/intent-classifier.ts')
      const { generateProjectName } = await import('../../../lib/utils.ts')
      const { generateWebsite } = await import('../../../lib/website-generator.ts')
      const { deployToVercel } = await import('../../../lib/deployment.ts')
      const { saveProject } = await import('../../../lib/database.ts')
      const { validateGitIntegrationConfig } = await import('../../../lib/vercel-git.ts')

      classifyIntent.mockResolvedValue({
        intent: 'new_site',
        description: 'Create a blog'
      })

      generateProjectName.mockResolvedValue('my-blog')
      validateGitIntegrationConfig.mockReturnValue({ isValid: false })
      generateWebsite.mockResolvedValue('<html><body>Blog</body></html>')
      deployToVercel.mockResolvedValue('https://my-blog.vercel.app')
      saveProject.mockResolvedValue(mockSupabaseData.userProject)

      await handler(mockReq, mockRes)

      expect(sendStatusMessage).toHaveBeenCalledWith(
        expect.any(String),
        mockTwilio,
        'text_received',
        expect.any(Object)
      )

      expect(sendStatusMessage).toHaveBeenCalledWith(
        expect.any(String),
        mockTwilio,
        'intent_classified',
        expect.any(Object)
      )

      expect(sendStatusMessage).toHaveBeenCalledWith(
        expect.any(String),
        mockTwilio,
        'project_named',
        expect.any(Object)
      )
    })
  })
})