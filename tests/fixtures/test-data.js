/**
 * Test data and fixtures for unit and integration tests
 */

import {
  createMockGenAI,
  createMockTwilioClient,
  createMockSupabaseClient,
  createMockOpenAI,
  createMockFetch,
  resetAllMocks
} from '../helpers/mock-clients.js'

// Export mock creation functions with the names expected by tests
export const createMockTwilio = createMockTwilioClient
export const createMockSupabase = createMockSupabaseClient
export { resetAllMocks }

// Create default mock instances
export const mockGenAI = createMockGenAI()
export const mockTwilio = createMockTwilioClient()
export const mockSupabase = createMockSupabaseClient()
export const mockOpenAI = createMockOpenAI()
export const mockFetch = createMockFetch()

export const mockHtmlContent = {
  simple: '<html><head><title>Test</title></head><body><h1>Test Site</h1></body></html>',
  
  complex: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Website</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { background: #333; color: white; padding: 20px; }
        .content { padding: 20px; }
        .footer { background: #f0f0f0; padding: 10px; text-align: center; }
    </style>
</head>
<body>
    <header class="header">
        <h1>John Doe</h1>
        <p>Web Developer</p>
    </header>
    <main class="content">
        <section>
            <h2>About Me</h2>
            <p>I am a passionate web developer with experience in modern technologies.</p>
        </section>
        <section>
            <h2>Projects</h2>
            <div class="project">
                <h3>E-commerce Site</h3>
                <p>Built with React and Node.js</p>
            </div>
        </section>
    </main>
    <footer class="footer">
        <p>&copy; 2024 John Doe. All rights reserved.</p>
    </footer>
</body>
</html>`,

  invalid: '<html><body><h1>Unclosed tag<p>Missing closing tags</body></html>',
  
  minimal: '<!DOCTYPE html><html><body><h1>Hello World</h1></body></html>',
  
  withScripts: `<!DOCTYPE html>
<html>
<head><title>Interactive Site</title></head>
<body>
    <h1>Interactive Website</h1>
    <button onclick="alert('Hello!')">Click Me</button>
    <script>
        console.log('Page loaded');
    </script>
</body>
</html>`
}

export const mockUserMessages = {
  newSite: {
    simple: 'Create a simple website',
    portfolio: 'Create a portfolio website for a web developer with dark theme',
    ecommerce: 'Make an e-commerce site for handmade jewelry with purple and gold colors',
    restaurant: 'Build a website for my Italian restaurant with menu and contact info',
    blog: 'Create a personal blog about travel experiences'
  },
  
  editSite: {
    colorChange: 'Change the background color to blue',
    addSection: 'Add a contact form and footer section',
    styleUpdate: 'Make the text larger and change font to serif',
    contentAdd: 'Add a new project to the portfolio section',
    layoutChange: 'Make the layout responsive for mobile devices'
  },
  
  importRepo: {
    simple: 'https://github.com/user/repo',
    withEdit: 'https://github.com/user/repo - change the header color to red',
    githubUrl: 'Work on https://github.com/testuser/my-website',
    shortUrl: 'github.com/user/project - add a contact form'
  }
}

export const mockGitHubData = {
  repository: {
    full_name: 'testuser/test-repo',
    name: 'test-repo',
    html_url: 'https://github.com/testuser/test-repo',
    clone_url: 'https://github.com/testuser/test-repo.git',
    default_branch: 'main',
    description: 'Test repository for unit tests',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
}

export const mockGitHubResponses = {
  createRepo: {
    success: {
      full_name: 'testuser/test-repo',
      html_url: 'https://github.com/testuser/test-repo',
      clone_url: 'https://github.com/testuser/test-repo.git',
      default_branch: 'main',
      description: 'Test repository for unit tests'
    },
    
    conflict: {
      status: 422,
      message: 'Repository creation failed',
      errors: [
        { field: 'name', message: 'name already exists on this account' }
      ]
    }
  },
  
  getFile: {
    success: {
      content: Buffer.from(mockHtmlContent.simple).toString('base64'),
      sha: 'abc123def456',
      size: mockHtmlContent.simple.length
    }
  },
  
  updateFile: {
    success: {
      commit: {
        sha: 'def456abc789',
        html_url: 'https://github.com/testuser/test-repo/commit/def456abc789'
      }
    }
  }
}

export const mockSupabaseData = {
  userProject: {
    id: 1,
    phone_number: '+**********',
    project_name: 'test-portfolio',
    description: 'A test portfolio website',
    url: 'https://test-portfolio.vercel.app',
    html_content: mockHtmlContent.simple,
    github_repo_url: 'https://github.com/testuser/test-portfolio',
    github_repo_name: 'testuser/test-portfolio',
    last_commit_sha: 'abc123def456',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  
  savedProject: {
    id: 2,
    phone_number: '+**********',
    project_name: 'Test Import Project',
    description: 'Imported project description',
    url: 'https://imported.vercel.app',
    html_content: '<html><body>Imported content</body></html>',
    github_repo_url: 'https://github.com/user/test-repo',
    github_repo_name: 'user/test-repo',
    last_commit_sha: 'abc123def456',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  
  importData: {
    projectName: 'Test Import Project',
    description: 'Imported project description',
    url: 'https://imported.vercel.app',
    htmlContent: '<html><body>Imported content</body></html>',
    githubRepoUrl: 'https://github.com/user/test-repo',
    githubRepoName: 'user/test-repo',
    lastCommitSha: 'abc123def456'
  },
  
  multipleProjects: [
    {
      id: 1,
      project_name: 'portfolio-site',
      description: 'Personal portfolio',
      url: 'https://portfolio.vercel.app',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      project_name: 'blog-site',
      description: 'Personal blog',
      url: 'https://blog.vercel.app',
      created_at: '2024-01-02T00:00:00Z'
    }
  ]
}

export const mockVercelResponses = {
  deployment: {
    success: {
      url: 'https://test-deployment-abc123.vercel.app',
      deploymentId: 'dpl_abc123def456',
      readyState: 'READY'
    },
    
    building: {
      url: 'https://test-deployment-def456.vercel.app',
      deploymentId: 'dpl_def456abc789',
      readyState: 'BUILDING'
    }
  },
  
  project: {
    success: {
      id: 'prj_abc123',
      name: 'test-project',
      framework: null,
      gitRepository: {
        type: 'github',
        repo: 'testuser/test-repo'
      }
    }
  }
}

export const mockAIResponses = {
  commitMessages: {
    newSite: 'feat: create portfolio website with dark theme',
    editSite: 'style: update background color to blue',
    addFeature: 'feat: add contact form and footer section'
  },
  
  projectNames: {
    portfolio: 'portfolio-website',
    ecommerce: 'jewelry-store',
    restaurant: 'italian-restaurant',
    blog: 'travel-blog'
  },
  
  repositoryDescriptions: {
    portfolio: 'Professional portfolio website showcasing web development projects and skills',
    ecommerce: 'Modern e-commerce website for handmade jewelry with shopping cart functionality',
    restaurant: 'Italian restaurant website featuring menu, location, and contact information'
  }
}

export const mockWhatsAppData = {
  textMessage: {
    fields: {
      From: ['whatsapp:+**********'],
      To: ['whatsapp:+***********'],
      Body: ['Create a portfolio website'],
      NumMedia: ['0'],
      MessageSid: ['SM**********abcdef'],
      AccountSid: ['AC**********abcdef']
    }
  },
  
  audioMessage: {
    fields: {
      From: ['whatsapp:+**********'],
      To: ['whatsapp:+***********'],
      Body: [''],
      NumMedia: ['1'],
      MediaUrl0: ['https://api.twilio.com/2010-04-01/Accounts/AC123/Messages/MM123/Media/ME123'],
      MediaContentType0: ['audio/ogg'],
      MessageSid: ['SM**********abcdef'],
      AccountSid: ['AC**********abcdef']
    }
  }
}

export const mockPhoneNumbers = {
  valid: '+**********',
  whatsapp: 'whatsapp:+**********',
  international: '+***********',
  normalized: '+**********'
}

export const mockEnvironmentVars = {
  complete: {
    GITHUB_TOKEN: 'ghp_test_token_123',
    GOOGLE_API_KEY: 'AIza_test_key_456',
    TWILIO_ACCOUNT_SID: 'AC_test_sid_789',
    TWILIO_AUTH_TOKEN: 'test_auth_token',
    SUPABASE_URL: 'https://test.supabase.co',
    SUPABASE_ANON_KEY: 'test_supabase_key',
    VERCEL_API_TOKEN: 'test_vercel_token'
  },
  
  missing: {
    GITHUB_TOKEN: undefined,
    GOOGLE_API_KEY: 'AIza_test_key_456'
  }
}