#!/usr/bin/env node

/**
 * Comprehensive test runner script
 * Provides utilities for running different test suites and generating reports
 */

import { spawn } from 'child_process'
import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const dir = dirname(__filename)
const rootDir = join(dir, '..')

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// Test suite configurations
const testSuites = {
  unit: {
    name: 'Unit Tests',
    command: 'vitest',
    args: ['run', '--config', 'vitest.config.js', 'tests/unit'],
    description: 'Fast isolated unit tests'
  },
  integration: {
    name: 'Integration Tests',
    command: 'vitest',
    args: ['run', '--config', 'vitest.config.js', 'tests/integration'],
    description: 'Integration tests with real dependencies'
  },
  coverage: {
    name: 'Coverage Report',
    command: 'vitest',
    args: ['run', '--coverage', '--config', 'vitest.config.js'],
    description: 'Generate code coverage report'
  },
  watch: {
    name: 'Watch Mode',
    command: 'vitest',
    args: ['--config', 'vitest.config.js'],
    description: 'Run tests in watch mode'
  },
  ui: {
    name: 'Test UI',
    command: 'vitest',
    args: ['--ui', '--config', 'vitest.config.js'],
    description: 'Open Vitest UI in browser'
  }
}

/**
 * Print colored output to console
 */
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

/**
 * Print test suite header
 */
function printHeader(title) {
  const border = '='.repeat(60)
  colorLog(border, 'cyan')
  colorLog(`  ${title}`, 'bright')
  colorLog(border, 'cyan')
}

/**
 * Print test suite summary
 */
function printSummary(results) {
  printHeader('TEST SUMMARY')
  
  let totalPassed = 0
  let totalFailed = 0
  let totalTime = 0
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    const color = result.success ? 'green' : 'red'
    
    colorLog(`${status} ${result.suite}: ${result.message}`, color)
    
    if (result.stats) {
      totalPassed += result.stats.passed || 0
      totalFailed += result.stats.failed || 0
      totalTime += result.stats.time || 0
    }
  })
  
  console.log()
  colorLog(`Total Tests: ${totalPassed + totalFailed}`, 'bright')
  colorLog(`Passed: ${totalPassed}`, 'green')
  colorLog(`Failed: ${totalFailed}`, totalFailed > 0 ? 'red' : 'green')
  colorLog(`Time: ${totalTime.toFixed(2)}s`, 'blue')
  
  const overallSuccess = results.every(r => r.success)
  const overallStatus = overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'
  const overallColor = overallSuccess ? 'green' : 'red'
  
  console.log()
  colorLog(overallStatus, overallColor)
}

/**
 * Run a test suite
 */
function runTestSuite(suiteName, options = {}) {
  return new Promise((resolve) => {
    const suite = testSuites[suiteName]
    if (!suite) {
      resolve({
        suite: suiteName,
        success: false,
        message: 'Unknown test suite',
        error: `Test suite '${suiteName}' not found`
      })
      return
    }
    
    printHeader(`Running ${suite.name}`)
    colorLog(suite.description, 'blue')
    console.log()
    
    const startTime = Date.now()
    const args = [...suite.args]
    
    // Add additional options
    if (options.reporter) {
      args.push('--reporter', options.reporter)
    }
    if (options.outputFile) {
      args.push('--outputFile', options.outputFile)
    }
    if (options.silent) {
      args.push('--silent')
    }
    
    const child = spawn('npx', [suite.command, ...args], {
      stdio: options.silent ? 'pipe' : 'inherit',
      cwd: rootDir,
      env: {
        ...process.env,
        NODE_ENV: 'test',
        FORCE_COLOR: '1'
      }
    })
    
    let output = ''
    if (options.silent) {
      child.stdout.on('data', (data) => {
        output += data.toString()
      })
      child.stderr.on('data', (data) => {
        output += data.toString()
      })
    }
    
    child.on('close', (code) => {
      const endTime = Date.now()
      const duration = (endTime - startTime) / 1000
      
      const success = code === 0
      const message = success 
        ? `Completed in ${duration.toFixed(2)}s`
        : `Failed with exit code ${code}`
      
      // Parse test statistics from output if available
      let stats = null
      if (output) {
        const testMatch = output.match(/(\d+) passed.*?(\d+) failed/i)
        if (testMatch) {
          stats = {
            passed: parseInt(testMatch[1]),
            failed: parseInt(testMatch[2]),
            time: duration
          }
        }
      }
      
      resolve({
        suite: suite.name,
        success,
        message,
        stats,
        output: options.silent ? output : null,
        duration
      })
    })
    
    child.on('error', (error) => {
      resolve({
        suite: suite.name,
        success: false,
        message: 'Failed to start',
        error: error.message
      })
    })
  })
}

/**
 * Generate test report
 */
function generateReport(results, format = 'json') {
  const reportDir = join(rootDir, 'test-reports')
  if (!existsSync(reportDir)) {
    mkdirSync(reportDir, { recursive: true })
  }
  
  const timestamp = new Date().toISOString()
  const report = {
    timestamp,
    results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      duration: results.reduce((sum, r) => sum + (r.duration || 0), 0)
    }
  }
  
  if (format === 'json') {
    const reportFile = join(reportDir, `test-report-${Date.now()}.json`)
    writeFileSync(reportFile, JSON.stringify(report, null, 2))
    colorLog(`📄 Report saved to: ${reportFile}`, 'blue')
  }
  
  return report
}

/**
 * Check test environment
 */
function checkEnvironment() {
  colorLog('🔍 Checking test environment...', 'yellow')
  
  const checks = [
    {
      name: 'Node.js version',
      check: () => process.version,
      expected: 'v18+ or v20+'
    },
    {
      name: 'NPM packages',
      check: () => existsSync(join(rootDir, 'node_modules')),
      expected: 'Installed'
    },
    {
      name: 'Vitest config',
      check: () => existsSync(join(rootDir, 'vitest.config.js')),
      expected: 'Present'
    },
    {
      name: 'Test directories',
      check: () => existsSync(join(rootDir, 'tests')),
      expected: 'Present'
    }
  ]
  
  let allPassed = true
  
  checks.forEach(check => {
    try {
      const result = check.check()
      const passed = result === true || (typeof result === 'string' && result.length > 0)
      const status = passed ? '✅' : '❌'
      const color = passed ? 'green' : 'red'
      
      colorLog(`${status} ${check.name}: ${result || 'Failed'}`, color)
      
      if (!passed) allPassed = false
    } catch (error) {
      colorLog(`❌ ${check.name}: Error - ${error.message}`, 'red')
      allPassed = false
    }
  })
  
  console.log()
  return allPassed
}

/**
 * Main CLI handler
 */
async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'help'
  
  switch (command) {
    case 'unit':
      await runSingleSuite('unit')
      break
      
    case 'integration':
      await runSingleSuite('integration')
      break
      
    case 'coverage':
      await runSingleSuite('coverage')
      break
      
    case 'watch':
      await runSingleSuite('watch')
      break
      
    case 'ui':
      await runSingleSuite('ui')
      break
      
    case 'all':
      await runAllSuites()
      break
      
    case 'check':
      checkEnvironment()
      break
      
    case 'help':
    default:
      printHelp()
      break
  }
}

/**
 * Run a single test suite
 */
async function runSingleSuite(suiteName) {
  if (!checkEnvironment()) {
    colorLog('❌ Environment check failed. Please fix issues before running tests.', 'red')
    process.exit(1)
  }
  
  const result = await runTestSuite(suiteName)
  
  if (!result.success) {
    colorLog(`\n❌ ${result.suite} failed: ${result.message}`, 'red')
    if (result.error) {
      colorLog(result.error, 'red')
    }
    process.exit(1)
  } else {
    colorLog(`\n✅ ${result.suite} completed successfully`, 'green')
  }
}

/**
 * Run all test suites
 */
async function runAllSuites() {
  if (!checkEnvironment()) {
    colorLog('❌ Environment check failed. Please fix issues before running tests.', 'red')
    process.exit(1)
  }
  
  const suitesToRun = ['unit', 'integration', 'coverage']
  const results = []
  
  for (const suiteName of suitesToRun) {
    const result = await runTestSuite(suiteName, { silent: false })
    results.push(result)
    
    // Add delay between suites
    if (suiteName !== suitesToRun[suitesToRun.length - 1]) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  printSummary(results)
  generateReport(results)
  
  const hasFailures = results.some(r => !r.success)
  if (hasFailures) {
    process.exit(1)
  }
}

/**
 * Print help information
 */
function printHelp() {
  printHeader('Test Runner Help')
  
  colorLog('Usage: npm run test:runner <command>', 'bright')
  console.log()
  
  colorLog('Commands:', 'bright')
  colorLog('  unit         Run unit tests only', 'blue')
  colorLog('  integration  Run integration tests only', 'blue')
  colorLog('  coverage     Generate coverage report', 'blue')
  colorLog('  watch        Run tests in watch mode', 'blue')
  colorLog('  ui           Open Vitest UI', 'blue')
  colorLog('  all          Run all test suites', 'blue')
  colorLog('  check        Check test environment', 'blue')
  colorLog('  help         Show this help message', 'blue')
  
  console.log()
  colorLog('Examples:', 'bright')
  colorLog('  npm run test:runner unit', 'yellow')
  colorLog('  npm run test:runner all', 'yellow')
  colorLog('  npm run test:runner coverage', 'yellow')
  
  console.log()
  colorLog('Available test suites:', 'bright')
  Object.entries(testSuites).forEach(([key, suite]) => {
    colorLog(`  ${key.padEnd(12)} ${suite.description}`, 'cyan')
  })
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    colorLog(`❌ Error: ${error.message}`, 'red')
    process.exit(1)
  })
}

export { runTestSuite, generateReport, checkEnvironment }