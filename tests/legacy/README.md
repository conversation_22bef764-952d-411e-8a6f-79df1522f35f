# Legacy Tests

This directory contains the original test files that were standalone Node.js scripts. These tests have been preserved for reference and migration purposes.

## Original Test Files

- `test-ai-commit-generator.js` - Tests for AI commit message generation
- `test-intent-classifier.js` - Tests for message intent classification  
- `test-new-repository-description.js` - Tests for repository description generation

## Migration Status

These legacy tests have been migrated to the new Vitest-based testing framework:

- ✅ **AI Commit Generator** → `tests/unit/lib/ai-commit-generator.test.js`
- ✅ **Intent Classifier** → `tests/unit/lib/intent-classifier.test.js`
- ✅ **Repository Description** → Functionality integrated into GitHub tests

## Key Differences

### Legacy Approach
- Standalone Node.js scripts
- Manual test execution with `node tests/test-*.js`
- Basic console.log assertions
- No test framework or structured organization
- No mocking or isolation

### New Vitest Approach
- Proper test framework with structured test suites
- Comprehensive mocking of external dependencies
- Organized into unit and integration test categories
- Code coverage reporting
- CI/CD integration
- Watch mode and UI for development

## Running Legacy Tests

If you need to run the original tests for comparison:

```bash
# Run individual legacy tests
node tests/legacy/test-ai-commit-generator.js
node tests/legacy/test-intent-classifier.js
node tests/legacy/test-new-repository-description.js
```

**Note:** Legacy tests require actual API keys and will make real API calls. The new Vitest tests use mocks and don't require real credentials.

## Migration Benefits

1. **Faster Execution** - Mocked dependencies eliminate network calls
2. **Reliable Testing** - No dependency on external service availability
3. **Better Coverage** - Comprehensive test scenarios including edge cases
4. **CI/CD Ready** - Automated testing in GitHub Actions
5. **Developer Experience** - Watch mode, UI, and better error reporting
6. **Maintainability** - Structured test organization and shared utilities

## Cleanup

These legacy files can be safely removed once the migration is fully validated and the new test suite is proven to work correctly in all environments.