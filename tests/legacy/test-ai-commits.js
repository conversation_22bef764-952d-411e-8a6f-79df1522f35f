/**
 * Test script for AI-powered commit message generation
 * Run with: node tests/test-ai-commits.js
 */

import dotenv from 'dotenv';
import { GoogleGenAI } from "@google/genai";
import { generateEnhancedCommitMessage } from "../lib/ai-commit-generator.ts";

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

// Test configuration
const testCases = [
    {
        name: "New Website Creation",
        userMessage: "Create a portfolio website for a web developer with dark theme",
        isEdit: false,
        oldContent: null,
        newContent: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Web Developer</title>
    <style>
        body { background: #1a1a1a; color: #fff; font-family: Arial, sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .hero { text-align: center; padding: 100px 0; }
        .projects { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <section class="hero">
            <h1>John Doe</h1>
            <p>Full Stack Web Developer</p>
        </section>
        <section class="projects">
            <div class="project">
                <h3>Project 1</h3>
                <p>Description of project 1</p>
            </div>
        </section>
    </div>
</body>
</html>`,
        projectName: "portfolio-website"
    },
    {
        name: "Style Update",
        userMessage: "Change the background color to blue and make the text larger",
        isEdit: true,
        oldContent: `<!DOCTYPE html>
<html><head><style>body { background: #1a1a1a; color: #fff; font-size: 16px; }</style></head>
<body><h1>My Website</h1></body></html>`,
        newContent: `<!DOCTYPE html>
<html><head><style>body { background: #0066cc; color: #fff; font-size: 20px; }</style></head>
<body><h1>My Website</h1></body></html>`,
        projectName: "my-website"
    },
    {
        name: "Content Addition",
        userMessage: "Add a contact form and footer section",
        isEdit: true,
        oldContent: `<!DOCTYPE html>
<html><body><h1>Welcome</h1><p>About us content</p></body></html>`,
        newContent: `<!DOCTYPE html>
<html><body><h1>Welcome</h1><p>About us content</p>
<form><input type="email" placeholder="Email"><button>Submit</button></form>
<footer><p>&copy; 2024 My Company</p></footer></body></html>`,
        projectName: "company-website"
    }
];

async function runTests() {
    console.log("🤖 Testing AI-Powered Commit Message Generation\n");
    
    // Check if Google API key is available
    if (!process.env.GOOGLE_API_KEY) {
        console.error("❌ GOOGLE_API_KEY environment variable is required");
        console.log("Please set your Google API key in your environment variables");
        process.exit(1);
    }
    
    // Initialize Google AI
    const genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    
    for (const testCase of testCases) {
        console.log(`\n📝 Test: ${testCase.name}`);
        console.log(`User Message: "${testCase.userMessage}"`);
        console.log(`Project: ${testCase.projectName}`);
        console.log(`Is Edit: ${testCase.isEdit}`);
        
        try {
            const startTime = Date.now();
            
            const commitMessage = await generateEnhancedCommitMessage(
                testCase.userMessage,
                testCase.isEdit,
                testCase.oldContent,
                testCase.newContent,
                testCase.projectName,
                genAI
            );
            
            const duration = Date.now() - startTime;
            
            console.log(`✅ Generated Commit: "${commitMessage}"`);
            console.log(`⏱️  Generation Time: ${duration}ms`);
            
            // Validate commit message
            if (commitMessage.length > 72) {
                console.log(`⚠️  Warning: Commit message is longer than 72 characters (${commitMessage.length})`);
            }
            
            if (!commitMessage.includes(':')) {
                console.log(`⚠️  Warning: Commit message doesn't follow conventional format`);
            }
            
        } catch (error) {
            console.error(`❌ Error generating commit message:`, error.message);
        }
        
        console.log("─".repeat(80));
    }
    
    console.log("\n🎉 AI Commit Message Testing Complete!");
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(console.error);
}

export { runTests };