/**
 * Test script for the new generateRepositoryDescription function
 * This tests the AI-powered repository description generation that analyzes website content
 */

import { GoogleGenAI } from '@google/genai';
import dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

// Import the function we want to test (we'll need to extract it or make it exportable)
// For now, we'll copy the function here for testing
async function generateRepositoryDescription(userRequest, htmlContent, projectName, genAI) {
    console.log(`=== GENERATING REPOSITORY DESCRIPTION WITH GEMINI-2.5-FLASH ===`);
    
    try {
        // Extract key information from HTML content for context
        const htmlPreview = htmlContent.substring(0, 1000); // First 1000 chars for context

        const prompt = `Analyze this website project and create a professional GitHub repository description (MAX 280 characters):

Project Name: ${projectName}
User Request: "${userRequest}"
Website HTML Preview: ${htmlPreview}

Create a description that:
- Describes what the website IS (not what the user requested)
- Focuses on the website's purpose and functionality
- Is professional and concise
- Avoids personal details from the user request
- Highlights the main features/content
- Is under 280 characters

Examples of good descriptions:
- "Modern e-commerce website for handmade jewelry with shopping cart and payment integration"
- "Educational platform for children's learning activities with interactive games and printables"
- "Professional portfolio website showcasing web development projects and skills"

Return only the repository description, no quotes or extra text.`;

        console.log(`Sending prompt to Gemini-2.0-flash...`);
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: prompt,
            config: {
                temperature: 0.7,
                topP: 0.8,
                maxOutputTokens: 120
            }
        });
        
        const repositoryDescription = result.text.trim();
        
        // Validate length and truncate if necessary
        if (repositoryDescription.length > 280) {
            console.log(`⚠️  AI description too long (${repositoryDescription.length} chars), truncating...`);
            return repositoryDescription.substring(0, 277) + '...';
        }
        
        console.log(`✅ Generated repository description: "${repositoryDescription}"`);
        console.log(`Length: ${repositoryDescription.length} characters`);
        
        return repositoryDescription;
        
    } catch (error) {
        console.error('Error generating repository description:', error);
        throw error;
    }
}

async function runTests() {
    console.log('=== TESTING NEW REPOSITORY DESCRIPTION GENERATION ===\n');
    
    // Initialize Google AI
    const genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    
    // Test cases with different types of websites
    const testCases = [
        {
            name: "E-commerce Store",
            userRequest: "I want a website for my handmade jewelry business. I make earrings, necklaces, and bracelets. I want to show my products with prices and have a contact form. Use purple and gold colors.",
            htmlContent: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elegant Jewelry Collection</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #6a0dad, #ffd700); }
        .header { text-align: center; padding: 2rem; color: white; }
        .products { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; padding: 2rem; }
        .product { background: white; border-radius: 10px; padding: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .price { font-size: 1.5rem; color: #6a0dad; font-weight: bold; }
        .contact-form { background: white; padding: 2rem; margin: 2rem; border-radius: 10px; }
    </style>
</head>
<body>
    <header class="header">
        <h1>Elegant Jewelry Collection</h1>
        <p>Handcrafted with Love</p>
    </header>
    <section class="products">
        <div class="product">
            <h3>Golden Sunset Earrings</h3>
            <p>Beautiful handcrafted earrings with gold accents</p>
            <div class="price">$45</div>
        </div>
        <div class="product">
            <h3>Purple Dream Necklace</h3>
            <p>Elegant necklace with purple gemstones</p>
            <div class="price">$85</div>
        </div>
        <div class="product">
            <h3>Charm Bracelet</h3>
            <p>Delicate bracelet with custom charms</p>
            <div class="price">$35</div>
        </div>
    </section>
    <section class="contact-form">
        <h2>Contact Us</h2>
        <form>
            <input type="text" placeholder="Your Name" required>
            <input type="email" placeholder="Your Email" required>
            <textarea placeholder="Your Message" required></textarea>
            <button type="submit">Send Message</button>
        </form>
    </section>
</body>
</html>`,
            projectName: "elegant-jewelry-collection"
        },
        {
            name: "Portfolio Website",
            userRequest: "Create a portfolio website for me. I'm a web developer named John Smith. Show my skills in React, Node.js, and Python. Include my projects and a contact section. Make it modern and professional.",
            htmlContent: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>John Smith - Web Developer</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: #f8f9fa; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 2rem; text-align: center; }
        .skills { display: flex; justify-content: center; gap: 2rem; padding: 3rem 2rem; }
        .skill { background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .projects { padding: 3rem 2rem; }
        .project { background: white; margin: 1rem 0; padding: 2rem; border-radius: 10px; }
    </style>
</head>
<body>
    <section class="hero">
        <h1>John Smith</h1>
        <h2>Full Stack Web Developer</h2>
        <p>Creating amazing web experiences with modern technologies</p>
    </section>
    <section class="skills">
        <div class="skill">
            <h3>React</h3>
            <p>Frontend Development</p>
        </div>
        <div class="skill">
            <h3>Node.js</h3>
            <p>Backend Development</p>
        </div>
        <div class="skill">
            <h3>Python</h3>
            <p>Data & Automation</p>
        </div>
    </section>
    <section class="projects">
        <h2>My Projects</h2>
        <div class="project">
            <h3>E-commerce Platform</h3>
            <p>Full-stack e-commerce solution built with React and Node.js</p>
        </div>
        <div class="project">
            <h3>Data Analytics Dashboard</h3>
            <p>Python-based dashboard for business intelligence</p>
        </div>
    </section>
</body>
</html>`,
            projectName: "john-smith-portfolio"
        },
        {
            name: "Restaurant Website",
            userRequest: "I need a website for my Italian restaurant called 'Mama Mia'. Show our menu with pasta, pizza, and desserts. Include our location and hours. Use red and green colors like the Italian flag.",
            htmlContent: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mama Mia Italian Restaurant</title>
    <style>
        body { font-family: Georgia, serif; margin: 0; background: #fff; }
        .header { background: linear-gradient(90deg, #d32f2f, #388e3c); color: white; padding: 3rem 2rem; text-align: center; }
        .menu { padding: 3rem 2rem; }
        .menu-section { margin: 2rem 0; }
        .menu-item { display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #eee; }
        .location { background: #f5f5f5; padding: 3rem 2rem; text-align: center; }
    </style>
</head>
<body>
    <header class="header">
        <h1>Mama Mia</h1>
        <p>Authentic Italian Cuisine</p>
    </header>
    <section class="menu">
        <h2>Our Menu</h2>
        <div class="menu-section">
            <h3>Pasta</h3>
            <div class="menu-item">
                <span>Spaghetti Carbonara</span>
                <span>$18</span>
            </div>
            <div class="menu-item">
                <span>Fettuccine Alfredo</span>
                <span>$16</span>
            </div>
        </div>
        <div class="menu-section">
            <h3>Pizza</h3>
            <div class="menu-item">
                <span>Margherita</span>
                <span>$14</span>
            </div>
            <div class="menu-item">
                <span>Pepperoni</span>
                <span>$16</span>
            </div>
        </div>
        <div class="menu-section">
            <h3>Desserts</h3>
            <div class="menu-item">
                <span>Tiramisu</span>
                <span>$8</span>
            </div>
        </div>
    </section>
    <section class="location">
        <h2>Visit Us</h2>
        <p>123 Little Italy Street, New York, NY 10013</p>
        <p>Open: Mon-Sun 11:00 AM - 10:00 PM</p>
    </section>
</body>
</html>`,
            projectName: "mama-mia-restaurant"
        }
    ];
    
    // Run tests
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n--- TEST ${i + 1}: ${testCase.name} ---`);
        console.log(`User Request: "${testCase.userRequest}"`);
        console.log(`Project Name: ${testCase.projectName}`);
        console.log(`HTML Content Length: ${testCase.htmlContent.length} characters`);
        
        try {
            const description = await generateRepositoryDescription(
                testCase.userRequest,
                testCase.htmlContent,
                testCase.projectName,
                genAI
            );
            
            console.log(`\n✅ SUCCESS!`);
            console.log(`Generated Description: "${description}"`);
            console.log(`Length: ${description.length} characters`);
            
            // Validate the description
            if (description.length > 280) {
                console.log(`❌ VALIDATION FAILED: Description too long (${description.length} > 280)`);
            } else if (description.length < 20) {
                console.log(`❌ VALIDATION FAILED: Description too short (${description.length} < 20)`);
            } else {
                console.log(`✅ VALIDATION PASSED: Description length is appropriate`);
            }
            
            // Check if description focuses on the website rather than the user request
            const userRequestWords = testCase.userRequest.toLowerCase().split(' ');
            const personalWords = ['i', 'my', 'me', 'want', 'need', 'called'];
            const hasPersonalWords = personalWords.some(word => description.toLowerCase().includes(word));
            
            if (hasPersonalWords) {
                console.log(`⚠️  WARNING: Description may contain personal references from user request`);
            } else {
                console.log(`✅ GOOD: Description focuses on the website, not personal details`);
            }
            
        } catch (error) {
            console.log(`❌ ERROR: ${error.message}`);
        }
        
        console.log(`\n${'='.repeat(60)}`);
    }
    
    console.log(`\n🎉 All tests completed!`);
}

// Run the tests
runTests().catch(console.error);