#!/usr/bin/env node

/**
 * Test script to validate GitHub repository creation with description optimization
 * This script tests the enhanced logging and Gemini-2.5-flash description generation
 */

import { GoogleGenAI } from '@google/genai';
import { createRepository } from '../lib/github.ts';

async function testGitHubDescriptionFix() {
    console.log('=== TESTING GITHUB DESCRIPTION FIX ===');
    
    // Validate environment variables
    const requiredEnvVars = ['GITHUB_TOKEN', 'GOOGLE_API_KEY'];
    const missing = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
        console.error('❌ Missing required environment variables:', missing);
        process.exit(1);
    }
    
    // Initialize Google AI client
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    
    // Test case 1: Long description that should trigger optimization
    const longDescription = "Make a marketing website for joyful, sorry, joy filled printables, which is a, I want a marketing site which is going to let me sell resources for teachers and in that they should, or for parents, excuse me, parents should be able to buy things for their children which will be educational, like toys and printable games and activities, all those sorts of things. Make the site fun, filled with colour and pop and it should really appeal to someone with young children.";
    
    console.log('\n=== TEST CASE 1: Long Description ===');
    console.log(`Original description length: ${longDescription.length} characters`);
    console.log(`Description: "${longDescription}"`);
    
    const testProjectName = `test-description-fix-${Date.now()}`;
    const testHtmlContent = `<!DOCTYPE html>
<html>
<head><title>Test Site</title></head>
<body><h1>Test Website</h1><p>This is a test website for GitHub description optimization.</p></body>
</html>`;

    try {
        console.log('\n=== ATTEMPTING REPOSITORY CREATION ===');
        
        const result = await createRepository(testProjectName, longDescription, testHtmlContent, {
            genAI,
            userMessage: longDescription
        });
        
        console.log('\n✅ REPOSITORY CREATION SUCCESSFUL!');
        console.log(`Repository URL: ${result.repoUrl}`);
        console.log(`Repository Name: ${result.repoName}`);
        console.log(`Clone URL: ${result.cloneUrl}`);
        
        // Clean up - delete the test repository
        console.log('\n=== CLEANING UP TEST REPOSITORY ===');
        await deleteTestRepository(result.repoName);
        
    } catch (error) {
        console.error('\n❌ REPOSITORY CREATION FAILED:');
        console.error('Error:', error.message);
        
        // Analyze the error
        if (error.message.includes('description cannot be more than 350 characters')) {
            console.log('\n🔍 DIAGNOSIS: Description length issue detected');
            console.log('✅ Enhanced logging is working correctly');
            console.log('❌ Description optimization may have failed');
        } else if (error.message.includes('already exists')) {
            console.log('\n🔍 DIAGNOSIS: Repository name conflict detected');
            console.log('✅ Name conflict detection is working');
        } else if (error.message.includes('401')) {
            console.log('\n🔍 DIAGNOSIS: Authentication error');
            console.log('❌ GitHub token may be invalid or expired');
        } else if (error.message.includes('403')) {
            console.log('\n🔍 DIAGNOSIS: Permission error');
            console.log('❌ GitHub token may lack repository creation permissions');
        } else {
            console.log('\n🔍 DIAGNOSIS: Unknown error');
            console.log('❌ Unexpected error occurred');
        }
        
        console.log('\n=== DIAGNOSTIC INFORMATION ===');
        console.log(`GitHub token present: ${!!process.env.GITHUB_TOKEN}`);
        console.log(`GitHub token length: ${process.env.GITHUB_TOKEN ? process.env.GITHUB_TOKEN.length : 0}`);
        console.log(`Google API key present: ${!!process.env.GOOGLE_API_KEY}`);
        console.log(`Google API key length: ${process.env.GOOGLE_API_KEY ? process.env.GOOGLE_API_KEY.length : 0}`);
    }
}

/**
 * Deletes a test repository to clean up
 * @param {string} repoName - Full repository name (owner/repo)
 */
async function deleteTestRepository(repoName) {
    try {
        const response = await fetch(`https://api.github.com/repos/${repoName}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${process.env.GITHUB_TOKEN}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        
        if (response.ok) {
            console.log(`✅ Test repository ${repoName} deleted successfully`);
        } else {
            console.log(`⚠️  Could not delete test repository ${repoName} (status: ${response.status})`);
            console.log('You may need to delete it manually from GitHub');
        }
    } catch (error) {
        console.log(`⚠️  Error deleting test repository: ${error.message}`);
        console.log('You may need to delete it manually from GitHub');
    }
}

// Run the test
testGitHubDescriptionFix().catch(console.error);