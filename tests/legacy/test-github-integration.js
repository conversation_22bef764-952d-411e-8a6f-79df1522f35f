/**
 * Test script to validate GitHub integration fixes
 */

import { createRepository } from '../lib/github.ts';
import { validateGitIntegrationConfig } from '../lib/vercel-git.ts';
import { sendWhatsappMessage } from '../lib/messaging.ts';

// Mock Twilio client for testing
const mockTwilioClient = {
    messages: {
        create: async (params) => {
            console.log(`✅ Mock WhatsApp message sent to ${params.to}: "${params.body}"`);
            return { sid: 'mock-message-id' };
        }
    }
};

async function testGitHubIntegration() {
    console.log('=== TESTING GITHUB INTEGRATION FIXES ===\n');
    
    // Test 1: Validate Git Integration Config
    console.log('1. Testing Git Integration Configuration...');
    const gitConfig = validateGitIntegrationConfig();
    console.log(`   Git integration valid: ${gitConfig.isValid}`);
    if (!gitConfig.isValid) {
        console.log(`   Message: ${gitConfig.message}`);
    }
    console.log('');
    
    // Test 2: Test WhatsApp messaging function
    console.log('2. Testing WhatsApp messaging function...');
    try {
        await sendWhatsappMessage(
            'whatsapp:+1234567890',
            'Test message for GitHub integration debugging',
            mockTwilioClient
        );
        console.log('   ✅ WhatsApp messaging function works correctly');
    } catch (error) {
        console.log('   ❌ WhatsApp messaging function failed:', error.message);
    }
    console.log('');
    
    // Test 3: Test GitHub repository creation (only if config is valid)
    if (gitConfig.isValid) {
        console.log('3. Testing GitHub repository creation...');
        try {
            // Generate a unique test repository name
            const testRepoName = `test-repo-${Date.now()}`;
            const testDescription = 'Test repository for debugging GitHub integration';
            const testHtmlContent = '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Test Site</h1></body></html>';
            
            console.log(`   Creating test repository: ${testRepoName}`);
            
            const repoInfo = await createRepository(
                testRepoName,
                testDescription,
                testHtmlContent,
                {
                    // No AI options for test
                }
            );
            
            console.log('   ✅ GitHub repository created successfully!');
            console.log(`   Repository URL: ${repoInfo.repoUrl}`);
            console.log(`   Repository name: ${repoInfo.repoName}`);
            
        } catch (error) {
            console.log('   ❌ GitHub repository creation failed:', error.message);
            
            // Analyze the error
            if (error.message.includes('422')) {
                console.log('   📋 This is a 422 error - likely repository name conflict or validation issue');
                console.log('   💡 The enhanced error handling should provide more details in production');
            } else if (error.message.includes('401')) {
                console.log('   📋 This is a 401 error - authentication issue');
                console.log('   💡 Check your GITHUB_TOKEN environment variable');
            } else if (error.message.includes('403')) {
                console.log('   📋 This is a 403 error - permission issue');
                console.log('   💡 Check your GitHub token permissions');
            }
        }
    } else {
        console.log('3. Skipping GitHub repository creation test (config invalid)');
    }
    
    console.log('\n=== TEST SUMMARY ===');
    console.log('The following fixes have been implemented:');
    console.log('1. ✅ Fixed WhatsApp message parameter order in editAndRedeployWebsite()');
    console.log('2. ✅ Added comprehensive debugging to GitHub repository creation');
    console.log('3. ✅ Added automatic retry with timestamp suffix for repository name conflicts');
    console.log('4. ✅ Enhanced error reporting for 422 GitHub API errors');
    console.log('');
    console.log('Next steps:');
    console.log('- Deploy these changes to test with real WhatsApp messages');
    console.log('- Monitor logs for the enhanced debugging information');
    console.log('- The system should now handle repository name conflicts gracefully');
}

// Run the test
testGitHubIntegration().catch(console.error);