#!/usr/bin/env node

/**
 * Simple test for AI description generation
 * This tests the Gemini-2.5-flash description optimization without GitHub API calls
 */

import dotenv from 'dotenv';
import { GoogleGenAI } from '@google/genai';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

// Mock the generateOptimizedDescription function for testing
async function generateOptimizedDescription(originalDescription, genAI) {
    console.log(`=== GENERATING OPTIMIZED DESCRIPTION WITH GEMINI-2.0-FLASH ===`);
    
    try {
        // Use Gemini-2.0-flash for faster, cost-effective description generation
        const generationConfig = {
            temperature: 0.7,
            topP: 0.8,
            maxOutputTokens: 500, // Increased to allow proper response generation
        };

        const prompt = `Optimize this description to under 300 characters for GitHub:

"${originalDescription}"

Make it concise, professional, and engaging. Focus on what the website does. Return only the optimized description.`;

        console.log(`Sending prompt to Gemini-2.0-flash...`);
        const result = await genAI.models.generateContent({
            model: "gemini-2.0-flash",
            contents: prompt,
            config: generationConfig
        });
        
        // Extract text from the actual response structure
        let optimizedDescription = '';
        if (result.candidates && result.candidates[0] && result.candidates[0].content && result.candidates[0].content.parts) {
            optimizedDescription = result.candidates[0].content.parts[0].text.trim();
        } else if (result.text) {
            optimizedDescription = result.text.trim();
        } else {
            throw new Error('No text content found in response');
        }
        
        // Validate length
        if (optimizedDescription.length > 300) {
            console.log(`⚠️  AI description still too long (${optimizedDescription.length} chars), truncating...`);
            return optimizedDescription.substring(0, 297) + '...';
        }
        
        console.log(`✅ Generated optimized description: "${optimizedDescription}"`);
        console.log(`Length: ${optimizedDescription.length} characters`);
        
        return optimizedDescription;
        
    } catch (error) {
        console.error('Error generating optimized description:', error);
        throw error;
    }
}

async function testDescriptionGeneration() {
    console.log('=== TESTING AI DESCRIPTION GENERATION ===');
    
    // Check for Google API key
    if (!process.env.GOOGLE_API_KEY) {
        console.error('❌ GOOGLE_API_KEY environment variable is required');
        console.log('This test requires a Google API key to test Gemini-2.5-flash');
        process.exit(1);
    }
    
    // Initialize Google AI client
    const genAI = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY });
    
    // Test with the problematic description from the log
    const longDescription = "Make a marketing website for joyful, sorry, joy filled printables, which is a, I want a marketing site which is going to let me sell resources for teachers and in that they should, or for parents, excuse me, parents should be able to buy things for their children which will be educational, like toys and printable games and activities, all those sorts of things. Make the site fun, filled with colour and pop and it should really appeal to someone with young children.";
    
    console.log('\n=== TEST CASE: Long Description Optimization ===');
    console.log(`Original description length: ${longDescription.length} characters`);
    console.log(`Original: "${longDescription}"`);
    
    try {
        const optimizedDescription = await generateOptimizedDescription(longDescription, genAI);
        
        console.log('\n✅ DESCRIPTION OPTIMIZATION SUCCESSFUL!');
        console.log(`Optimized: "${optimizedDescription}"`);
        console.log(`New length: ${optimizedDescription.length} characters`);
        console.log(`Reduction: ${longDescription.length - optimizedDescription.length} characters`);
        
        if (optimizedDescription.length <= 300) {
            console.log('✅ Description meets GitHub requirements (≤300 chars)');
        } else {
            console.log('❌ Description still too long');
        }
        
    } catch (error) {
        console.error('\n❌ DESCRIPTION OPTIMIZATION FAILED:');
        console.error('Error:', error.message);
        
        // Test fallback truncation
        console.log('\n=== TESTING FALLBACK TRUNCATION ===');
        const fallbackDescription = longDescription.substring(0, 297) + '...';
        console.log(`Fallback description: "${fallbackDescription}"`);
        console.log(`Fallback length: ${fallbackDescription.length} characters`);
    }
}

// Run the test
testDescriptionGeneration().catch(console.error);