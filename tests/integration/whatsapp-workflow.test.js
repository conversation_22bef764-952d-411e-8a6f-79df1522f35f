/**
 * Integration tests for complete WhatsApp workflows
 * These tests use real dependencies and test end-to-end functionality
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { handleWhatsAppWebhook, processIncomingMessage } from '../../api/whatsapp.ts'

// Only run integration tests if explicitly enabled
const INTEGRATION_TESTS_ENABLED = process.env.RUN_INTEGRATION_TESTS === 'true'

describe.skipIf(!INTEGRATION_TESTS_ENABLED)('WhatsApp Integration Workflows', () => {
  let testClients

  beforeAll(async () => {
    // Setup real clients for integration testing
    // These would use test/sandbox environments
    testClients = {
      // Note: In real integration tests, these would be configured
      // to use sandbox/test environments, not production
      twilio: null, // Would initialize with test credentials
      genAI: null,  // Would use test API key with limited quota
      supabase: null // Would use test database
    }
    
    // Skip if required environment variables are not set
    const requiredEnvVars = [
      'INTEGRATION_GOOGLE_AI_API_KEY',
      'INTEGRATION_TWILIO_ACCOUNT_SID',
      'INTEGRATION_SUPABASE_URL'
    ]
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
    if (missingVars.length > 0) {
      console.warn(`Skipping integration tests - missing env vars: ${missingVars.join(', ')}`)
      return
    }
  })

  afterAll(async () => {
    // Cleanup any test data created during integration tests
    if (testClients?.supabase) {
      // Clean up test records
      await testClients.supabase
        .from('user_messages')
        .delete()
        .like('phone_number', '+test%')
    }
  })

  beforeEach(() => {
    // Skip individual tests if clients aren't configured
    if (!testClients || !testClients.twilio) {
      console.warn('Skipping test - integration clients not configured')
      return
    }
  })

  describe('End-to-End Website Creation', () => {
    it.skip('should create a complete website from WhatsApp message', async () => {
      // This test would:
      // 1. Send a real WhatsApp message (to test number)
      // 2. Process through the entire pipeline
      // 3. Generate actual HTML with AI
      // 4. Create real GitHub repository (in test org)
      // 5. Deploy to Vercel (test project)
      // 6. Verify all URLs are accessible
      // 7. Clean up created resources
      
      const testMessage = 'Create a simple portfolio website for John Doe, a web developer'
      const testPhoneNumber = '+test1234567890'
      
      const result = await processIncomingMessage(
        testMessage,
        testPhoneNumber,
        testClients
      )
      
      expect(result.success).toBe(true)
      expect(result.intent).toBe('new_site')
      expect(result.githubUrl).toMatch(/^https:\/\/github\.com\//)
      expect(result.vercelUrl).toMatch(/^https:\/\/.*\.vercel\.app$/)
      
      // Verify the created website is accessible
      const response = await fetch(result.vercelUrl)
      expect(response.ok).toBe(true)
      
      const html = await response.text()
      expect(html).toContain('John Doe')
      expect(html).toContain('web developer')
    }, 60000) // 60 second timeout for full workflow

    it.skip('should handle AI service failures gracefully', async () => {
      // Test with invalid AI API key to simulate service failure
      const failingClients = {
        ...testClients,
        genAI: null // Simulate AI service unavailable
      }
      
      const result = await processIncomingMessage(
        'Create a website for testing AI failure',
        '+test1234567891',
        failingClients
      )
      
      // Should still succeed with fallback HTML
      expect(result.success).toBe(true)
      expect(result.usedFallback).toBe(true)
      expect(result.githubUrl).toBeDefined()
    })

    it.skip('should update existing websites correctly', async () => {
      // First create a website
      const createResult = await processIncomingMessage(
        'Create a portfolio website',
        '+test1234567892',
        testClients
      )
      
      expect(createResult.success).toBe(true)
      
      // Then update it
      const updateResult = await processIncomingMessage(
        'Add a contact form to my website',
        '+test1234567892',
        testClients
      )
      
      expect(updateResult.success).toBe(true)
      expect(updateResult.intent).toBe('update_site')
      expect(updateResult.updated).toBe(true)
      
      // Verify the update was applied
      const response = await fetch(updateResult.vercelUrl || createResult.vercelUrl)
      const html = await response.text()
      expect(html.toLowerCase()).toContain('contact')
    })
  })

  describe('Database Integration', () => {
    it.skip('should persist user messages correctly', async () => {
      const testMessage = 'Test message for database integration'
      const testPhoneNumber = '+test1234567893'
      
      await processIncomingMessage(testMessage, testPhoneNumber, testClients)
      
      // Verify message was saved to database
      const { data: messages } = await testClients.supabase
        .from('user_messages')
        .select('*')
        .eq('phone_number', testPhoneNumber)
        .order('created_at', { ascending: false })
        .limit(1)
      
      expect(messages).toHaveLength(1)
      expect(messages[0].message).toBe(testMessage)
      expect(messages[0].intent).toBe('new_site')
    })

    it.skip('should handle database connection failures', async () => {
      const failingClients = {
        ...testClients,
        supabase: null // Simulate database unavailable
      }
      
      const result = await processIncomingMessage(
        'Test database failure handling',
        '+test1234567894',
        failingClients
      )
      
      // Should still attempt to process the request
      // but may not persist data
      expect(result).toBeDefined()
    })
  })

  describe('External API Integration', () => {
    it.skip('should handle GitHub API rate limits', async () => {
      // This would test behavior when GitHub API rate limits are hit
      // In a real scenario, this might involve making many requests
      // or using a test account with low rate limits
      
      const result = await processIncomingMessage(
        'Create a website to test rate limiting',
        '+test1234567895',
        testClients
      )
      
      // Should handle rate limiting gracefully
      expect(result).toBeDefined()
    })

    it.skip('should handle Vercel deployment failures', async () => {
      // Test with invalid Vercel configuration
      const failingClients = {
        ...testClients,
        // Could modify Vercel client to use invalid token
      }
      
      const result = await processIncomingMessage(
        'Test Vercel deployment failure',
        '+test1234567896',
        failingClients
      )
      
      // Should succeed with GitHub but fail Vercel deployment
      expect(result.success).toBe(true)
      expect(result.githubUrl).toBeDefined()
      expect(result.vercelUrl).toBeUndefined()
    })
  })

  describe('Performance and Load Testing', () => {
    it.skip('should handle concurrent requests', async () => {
      const concurrentRequests = 5
      const promises = []
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          processIncomingMessage(
            `Concurrent test website ${i}`,
            `+test123456789${i}`,
            testClients
          )
        )
      }
      
      const results = await Promise.all(promises)
      
      // All requests should succeed
      results.forEach((result, index) => {
        expect(result.success).toBe(true)
        expect(result.githubUrl).toMatch(/concurrent-test-website-\d+/)
      })
    }, 120000) // 2 minute timeout for concurrent operations

    it.skip('should complete workflow within reasonable time', async () => {
      const startTime = Date.now()
      
      const result = await processIncomingMessage(
        'Performance test website',
        '+test1234567897',
        testClients
      )
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(result.success).toBe(true)
      expect(duration).toBeLessThan(45000) // Should complete within 45 seconds
    })
  })

  describe('Error Recovery and Resilience', () => {
    it.skip('should recover from partial failures', async () => {
      // Test scenario where some services succeed and others fail
      // This helps verify the system's resilience
      
      const result = await processIncomingMessage(
        'Test partial failure recovery',
        '+test1234567898',
        testClients
      )
      
      // Should provide meaningful feedback about what succeeded/failed
      expect(result).toBeDefined()
      expect(result.success).toBeDefined()
    })

    it.skip('should handle malformed webhook payloads', async () => {
      const malformedRequest = {
        method: 'POST',
        headers: new Map([['x-twilio-signature', 'test-signature']]),
        json: async () => ({ malformed: 'payload', missing: 'required fields' })
      }
      
      const response = await handleWhatsAppWebhook(malformedRequest, testClients)
      
      expect(response.status).toBe(400)
    })
  })
})

// Helper function to create test data
function createTestWebhookPayload(message, phoneNumber) {
  return {
    MessageSid: `SM${Date.now()}`,
    From: `whatsapp:${phoneNumber}`,
    To: 'whatsapp:+14155238886',
    Body: message,
    NumMedia: '0'
  }
}

// Helper function to cleanup test resources
async function cleanupTestResources(testClients, phoneNumbers) {
  if (!testClients?.supabase) return
  
  try {
    // Clean up test messages
    await testClients.supabase
      .from('user_messages')
      .delete()
      .in('phone_number', phoneNumbers)
    
    // Clean up test projects
    await testClients.supabase
      .from('project_metadata')
      .delete()
      .in('phone_number', phoneNumbers)
    
    // Clean up test website codes
    await testClients.supabase
      .from('website_codes')
      .delete()
      .in('phone_number', phoneNumbers)
  } catch (error) {
    console.warn('Error cleaning up test resources:', error.message)
  }
}